<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.quality.dao.TasteTaskDetailMapper">

    <select id="selectDetailList" resultType="com.hvisions.quality.dto.qc.taste.task.TaskDetailDTO">
        SELECT
            t1.id detailId,
            t1.group_no,
            t1.conclusion,
            t1.result,
            t2.id cupId,
            t2.cup_no,
            t2.cup_type_flag,
            t3.user_name tastePersonName
        FROM
            quality.hv_qm_taste_task_detail t1
                LEFT JOIN quality.hv_qm_taste_task_cup t2 ON t1.id = t2.task_detail_id
                LEFT JOIN framework.sys_user t3 ON t1.user_id= t3.id
        WHERE
            t1.taste_task_id =#{id}
        AND t1.taste_status>1
    </select>
    <select id="selectTasteList" resultType="com.hvisions.quality.dto.qc.taste.task.TasteDTO">
        SELECT
            t1.id,
            t1.group_no,
            t1.user_id tastePersonId,
            t1.taste_status,
            t1.result,
            t1.conclusion,
            t1.taste_start_time,
            t1.taste_end_time,
            t1.taste_task_id,
            t2.register_number,
            t2.inspection_number,
            t2.material_code,
            t2.material_name,
            t2.inspection_scene,
            t2.taste_method_name,
            t2.host_time,
            t6.uuid as uuId,
            t3.user_name tastePersonName,
            t8.barcode as sampleNumNew,
            t2.sample_code as sampleCode,
            MAX( CASE WHEN t9.attr_key = '样品名称' THEN t9.attr_value END) as extendName
        FROM
            quality.hv_qm_taste_task_detail t1
                LEFT JOIN quality.hv_qm_taste_task t2 ON t1.taste_task_id = t2.id
                LEFT JOIN quality.hv_qm_inspection t6 on t2.inspection_id=t6.id
                LEFT JOIN quality.hv_qm_inspection_sample t7 on t6.id=t7.inspection_id
                LEFT JOIN quality.hv_qm_inspection_sample_detail t8 on t7.id=t8.sample_id
                LEFT JOIN framework.sys_user t3 ON t1.user_id= t3.id
                LEFT JOIN hv_qm_inspection_register ir ON ir.inspection_uuid = t6.uuid
                left join quality.hv_qm_inspection_reg_rel_detail t9 on t9.register_id = ir.id
        WHERE
            t1.taste_status > 1
          <if test="registerNumber != null and registerNumber != ''">
              AND t2.register_number LIKE concat( '%', #{registerNumber}, '%' )
          </if>
          <if test="inspectionNumber != null and inspectionNumber != ''">
              AND t2.inspection_number LIKE concat( '%', #{inspectionNumber}, '%' )
          </if>
          <if test="materialCode != null and materialCode != ''">
            AND t2.material_code LIKE concat( '%', #{materialCode}, '%' )
          </if>
          <if test="taskCode != null and taskCode != ''">
            AND t2.task_code LIKE concat( '%', #{taskCode}, '%' )
          </if>
          <if test="sampleCode != null and sampleCode != ''">
              AND (t2.sample_code LIKE concat( '%', #{sampleCode}, '%' ) OR t8.barcode LIKE concat( '%', #{sampleCode}, '%' ))
          </if>
        <if test="materialName != null and materialName != ''">
            AND t2.material_name LIKE concat( '%', #{materialName}, '%' )
        </if>
        <if test="tasteStatus != null">
            AND t1.taste_status = #{tasteStatus}
        </if>
        <if test="tasteStatusList != null and tasteStatusList.size()>0">
            AND t1.taste_status IN
            <foreach collection="tasteStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="inspectionScene != null and inspectionScene != ''">
            AND t2.inspection_scene LIKE concat( '%', #{inspectionScene}, '%' )
        </if>
        <if test="result != null">
            AND t1.result = #{result}
        </if>
        <if test="hostTimeStart != null and hostTimeEnd != null">
            AND t2.host_time BETWEEN #{hostTimeStart} AND #{hostTimeEnd}
        </if>
        <if test="tasteTimeStart != null and tasteTimeEnd != null">
            AND t1.taste_start_time BETWEEN #{tasteTimeStart} AND #{tasteTimeEnd}
        </if>
        <if test="tastePersonName != null and tastePersonName != ''">
            AND t3.user_name LIKE concat( '%', #{tastePersonName}, '%' )
        </if>
        <if test="userId != null">
            AND (t1.user_id IS NULL OR t1.user_id=#{userId})
        </if>
        GROUP BY t1.id,t8.barcode
        ORDER BY t1.id DESC
    </select>
    <select id="selectNotTasteCount" resultType="java.lang.Integer">
        SELECT
            count( t2.id )
        FROM
            hv_qm_taste_task_cup t1
                LEFT JOIN hv_qm_taste_task_cup_detail t2 ON t1.id = t2.cup_id
        WHERE
            t1.task_detail_id = #{id}
          AND t2.conclusion IS NULL
    </select>
    <select id="selectTasteDetail" resultType="com.hvisions.quality.dto.qc.taste.task.TasteDetailDTO">
        SELECT
            t1.id,
            t1.conclusion,
            t1.result,
            t1.group_no,
            t2.material_code,
            t2.material_name,
            t2.register_number,
            t2.blind_fields,
            t2.inspection_number,
            t2.inspection_scene,
            t2.taste_method_name,
            t3.user_name tastePersonName
        FROM
            quality.hv_qm_taste_task_detail t1
                LEFT JOIN quality.hv_qm_taste_task t2 ON t1.taste_task_id = t2.id
                LEFT JOIN framework.sys_user t3 ON t1.user_id= t3.id
        WHERE
            t1.id =#{id}
    </select>
    <select id="selectGroupNoList" resultType="java.lang.Integer">
        SELECT
            group_no
        FROM
            hv_qm_taste_task_detail
        WHERE
                taste_task_id =(
                SELECT
                    taste_task_id
                FROM
                    hv_qm_taste_task_detail
                WHERE
                    id = #{tasteId}
            )
          AND taste_status &lt; 4
        <if test="userId != null">
            AND (user_id =#{userId} OR user_id IS NULL)
        </if>
    </select>
    <select id="selectHaveUserDetailList" resultType="com.hvisions.quality.dto.qc.taste.task.TaskDetailAddDTO">
        SELECT
            t1.id detailId,
            t1.user_id,
            t1.group_no,
            t2.user_name,
            t3.user_account
        FROM
            quality.hv_qm_taste_task_detail t1
                LEFT JOIN framework.sys_user t2 ON t1.user_id = t2.id
                LEFT JOIN framework.sys_user_login t3 ON t2.id = t3.user_id
        WHERE
            t1.taste_task_id = #{id}
    </select>
    <select id="selectAppTaskDetail" resultType="com.hvisions.quality.dto.qc.taste.task.TaskDetailAppDTO">
        SELECT
            t1.id detailId,
            t1.conclusion,
            t1.result,
            t2.user_name tastePersonName
        FROM
            quality.hv_qm_taste_task_detail t1
                LEFT JOIN framework.sys_user t2 ON t1.user_id = t2.id
        WHERE
            t1.taste_task_id = #{id}
          AND t1.taste_status > 1
        ORDER BY
            t1.group_no
    </select>
    <select id="selectItemConclusionList"
            resultType="com.hvisions.quality.dto.qc.taste.task.ItemConclusionDTO">
        SELECT
            tt3.id id,
            tt3.conclusion_code code,
            tt3.conclusion_name name,
            tt1.check_item_code checkItemCode
        FROM
            hv_qm_check_item tt1
                JOIN hv_qm_taste_item_conclusion_relation tt2 ON tt1.id = tt2.item_id
                JOIN hv_qm_taste_conclusion tt3 ON tt2.conclusion_id = tt3.id
        WHERE
                tt1.check_item_code IN (
                SELECT DISTINCT
                    t3.item_code
                FROM
                    hv_qm_taste_task_detail t1
                        JOIN hv_qm_taste_task_cup t2 ON t1.id = t2.task_detail_id
                        JOIN hv_qm_taste_task_cup_detail t3 ON t2.id = t3.cup_id
                WHERE
                    t1.id = #{id})
    </select>
    <select id="selectNotTasteGroupNo" resultType="java.lang.String">
        SELECT group_no from hv_qm_taste_task_detail where create_time	>=#{today} and group_no = #{groupNo} AND conclusion IS NULL LIMIT 1;
    </select>
    <select id="selectTaskDetailList" resultType="com.hvisions.quality.entity.qc.taste.task.TasteTask">
        SELECT t2.* from hv_qm_taste_task_detail t1 join hv_qm_taste_task t2 on  t2.id = t1.taste_task_id where  t1.id = #{id}
    </select>
    <update id="updateStatusById" >
        UPDATE hv_qm_taste_task
        <trim prefix="set" suffixOverrides=",">
            <if test="status!=null"> task_status = #{status}</if>
        </trim>
        where id=#{id}
    </update>

    <select id="selectInspectionDetailList" resultType="java.lang.Integer">
        SELECT d.id from hv_qm_inspection i
        LEFT JOIN hv_qm_inspection_detail  d on d.inspection_id = i.id
        WHERE d.check_type_code LIKE "%PP" and i.inspection_num=#{inspectionNumber}
    </select>

    <select id="selectNotTasteGroupNoNew" resultType="java.lang.String">
        SELECT
            group_no
        FROM
            hv_qm_relation
        WHERE
            create_time >= #{today} and group_no = #{groupNo} LIMIT 1;
    </select>

    <select id="selectTastePersonName" resultType="java.lang.String">
        SELECT
        GROUP_CONCAT(DISTINCT t3.user_name) tastePersonName
        FROM
        quality.hv_qm_taste_task_detail t1
        LEFT JOIN quality.hv_qm_taste_task_cup t2 ON t1.id = t2.task_detail_id
        LEFT JOIN framework.sys_user t3 ON t1.user_id = t3.id
        LEFT JOIN quality.hv_qm_taste_task tt ON tt.id = t1.taste_task_id
        LEFT JOIN quality.hv_qm_inspection ii ON ii.id = tt.inspection_id
        WHERE
            ii.id = #{inspectionId}
          AND t1.taste_status>1
    </select>

    <select id="selectConclusion" resultType="java.lang.String">
        SELECT
            tt.judge_conclusion
        FROM
            hv_qm_taste_task tt
        LEFT JOIN quality.hv_qm_inspection ii ON ii.inspection_num = tt.inspection_number
        WHERE
           ii.id = #{inspectionId}
    </select>
</mapper>