<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.quality.dao.LocationMapper">
    <select id="query" resultType="com.hvisions.quality.dto.qc.location.LocationDTO">
        select hql.* , suc.user_name as creator_name , suu.user_name as updater_name
        from quality.hv_qm_location hql
        left join framework.sys_user suc on hql.creator_id = suc.id
        left join framework.sys_user suu on hql.updater_id = suu.id
        <where>
            <if test="query.locationCode != null and query.locationCode != ''">
                hql.location_code like concat('%',#{query.locationCode},'%')
            </if>
            <if test="query.locationName != null and query.locationName != ''">
                and hql.location_name like concat('%',#{query.locationName},'%')
            </if>
            <if test="query.enableFlag != null">
                and hql.enable_flag = #{query.enableFlag}
            </if>
            <if test="query.locationBusinessType != null and query.locationBusinessType != ''">
                and hql.location_business_type like concat('%',#{query.locationBusinessType},'%')
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (
                    hql.location_code like concat('%',#{query.keyword},'%')
                    or
                    hql.location_name like concat('%',#{query.keyword},'%')
                )
            </if>
        </where>
    </select>
</mapper>