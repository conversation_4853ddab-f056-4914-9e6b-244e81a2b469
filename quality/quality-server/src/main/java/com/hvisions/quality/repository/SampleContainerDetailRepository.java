package com.hvisions.quality.repository;

import com.hvisions.quality.entity.qc.container.SampleContainerDetail;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * 样品容器明细仓库
 */
public interface SampleContainerDetailRepository extends JpaRepository<SampleContainerDetail, Integer> {
    /**
     * 根据ID列表删除
     *
     * @param idList ID列表
     */
    void deleteByIdIn(List<Integer> idList);

    /**
     * 查询容器明细信息
     *
     * @param containerId 样品容器ID
     * @param barcode     装载条码
     * @return 容器明细信息
     */
    SampleContainerDetail findFirstBySampleContainerIdAndLoadBarCode(Integer containerId, String barcode);
}
