package com.hvisions.quality.service.imp;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.quality.constant.TasteConstant;
import com.hvisions.quality.dao.*;
import com.hvisions.quality.dao.wine.ReceiveWineDetailMapper;
import com.hvisions.quality.dao.wine.ReceiveWineMapper;
import com.hvisions.quality.dto.qc.taste.task.*;
import com.hvisions.quality.entity.qc.taste.task.TasteTask;
import com.hvisions.quality.entity.qc.taste.task.TasteTaskCupDetail;
import com.hvisions.quality.entity.qc.taste.task.TasteTaskDetail;
import com.hvisions.quality.entity.wine.ReceiveWine;
import com.hvisions.quality.entity.wine.ReceiveWineDetail;
import com.hvisions.quality.enums.qc.OperateTypeEnum;
import com.hvisions.quality.enums.qc.TasteMethodEnum;
import com.hvisions.quality.enums.qc.TasteStatusEnum;
import com.hvisions.quality.enums.qc.TasteTaskStatusEnum;
import com.hvisions.quality.excel.TasteResultImportExcel;
import com.hvisions.quality.excel.TasteResultReportExcel;
import com.hvisions.quality.repository.TasteTaskCupDetailRepository;
import com.hvisions.quality.repository.TasteTaskDetailRepository;
import com.hvisions.quality.repository.TasteTaskRepository;
import com.hvisions.quality.service.AsyncService;
import com.hvisions.quality.service.TasteCommonService;
import com.hvisions.quality.service.TasteService;
import com.hvisions.quality.service.TasteTaskService;
import com.hvisions.quality.util.FileNameUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TasteServiceImpl implements TasteService {
    @Resource
    private TasteTaskDetailMapper tasteTaskDetailMapper;
    @Resource
    private TasteTaskCupDetailMapper tasteTaskCupDetailMapper;
    @Resource
    private TasteTaskCupDetailRepository tasteTaskCupDetailRepository;
    @Resource
    private TasteTaskDetailRepository tasteTaskDetailRepository;
    @Resource
    private TasteTaskRepository tasteTaskRepository;
    @Resource
    private TasteTaskMapper tasteTaskMapper;
    @Resource
    private TasteTaskCupMapper tasteTaskCupMapper;
    @Resource
    private TasteConclusionMapper tasteConclusionMapper;
    @Resource
    private TasteCommonService tasteCommonService;
    @Resource
    private AsyncService asyncService;
    @Resource
    private TasteTaskService tasteTaskService;

    @Override
    public Page<TasteDTO> queryTastePage(TasteQuery query,Integer userId) {
        userId=tasteCommonService.getUserId();
        query.setUserId(userId);
        return PageHelperUtil.getPage(tasteTaskDetailMapper::selectTasteList, query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editTasteDetail(TasteDetailDTO dto) {

        TasteTaskDetail tasteTaskDetail = tasteTaskDetailMapper.selectById(dto.getId());
        if (tasteTaskDetail==null){
            throw new BaseKnownException("品评执行任务不存在");
        }
        if (tasteTaskDetail.getTasteStatus()>TasteStatusEnum.TASTE.getCode()){
            throw new BaseKnownException("品评已完成或已终止,无法品评");
        }
        TasteTask tasteTask = tasteTaskMapper.selectById(tasteTaskDetail.getTasteTaskId());
        if (tasteTask==null){
            throw new BaseKnownException("品评任务不存在");
        }
        //根据组号查详情
        /*TasteTaskDetail taskDetail = tasteTaskDetailMapper.selectOne(Wrappers.<TasteTaskDetail>lambdaQuery()
                .eq(TasteTaskDetail::getTasteTaskId, tasteTask.getId())
                .eq(TasteTaskDetail::getGroupNo, dto.getGroupNo())
                .last("limit 1"));
        if (taskDetail != null) {
            tasteTaskDetail=taskDetail;
        }*/

        //保存品评详情
        for (TaskCupItemDTO taskCupItemDTO : dto.getCupItemList()) {
            TasteTaskCupDetail tasteTaskCupDetail = tasteTaskCupDetailMapper.selectOne(Wrappers.<TasteTaskCupDetail>lambdaQuery()
                    .eq(TasteTaskCupDetail::getCupId, taskCupItemDTO.getCupId())
                    .eq(TasteTaskCupDetail::getItemCode, taskCupItemDTO.getItemCode())
                    .last("limit 1"));
            if (tasteTaskCupDetail!=null){
                tasteTaskCupDetail.setConclusion(taskCupItemDTO.getConclusion());
                tasteTaskCupDetailMapper.updateById(tasteTaskCupDetail);
            }
        }
        //修改品评信息
        tasteTaskDetail.setResult(dto.getResult());

        Date date = new Date();
        if (OperateTypeEnum.HOST.getCode().equals(dto.getType())){
            //提交
            long notTasteCount = dto.getCupItemList().stream().filter(item -> StrUtil.isBlank(item.getConclusion())).count();
            //Integer notTasteCount=tasteTaskDetailMapper.selectNotTasteCount(dto.getId());
            if (notTasteCount>0){
                throw new BaseKnownException("没有品评完成,不能提交");
            }
            if (tasteTaskDetail.getTasteStartTime()==null){
                tasteTaskDetail.setTasteStartTime(date);
            }
            tasteTaskDetail.setTasteEndTime(date);
            tasteTaskDetail.setTasteStatus(TasteStatusEnum.TASTE_COMPLETE.getCode());
        }else {
            //保存
            if (tasteTaskDetail.getTasteStartTime()==null){
                tasteTaskDetail.setTasteStartTime(date);
                tasteTaskDetail.setTasteStatus(TasteStatusEnum.TASTE.getCode());
            }
        }

        //判断是否是品曲，是则需要将结果进行汇总分组 && 提交
        if(tasteTask.getTasteMethodName().equals(TasteMethodEnum.TASTE_METHOD_PQ.getName()) && OperateTypeEnum.HOST.getCode().equals(dto.getType())){
            dto.setConclusion(null);
            Map<String, Long> collect = dto.getCupItemList().stream().filter(item->item.getItemCode().equals(TasteMethodEnum.TASTE_METHOD.getCode())).collect(Collectors.groupingBy(TaskCupItemDTO::getConclusion, Collectors.counting()));
            //算总条数
            long count = dto.getCupItemList().stream().filter(item->item.getItemCode().equals(TasteMethodEnum.TASTE_METHOD.getCode())).count();

            StringBuffer huang=new StringBuffer();
            StringBuffer bai=new StringBuffer();
            StringBuffer hei=new StringBuffer();
            StringBuffer qt=new StringBuffer();

            collect.forEach((k,v)->{
                //计算百分比
                String num = calculateProportion(v, count);
                if("黄".equals(k)){
                    huang.append(k +"曲率"+ num + "%,");
                }else if("白".equals(k)){
                    bai.append(k +"曲率"+ num + "%,");
                }else if("黑".equals(k)){
                    hei.append(k +"曲率"+ num + "%,");
                }else {
                    qt.append(k +"曲率"+ num + "%,");
                }
            });

            if(huang.length()==0){
                huang.append("黄曲率0%,");
            }

            if(bai.length()==0){
                bai.append("白曲率0%,");
            }

            if(hei.length()==0){
                hei.append("黑曲率0%,");
            }


            dto.setConclusion(huang.toString()+bai.toString()+hei.toString()+qt.toString());

            dto.setConclusion(dto.getConclusion().substring(0,dto.getConclusion().length()-1));
        }

        tasteTaskDetail.setConclusion(dto.getConclusion());
        tasteTaskDetailMapper.updateById(tasteTaskDetail);

        //如果所有品评详情都完成，修改品评任务的状态为待综合判定
        List<TasteTaskDetail> unCompleteTaskDetailList = tasteTaskDetailMapper.selectList(Wrappers.<TasteTaskDetail>lambdaQuery()
                        .select(TasteTaskDetail::getId)
                .eq(TasteTaskDetail::getTasteTaskId, tasteTaskDetail.getTasteTaskId())
                .lt(TasteTaskDetail::getTasteStatus, TasteStatusEnum.TASTE_COMPLETE.getCode()));

        //默认品评中
        Integer taskStatus=TasteTaskStatusEnum.TASTE.getCode();
        if (OperateTypeEnum.HOST.getCode().equals(dto.getType())
                && CollectionUtils.isEmpty(unCompleteTaskDetailList)){
            taskStatus=TasteTaskStatusEnum.WAIT_JUDGEMENT.getCode();
        }
        tasteTask.setTaskStatus(taskStatus);
        tasteTaskMapper.updateById(tasteTask);


        //都提交完，则需要进行自动综合判定 && 是否是品曲、半成品酒品评、组合酒品评、轮次酒品评
        if(taskStatus.equals(TasteTaskStatusEnum.WAIT_JUDGEMENT.getCode())){
            if(tasteTask.getTasteMethodName().equals(TasteMethodEnum.TASTE_METHOD_PQ.getName())){
                comprehensiveJudgmentPQ(tasteTask.getId());
            }

            if(tasteTask.getTasteMethodName().equals(TasteMethodEnum.TASTE_METHOD_BCP.getName()) || tasteTask.getTasteMethodName().equals(TasteMethodEnum.TASTE_METHOD_ZH.getName()) || tasteTask.getTasteMethodName().equals(TasteMethodEnum.TASTE_METHOD_LC.getName())){
                ComprehensiveJudgmentDTO judgmentDTO=new ComprehensiveJudgmentDTO();
                judgmentDTO.setId(tasteTask.getId());
                judgmentDTO.setResult(3);
                judgmentDTO.setType(2);
                //新酒所有品评都可复评
                judgmentDTO.setIsAgainTaste(1);
                tasteTaskService.comprehensiveJudgment(judgmentDTO);
            }
        }

    }

    public String calculateProportion(long nun,long sum){
        BigDecimal bgNum = BigDecimal.valueOf(nun);
        BigDecimal bgSum = new BigDecimal(sum);
        // 除法保留三位位小数
        BigDecimal result = bgNum.divide(bgSum, 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));

        return result.setScale(0,RoundingMode.HALF_UP).toString();
    }

    /**
     * 品曲-自动综合判定
     * @param tasteTaskId 品评任务id
     */
    public void comprehensiveJudgmentPQ(Integer tasteTaskId){
        ComprehensiveJudgmentDTO judgmentDTO=new ComprehensiveJudgmentDTO();

        List<TaskDetailDTO> dtoList = tasteTaskService.getTaskDetail(tasteTaskId);
        List<TaskCupItemDTO> cupItemList=new ArrayList<>();
        dtoList.forEach(v2->{
            v2.getCupItemList().forEach(item->{
                item.setTastePersonName(v2.getTastePersonName());
            });
            cupItemList.addAll(v2.getCupItemList());
        });

        judgmentDTO.setConclusion(null);

        Map<String, Long> collect = cupItemList.stream()
                .peek(item -> {
                    if (item.getConclusion() == null) {
                        throw new BaseKnownException(item.getTastePersonName()+"未提交品评结果");
                    }
                })
                .filter(item->item.getItemCode().equals(TasteMethodEnum.TASTE_METHOD.getCode()))
                .collect(Collectors.groupingBy(TaskCupItemDTO::getConclusion, Collectors.counting()));
        //算总条数
        long count = cupItemList.stream().filter(item->item.getItemCode().equals(TasteMethodEnum.TASTE_METHOD.getCode())).count();

        StringBuffer huang=new StringBuffer();
        StringBuffer bai=new StringBuffer();
        StringBuffer hei=new StringBuffer();
        StringBuffer qt=new StringBuffer();

        collect.forEach((k,v2)->{
            //计算百分比
            String num = calculateProportion(v2, count);
            if("黄".equals(k)){
                huang.append(k +"曲率"+ num + "%,");
            }else if("白".equals(k)){
                bai.append(k +"曲率"+ num + "%,");
            }else if("黑".equals(k)){
                hei.append(k +"曲率"+ num + "%,");
            }else {
                qt.append(k +"曲率"+ num + "%,");
            }
        });

        if(huang.length()==0){
            huang.append("黄曲率0%,");
        }

        if(bai.length()==0){
            bai.append("白曲率0%,");
        }

        if(hei.length()==0){
            hei.append("黑曲率0%,");
        }

        judgmentDTO.setConclusion(huang.toString()+bai.toString()+hei.toString()+qt.toString());

        judgmentDTO.setConclusion(judgmentDTO.getConclusion().substring(0,judgmentDTO.getConclusion().length()-1));
        judgmentDTO.setId(tasteTaskId);
        judgmentDTO.setResult(3);
        judgmentDTO.setType(2);
        judgmentDTO.setIsAgainTaste(0);
        tasteTaskService.comprehensiveJudgment(judgmentDTO);
    }

    @Override
    public TasteDetailDTO getDetail(Integer id) {
        TasteDetailDTO result=tasteTaskDetailMapper.selectTasteDetail(id);
        if (result!=null){
            List<TaskCupItemDTO> cupItemList =tasteTaskCupDetailMapper.selectCupDetailList(id, TasteConstant.MENU_TYPE);
            result.setCupItemList(cupItemList);
        }
        return result;
    }

    @Override
    public void stopTaste(Integer id) {
        TasteTaskDetail tasteTaskDetail = tasteTaskDetailMapper.selectById(id);
        if (tasteTaskDetail!=null){
            if (!TasteStatusEnum.WAIT_TASTE.getCode().equals(tasteTaskDetail.getTasteStatus())){
                throw new BaseKnownException("此品评任务不可被终止！");
            }
            tasteTaskDetail.setTasteStatus(TasteStatusEnum.TASTE_STOP.getCode());
            tasteTaskDetailRepository.save(tasteTaskDetail);
            //查询所有品评详情的状态
            List<TasteTaskDetail> tasteTaskDetailList = tasteTaskDetailMapper.selectList(Wrappers.<TasteTaskDetail>lambdaQuery()
                    .select(TasteTaskDetail::getTasteStatus)
                    .eq(TasteTaskDetail::getTasteTaskId, tasteTaskDetail.getTasteTaskId()));
            //默认品评任务状态为已终止
            Integer taskStatus=TasteTaskStatusEnum.HAVE_STOP.getCode();
            boolean completeFlag=false;
            for (TasteTaskDetail taskDetail : tasteTaskDetailList) {
                Integer tasteStatus = taskDetail.getTasteStatus();
                if (TasteStatusEnum.TASTE.getCode().equals(tasteStatus)||TasteStatusEnum.WAIT_TASTE.getCode().equals(tasteStatus)){
                    //如果存在待品评和品评中状态详情
                    taskStatus=TasteTaskStatusEnum.TASTE.getCode();
                    break;
                }
                if (TasteStatusEnum.TASTE_COMPLETE.getCode().equals(tasteStatus)){
                    //存在品评完成状态的详情
                    completeFlag=true;
                }
            }

            if (!TasteTaskStatusEnum.TASTE.getCode().equals(taskStatus)){
                if (completeFlag){
                    taskStatus=TasteTaskStatusEnum.WAIT_JUDGEMENT.getCode();
                }
            }
            //更新任务状态
            TasteTask tasteTask = tasteTaskMapper.selectById(tasteTaskDetail.getTasteTaskId());
            tasteTask.setTaskStatus(taskStatus);
            tasteTaskRepository.save(tasteTask);
            if (TasteTaskStatusEnum.HAVE_STOP.getCode().equals(taskStatus)){
                UserInfoDTO userInfo = tasteCommonService.getUserInfo();
                asyncService.giveResultToInspection(tasteTask,userInfo);
            }
        }
    }

    @Override
    public List<Integer> getGroupNoList(Integer tasteId,Integer userId) {
        userId= tasteCommonService.getUserId();
        return tasteTaskDetailMapper.selectGroupNoList(tasteId,userId);
    }

    @Override
    public void verifyGroupNo(Integer tasteId, Integer groupNo, Integer userId) {
        userId= tasteCommonService.getUserId();
        TasteTaskDetail tasteTaskDetail = tasteTaskDetailMapper.selectById(tasteId);
        if (tasteTaskDetail==null){
            throw new BaseKnownException("品评执行任务不存在");
        }
        TasteTaskDetail taskDetail = tasteTaskDetailMapper.selectOne(Wrappers.<TasteTaskDetail>lambdaQuery()
                .eq(TasteTaskDetail::getTasteTaskId, tasteTaskDetail.getTasteTaskId())
                .eq(TasteTaskDetail::getGroupNo, groupNo)
                .last("limit 1"));
        if (taskDetail==null){
            throw new BaseKnownException("组号不存在");
        }
        Long tasteCount = tasteTaskDetailMapper.selectCount(Wrappers.<TasteTaskDetail>lambdaQuery()
                .eq(TasteTaskDetail::getTasteTaskId, tasteTaskDetail.getTasteTaskId())
                .eq(TasteTaskDetail::getUserId, userId));
        if (tasteCount>0){
            throw new BaseKnownException("该任务你只能品评一次");
        }
        taskDetail.setUserId(userId);
        tasteTaskDetailRepository.save(taskDetail);

    }

    @Override
    public TasteAppDetailDTO getAppDetail(Integer id) {
        TasteDetailDTO dto = tasteTaskDetailMapper.selectTasteDetail(id);
        TasteAppDetailDTO result = DtoMapper.convert(dto, TasteAppDetailDTO.class);
        if (result!=null){
            List<CupAppDTO> cupList = tasteTaskCupMapper.selectCupAppList(result.getId());
            //查询品评项的结论
            List<ItemConclusionDTO> itemConclusionDTOList=tasteTaskDetailMapper.selectItemConclusionList(id);
            Map<String, List<ItemConclusionDTO>> itemCodeConclusionMap = itemConclusionDTOList.stream().collect(Collectors.groupingBy(ItemConclusionDTO::getCheckItemCode));
            for (CupAppDTO cupAppDTO : cupList) {
                List<CupDetailAppDTO> cupDetailList = tasteTaskCupDetailMapper.selectCupDetailAppList(cupAppDTO.getCupId());
                for (CupDetailAppDTO cupDetailAppDTO : cupDetailList) {
                    //设置品评项的结论下拉列表
                    List<ItemConclusionDTO> list = itemCodeConclusionMap.get(cupDetailAppDTO.getItemCode());
                    if (list==null){
                        list=new ArrayList<>();
                    }
                    List<PullDownDTO> pullDownDTOList = list.stream().map(item -> {
                        PullDownDTO pullDownDTO = new PullDownDTO();
                        pullDownDTO.setId(item.getId());
                        pullDownDTO.setName(item.getName());
                        pullDownDTO.setCode(item.getCode());
                        return pullDownDTO;
                    }).collect(Collectors.toList());
                    cupDetailAppDTO.setConclusionList(pullDownDTOList);
                }
                cupAppDTO.setCupItemList(cupDetailList);
            }
            result.setCupList(cupList);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAppTasteDetail(TasteAppDetailDTO dto) {
        //校验品评任务详情
        TasteTaskDetail tasteTaskDetail = tasteTaskDetailMapper.selectById(dto.getId());
        if (tasteTaskDetail==null){
            throw new BaseKnownException("品评执行任务不存在");
        }
        if (tasteTaskDetail.getTasteStatus()>TasteStatusEnum.TASTE.getCode()){
            throw new BaseKnownException("品评已完成或已终止,无法品评");
        }
        TasteTask tasteTask = tasteTaskMapper.selectById(tasteTaskDetail.getTasteTaskId());
        if (tasteTask==null){
            throw new BaseKnownException("品评任务不存在");
        }
        //根据组号查详情
        //TODO 好像没啥意义不知道哪位写的导致永远只能更新一位品评人的结果 2024/09/19
        /*TasteTaskDetail taskDetail = tasteTaskDetailMapper.selectOne(Wrappers.<TasteTaskDetail>lambdaQuery()
                .eq(TasteTaskDetail::getTasteTaskId, tasteTask.getId())
                .eq(TasteTaskDetail::getGroupNo, dto.getGroupNo())
                .last("limit 1"));
        if (taskDetail != null) {
            //tasteTaskDetail=taskDetail;
        }*/
        //更新品评杯子信息
        tasteCommonService.saveTasteCupDetail(dto.getCupList());
        //更新品评任务详情信息
        if(tasteTask.getTasteMethodName().equals(TasteMethodEnum.TASTE_METHOD_PQ.getName())){
            tasteCommonService.updateTasteTaskDetail(dto, tasteTaskDetail,tasteTask.getTasteMethodName());
        }else {
            tasteCommonService.updateTasteTaskDetail(dto, tasteTaskDetail);
        }
        //更新品评任务状态
        tasteCommonService.updateTasteTaskStatus(dto, tasteTaskDetail, tasteTask);
    }

    @Override
    public Integer getUserId() {
        return tasteCommonService.getUserId();
    }

    @Override
    public List<PullDownDTO> getConclusionListByItemCode(String itemCode) {
        return tasteConclusionMapper.selectConclusionListByItemCode(itemCode);
    }

    @Override
    public Page<TasteResultReportDTO> queryTasteResultReport(TasteResultReportQuery query) {
        //若样品条码 班级 组号 工厂 综合判定结果 综合判定结论 这几个参数有任意一个值，则不走以下条件
        if ( query.getHostStartTime() != null || query.getHostEndTime()!=null
                || query.getTasteStartTime()!=null || query.getTasteEndTime()!=null
                || query.getCreateStartTime()!=null || query.getCreateEndTime()!=null
                || query.getBarcode()!=null || query.getClassName()!=null || query.getGroupNo()!=null
                || query.getFactory()!=null || query.getInspectionStatus()!=null || query.getJudgeConclusion()!=null){
            return PageHelperUtil.getPage(tasteTaskMapper::queryTasteResultReport, query);
        }

        if (query.getTasteStartTime()==null && query.getCreateEndTime() == null &&  query.getHostStartTime() == null){
            query.setTasteStartTime(LocalDate.now()+" 00:00:00");
        }
        if (query.getTasteEndTime()==null && query.getCreateEndTime() == null &&  query.getHostStartTime() == null){
            query.setTasteEndTime(LocalDate.now()+" 23:59:59");
        }

        Page<TasteResultReportDTO> page =  PageHelperUtil.getPage(tasteTaskMapper::queryTasteResultReport, query);
        return page;
    }

    @Override
    public ExcelExportDto exportTasteResultReport(TasteResultReportQuery query) throws IOException, IllegalAccessException {
        if (query.getTasteStartTime()==null && query.getCreateEndTime() == null &&  query.getHostStartTime() == null){
            query.setTasteStartTime(LocalDate.now()+" 00:00:00");
        }
        if (query.getTasteEndTime()==null && query.getCreateEndTime() == null &&  query.getHostStartTime() == null){
            query.setTasteEndTime(LocalDate.now()+" 23:59:59");
        }

        List<TasteResultReportDTO> list = tasteTaskMapper.queryTasteResultReport(query);

        List<TasteResultReportExcel> excelList = DtoMapper.convertList(list, TasteResultReportExcel.class);

        return EasyExcelUtil.getExcel(
                excelList,
                TasteResultReportExcel.class,
                FileNameUtil.createExcelFullFileName("品评结果"));
    }

    /*
     * @Description: 修改品评报表
     *
     * <AUTHOR>
     * @param taskDTO:
     * @return java.lang.Integer
     */
    @Override
    public Integer updateTasteResultReportQuery(TasteTaskDTO taskDTO) {
        TasteTask tasteTask = DtoMapper.convert(taskDTO, TasteTask.class);
        tasteTask.setUpdateTime(new Date());
        return tasteTaskMapper.updateById(tasteTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFile(MultipartFile file) throws IOException, IllegalAccessException {
        // 1. 读取Excel文件
        List<TasteResultImportExcel> excelList = EasyExcelUtil.getImport(file, TasteResultImportExcel.class);

        if (CollectionUtils.isEmpty(excelList)) {
            throw new BaseKnownException("导入文件为空，请检查文件内容");
        }

        log.info("开始导入品评结果数据，共{}条记录", excelList.size());
        List<String> list = new ArrayList<String>();
        // 2. 数据校验和处理
        for (int i = 0; i < excelList.size(); i++) {
            TasteResultImportExcel excel = excelList.get(i);
            try {
                // 校验必填字段
                validateExcelData(excel, i + 1);

                // 处理导入数据
               String barcode = processImportData(excel);

               if (barcode != null && !barcode.isEmpty()){
                   list.add(barcode);
               }
            } catch (Exception e) {
                log.error("第{}行数据处理失败: {}", i + 1, e.getMessage());
                throw new BaseKnownException(String.format("第%d行数据处理失败: %s", i + 1, e.getMessage()));
            }
        }
        System.out.println(list.toString());
        log.info("品评结果数据导入完成");
    }

    /**
     * 校验Excel数据
     */
    private void validateExcelData(TasteResultImportExcel excel, int rowNum) {
        if (StrUtil.isBlank(excel.getBarcode())) {
            throw new BaseKnownException(String.format("第%d行：样品条码不能为空", rowNum));
        }

        // 校验重量
        if (excel.getWeight() != null && excel.getWeight() <= 0) {
            throw new BaseKnownException(String.format("第%d行：重量必须大于0", rowNum));
        }
    }

    /**
     * 处理导入数据
     */
    private String processImportData(TasteResultImportExcel excel) {
        // 根据样品条码查找对应的品评任务
        TasteResultReportDTO tasteTask = findTasteTaskByBarcode(excel.getBarcode());
        if (tasteTask == null) {
            // 如果没有找到对应的品评任务，可以选择创建新的或抛出异常
            log.warn("未找到样品条码为{}的品评任务", excel.getBarcode());
            return excel.getBarcode();
        }

        // 更新品评任务相关信息
        updateTasteTaskFromExcel(tasteTask, excel);
        return "";
    }

    /**
     * 根据样品条码查找品评任务
     */
    private TasteResultReportDTO findTasteTaskByBarcode(String barcode) {
        return tasteTaskMapper.findTasteTaskByBarcode(barcode);
    }


    @Resource
    private ReceiveWineMapper receiveWineMapper;

    @Resource
    private ReceiveWineDetailMapper receiveWineDetailMapper;

    /**
     * 根据Excel数据更新品评任务
     */
    private void updateTasteTaskFromExcel(TasteResultReportDTO tasteTask, TasteResultImportExcel excel) {
         //通过检验单id查询收酒任务 有则更新无则新建
        Optional<ReceiveWine> receiveWineOptional = receiveWineMapper.getReceiveWineByInspectionId(tasteTask.getInspectionId());
        if (receiveWineOptional.isPresent()){
            ReceiveWine receiveWine = receiveWineOptional.get();
            receiveWine.setAlcoholContent(excel.getAlcoholContent());
            receiveWineMapper.updateById(receiveWine);

            //查询是否有收酒详情没有则新建有则更新
            ReceiveWineDetail receiveWineDetail = receiveWineDetailMapper.selectByReceiveWineId(receiveWine.getId());
            if (receiveWineDetail != null){
                receiveWineDetail.setAmount(excel.getWeight());
                receiveWineDetailMapper.updateById(receiveWineDetail);
            }else {
                receiveWineDetail = new ReceiveWineDetail();
                receiveWineDetail.setAmount(excel.getWeight());
                receiveWineDetail.setReceiveWineId(receiveWine.getId());
                receiveWineDetailMapper.insert(receiveWineDetail);
            }
        }else {
            ReceiveWine receiveWine = new ReceiveWine();
            receiveWine.setInspectionId(tasteTask.getInspectionId());
            receiveWine.setAlcoholContent(excel.getAlcoholContent());
            receiveWine.setStatus(2);
            receiveWine.setFinishTime(new Date());
            receiveWineMapper.insert(receiveWine);

            //新建详情
            ReceiveWineDetail detail = new ReceiveWineDetail();
            detail.setReceiveWineId(receiveWine.getId());
            detail.setAmount(excel.getWeight());
            receiveWineDetailMapper.insert(detail);
        }
    }
}
