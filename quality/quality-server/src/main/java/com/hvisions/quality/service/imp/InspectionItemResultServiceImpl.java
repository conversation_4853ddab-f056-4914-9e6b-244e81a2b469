package com.hvisions.quality.service.imp;

import cn.hutool.json.JSONUtil;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.quality.dao.InspectionItemResultMapper;
import com.hvisions.quality.dto.qc.inspection.InspectionItemResultDTO;
import com.hvisions.quality.entity.qc.inspection.InspectionItem;
import com.hvisions.quality.entity.qc.inspection.InspectionItemResult;
import com.hvisions.quality.repository.InspectionItemResultRepository;
import com.hvisions.quality.service.InspectionItemResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 质量检验-详情条目-检验结果
 */
@Service
@Slf4j
public class InspectionItemResultServiceImpl implements InspectionItemResultService {
    @Autowired
    private InspectionItemResultRepository itemResultRepository;

    @Resource
    private InspectionItemResultMapper inspectionItemResultMapper;

    @Override
    public Integer save(InspectionItemResultDTO itemResultDTO) {
        log.info("save item result: {}", JSONUtil.toJsonStr(itemResultDTO));
        if (Objects.isNull(itemResultDTO)) {
            return null;
        }

        InspectionItemResult itemResult = DtoMapper.convert(itemResultDTO, InspectionItemResult.class);
        Optional<InspectionItemResult> repositoryById = itemResultRepository.findById(itemResult.getId());
        if (repositoryById.isPresent()) {
            InspectionItemResult result = repositoryById.get();
            log.info("数据库的值: {}", JSONUtil.toJsonStr(result));
            String checkResult = null;
            if (!StringUtils.isEmpty(result.getCheckResult())) {
                checkResult = result.getCheckResult();
            }

            String judgeResult = null;
            if (!StringUtils.isEmpty(result.getJudgeResult())) {
                judgeResult = result.getJudgeResult();
            }
            log.info("数据库的值checkResult: {}, judgeResult: {}", checkResult, judgeResult);
            //传入的结果不为空，则更新
            if (!StringUtils.isEmpty(itemResultDTO.getCheckResult())) {
                //数据库的值为空，则更新
                if(StringUtils.isEmpty(checkResult)){
                    log.info("update item result checkResult is empty: {}", JSONUtil.toJsonStr(itemResult));
                    //itemResultRepository.save(itemResult);
                    inspectionItemResultMapper.updateById(itemResult);
                    return itemResult.getId();
                }

                //传的值与数据库的值不一致，则更新
                /*if(!StringUtils.equals(itemResultDTO.getCheckResult(), checkResult)){
                    itemResultRepository.save(itemResult);
                    return itemResult.getId();
                }*/

                //传的checkResult值与数据库的值不一致，或者传的judgeResult与数据库不一致，则更新
                if(!StringUtils.equals(itemResultDTO.getCheckResult(), checkResult) ||
                        !StringUtils.equals(itemResultDTO.getJudgeResult(), judgeResult)){
                    log.info("update item result checkResult is not equals or judgeResult is not equals: {}", JSONUtil.toJsonStr(itemResult));
                    inspectionItemResultMapper.updateById(itemResult);
                    return itemResult.getId();
                }

                //传入的判断结果与数据库不一致 && 传入的结果与数据库一致，则更新
                if (!StringUtils.equals(itemResultDTO.getJudgeResult(), judgeResult) && StringUtils.equals(itemResultDTO.getCheckResult(), checkResult)) {
                    log.info("update item result judgeResult is not equals && checkResult is equals: {}", JSONUtil.toJsonStr(itemResult));
                    inspectionItemResultMapper.updateById(itemResult);
                    return itemResult.getId();
                }
            }


            //传入的是值是空，数据库的值也是空，传入的结果与数据库不一致则更新
            if (StringUtils.isEmpty(itemResultDTO.getCheckResult()) && StringUtils.isEmpty(checkResult) && !StringUtils.equals(itemResultDTO.getJudgeResult(), judgeResult) ) {
                log.info("update item result checkResult is empty && judgeResult is not equals: {}", JSONUtil.toJsonStr(itemResult));
                inspectionItemResultMapper.updateById(itemResult);
                return itemResult.getId();
            }

            //传入的是值是空，数据库的值也是空，或者传入的结果与数据库一致，则更新
            if ((StringUtils.isEmpty(itemResultDTO.getCheckResult()) && StringUtils.isEmpty(checkResult)) || !StringUtils.equals(itemResultDTO.getJudgeResult(), judgeResult)) {
                log.info("update item result checkResult is empty or judgeResult is equals: {}", JSONUtil.toJsonStr(itemResult));
                inspectionItemResultMapper.updateById(itemResult);
                return itemResult.getId();
            }

            //传入的是值是空，数据库的值也是空，则更新
            if (StringUtils.isEmpty(itemResultDTO.getCheckResult()) && StringUtils.isEmpty(checkResult)) {
                log.info("update item result checkResult is empty: {}", JSONUtil.toJsonStr(itemResult));
                inspectionItemResultMapper.updateById(itemResult);
                return itemResult.getId();
            }
        }
        return itemResult.getId();
    }

    @Override
    public void save(List<InspectionItemResultDTO> itemResultDTOList) {
        if (CollectionUtils.isEmpty(itemResultDTOList)) {
            return;
        }

        List<InspectionItemResult> itemResultList = DtoMapper.convertList(itemResultDTOList, InspectionItemResult.class);
        itemResultRepository.saveAll(itemResultList);
    }

    @Override
    public void initItemResult(List<InspectionItem> inspectionItemList) {
        if (CollectionUtils.isEmpty(inspectionItemList)) {
            log.warn("init item result inspectionItemList is empty.");
            return;
        }

        List<InspectionItemResult> itemResultList = inspectionItemList.stream()
                .map(inspectionItem -> {
                    InspectionItemResult itemResult = new InspectionItemResult();
                    itemResult.setInspItemId(inspectionItem.getId());
                    return itemResult;
                }).collect(Collectors.toList());
        itemResultList = itemResultRepository.saveAll(itemResultList);
        log.info("init item result: {}", JSONUtil.toJsonStr(itemResultList));
    }

    @Override
    public InspectionItemResultDTO queryByInspItemId(Integer inspItemId) {
        InspectionItemResult inspectionItemResult = itemResultRepository.findFirstByInspItemId(inspItemId);
        return DtoMapper.convert(inspectionItemResult, InspectionItemResultDTO.class);
    }

    @Override
    public void copy(Integer inspItemId, Integer newInspItemId, boolean bringOriginalData, boolean hasDefaultValue) {
        InspectionItemResult originalItemResult = null;
        if (bringOriginalData) {
            originalItemResult = itemResultRepository.findFirstByInspItemId(inspItemId);
            log.info("find original item result: {}", JSONUtil.toJsonStr(originalItemResult));
        }

        if (Objects.isNull(originalItemResult)) {
            originalItemResult = new InspectionItemResult();
        }

        InspectionItemResult newItemResult = DtoMapper.convert(originalItemResult, InspectionItemResult.class);
        newItemResult.setId(null);
        newItemResult.setInspItemId(newInspItemId);
        itemResultRepository.save(newItemResult);
        log.info("reinspection copy newItemResult: {}", JSONUtil.toJsonStr(newItemResult));
    }

    @Override
    public InspectionItemResult findById(Integer id) {
        return itemResultRepository.findById(id)
                .orElseThrow(() -> new BaseKnownException("检验项目的检验结果数据不存在ID：" + id));
    }

    private void fillDefaultValue(InspectionItemResult newItemResult) {
        // 定性+汇总检验，录入默认值
        newItemResult.setJudgeResult("OK");
        newItemResult.setUnqualifiedQty(0);
    }
}
