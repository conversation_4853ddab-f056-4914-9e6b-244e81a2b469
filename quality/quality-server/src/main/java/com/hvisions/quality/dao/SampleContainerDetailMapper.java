package com.hvisions.quality.dao;

import com.hvisions.quality.dto.qc.container.SampleContainerDetailDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 样品容器Mapper
 */
@Mapper
@Component
public interface SampleContainerDetailMapper {
    /**
     * 样品容器
     *
     * @param query 查询条件
     * @return 样品容器
     */
    List<SampleContainerDetailDTO> query(@Param(value = "query") List<Integer> query);
}
