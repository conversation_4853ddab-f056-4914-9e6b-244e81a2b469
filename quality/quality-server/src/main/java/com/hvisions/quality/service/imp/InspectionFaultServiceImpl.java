package com.hvisions.quality.service.imp;

import cn.hutool.json.JSONUtil;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.quality.dto.qc.fault.FaultDTO;
import com.hvisions.quality.dto.qc.inspection.InspectionFaultDTO;
import com.hvisions.quality.entity.qc.inspection.InspectionFault;
import com.hvisions.quality.repository.InspectionFaultRepository;
import com.hvisions.quality.service.FaultService;
import com.hvisions.quality.service.InspectionFaultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 质量检验故障
 */
@Service
@Slf4j
public class InspectionFaultServiceImpl implements InspectionFaultService {
    @Autowired
    private InspectionFaultRepository inspectionFaultRepository;

    @Autowired
    private FaultService faultService;

    @Override
    public  List<InspectionFault> save(List<InspectionFaultDTO> inspectionFaultDTOList) {
        if (CollectionUtils.isEmpty(inspectionFaultDTOList)) {
            return null;
        }

        List<InspectionFault> inspectionFaultList = DtoMapper.convertList(inspectionFaultDTOList, InspectionFault.class);
        inspectionFaultRepository.saveAll(inspectionFaultList);
        log.info("save inspection fault: {}", JSONUtil.toJsonStr(inspectionFaultList));
        
        return inspectionFaultList;
    }

    @Override
    public List<InspectionFaultDTO> queryByInspItemId(Integer inspItemId) {
        List<InspectionFault> faultList = inspectionFaultRepository.findAllByInspItemId(inspItemId);
        List<InspectionFaultDTO> inspectionFaultDTOList = DtoMapper.convertList(faultList, InspectionFaultDTO.class);
        for (InspectionFaultDTO inspectionFaultDTO : inspectionFaultDTOList) {
            FaultDTO faultDTO = faultService.findById(inspectionFaultDTO.getFaultModeId());
            inspectionFaultDTO.setFaultDTO(faultDTO);
        }
        return inspectionFaultDTOList;
    }

    @Override
    public void copy(Integer inspItemId, Integer newInspItemId) {
        List<InspectionFaultDTO> inspectionFaultDTOList = queryByInspItemId(inspItemId);
        if (CollectionUtils.isEmpty(inspectionFaultDTOList)) {
            return;
        }
        for (InspectionFaultDTO faultDTO : inspectionFaultDTOList) {
            faultDTO.setId(null);
            faultDTO.setInspItemId(newInspItemId);
        }
        save(inspectionFaultDTOList);
        log.info("reinspection copy fault: {}", JSONUtil.toJsonStr(inspectionFaultDTOList));
    }

    @Override
    public void delete(Integer id) {
        inspectionFaultRepository.findById(id)
                .ifPresent(fault -> {
                    inspectionFaultRepository.deleteById(id);
                    log.info("delete inspection fault: {}", JSONUtil.toJsonStr(fault));
                });
    }

}
