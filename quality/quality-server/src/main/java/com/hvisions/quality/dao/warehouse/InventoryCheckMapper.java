package com.hvisions.quality.dao.warehouse;

import com.hvisions.quality.dto.warehouse.InventoryCheckDTO;
import com.hvisions.quality.dto.warehouse.InventoryCheckDetailDTO;
import com.hvisions.quality.dto.warehouse.InventoryCheckQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存盘点Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryCheckMapper {

    /**
     * 查询库存盘点
     *
     * @param query 查询条件
     * @return 库存盘点
     */
    List<InventoryCheckDTO> query(@Param("query") InventoryCheckQuery query);

    /**
     * 盘点单明细
     *
     * @param checkId 盘点ID
     * @return 盘点单明细
     */
    List<InventoryCheckDetailDTO> queryCheckDetail(@Param("checkId") Integer checkId);
}
