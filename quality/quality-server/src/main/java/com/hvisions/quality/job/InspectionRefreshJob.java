package com.hvisions.quality.job;

import com.hvisions.quality.service.imp.InspectionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: refreshJob
 * @description:
 * @date 2024/12/31 10:22
 */

@EnableScheduling
@Component
@Slf4j
@EnableAsync
public class InspectionRefreshJob {
    @Resource
    private InspectionServiceImpl inspectionService;

    private final ReentrantLock lock = new ReentrantLock();

    /**
     * 通过定时任务轮询，刷新
     *
     * 目的：多节点部署时，通过轮询”通知“所有节点，进行刷新 3分钟
     */
    @Scheduled(cron = "0 0/3 * * * ?")
    @Async("refreshLocalCache")
    public void refreshLocalCache() {
        /*log.info("[refreshLocalCache][开始刷新]:{}", LocalDateTime.now());
        inspectionService.initLocalCache();
        log.info("[refreshLocalCache][刷新成功]:{}", LocalDateTime.now());*/
        //CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            if (lock.tryLock()) {
                try {
                    log.info("[refreshLocalCache][开始刷新]:{}", LocalDateTime.now());
                    inspectionService.initLocalCache();
                    log.info("[refreshLocalCache][刷新成功]:{}", LocalDateTime.now());
                } catch (Exception e) {
                    log.error("[refreshLocalCache][刷新失败]:{}", e.getMessage());
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("[refreshLocalCache][已存在正在执行的任务，跳过此次执行]:{}", LocalDateTime.now());
            }
        //});

        // 处理异步任务的结果
        //future.thenAccept(aVoid -> {
            // 任务成功完成后执行的操作
        //    log.info("[refreshLocalCache][任务执行完成，成功]");
        //}).exceptionally(throwable -> {
            // 处理异常
        //    log.error("[refreshLocalCache][任务执行失败]: {}", throwable.getMessage());
        //    return null;
        //});
    }

    /**
     * 把未生成收酒单的数据处理下
     * 每1分钟处理一次
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    @Async("refreshLocalCache")
    public void refreshReceiveWine() {
        log.info("[refreshReceiveWine][开始刷新收酒]:{}", LocalDateTime.now());
        inspectionService.creatReceiveWine();
        log.info("[refreshReceiveWine][刷新成功]:{}", LocalDateTime.now());
    }
}
