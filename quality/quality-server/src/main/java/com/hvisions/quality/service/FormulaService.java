package com.hvisions.quality.service;

import com.hvisions.quality.dto.qc.formula.FormulaCalculateDTO;
import com.hvisions.quality.dto.qc.formula.FormulaDTO;
import com.hvisions.quality.dto.qc.formula.FormulaQuery;
import org.springframework.data.domain.Page;

/**
 * 公式
 */
public interface FormulaService extends EnableService {
    /**
     * 保存
     *
     * @param formulaDTO 公式
     */
    void save(FormulaDTO formulaDTO);

    /**
     * 查询公式
     *
     * @param formulaId 公式ID
     * @return 公式
     */
    FormulaDTO query(Integer formulaId);

    /**
     * 查询公式
     *
     * @param query 查询条件
     * @return 公式
     */
    Page<FormulaDTO> query(FormulaQuery query);

    /**
     * 验证公式
     *
     * @param formulaCalculateDTO 公式计算对象
     * @return 计算结果
     */
    Double verify(FormulaCalculateDTO formulaCalculateDTO);
}
