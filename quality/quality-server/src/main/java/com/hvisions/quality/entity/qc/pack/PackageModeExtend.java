package com.hvisions.quality.entity.qc.pack;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * 包装方式扩展
 */
@Getter
@Setter
@ToString
@Table(name = "hv_qm_package_mode_extend")
@Entity
public class PackageModeExtend {
    /**
     * 主键、
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 包装方式ID
     */
    @NotNull
    private Integer packageModeId;
}