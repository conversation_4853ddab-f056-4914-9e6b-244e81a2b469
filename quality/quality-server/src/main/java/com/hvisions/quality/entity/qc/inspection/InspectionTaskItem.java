package com.hvisions.quality.entity.qc.inspection;

import com.hvisions.quality.dto.qc.inspection.ItemizedResultDTO;
import com.hvisions.quality.entity.SysBase;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 巡检任务-检验条目
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "inspection_task_item", indexes = {@Index(name = "idx_task_id", columnList = "taskId")})
@TypeDef(name = "jsonStringType", typeClass = JsonStringType.class)
public class InspectionTaskItem extends SysBase {
    /**
     * 巡检任务ID
     */
    @NotNull(message = "巡检任务ID不能为空")
    @Column(nullable = false, updatable = false)
    private Integer taskId;

    /**
     * 检验类别ID
     */
    @NotNull(message = "检验类别ID不能为空")
    @Column(nullable = false, updatable = false)
    private Integer checkTypeId;

    /**
     * 检验类别编码
     */
    @NotBlank(message = "检验类别编码不能为空")
    @Column(nullable = false, updatable = false)
    private String checkTypeCode;

    /**
     * 检验类别名称
     */
    @NotBlank(message = "检验类别名称不能为空")
    @Column(nullable = false, updatable = false)
    private String checkTypeName;

    /**
     * 检验项目ID
     */
    @NotNull(message = "检验项目ID不能为空")
    @Column(nullable = false, updatable = false)
    private Integer checkItemId;

    /**
     * 检验项目编码
     */
    @NotBlank(message = "检验项目编码不能为空")
    @Column(nullable = false, updatable = false)
    private String checkItemCode;

    /**
     * 检验项目名称
     */
    @NotBlank(message = "检验项目名称不能为空")
    @Column(nullable = false, updatable = false)
    private String checkItemName;

    /**
     * 数据类型
     */
    @NotBlank(message = "数据类型不能为空")
    @Column(nullable = false, updatable = false)
    private String dataType;

    /**
     * 检验数量
     */
    private Integer inspectionQuantity;

    /**
     * 逐项结果记录
     */
    @Type(type = "jsonStringType")
    @Column(columnDefinition = "json")
    private List<ItemizedResultDTO> itemizedResultRecords;

    /**
     * 不合格数量
     */
    private Integer unqualifiedQuantity;

    /**
     * 判定结果
     */
    private String judgmentResult;

    /**
     * 是否已提交
     */
    @NotNull(message = "是否已提交不能为空")
    private Boolean submitted;

    /**
     * 判定结果
     */
    private String judgeResult;

    /**
     * 检验结果
     */
    private String checkResult;

    /**
     * 备注
     */
    private String remarks;

}