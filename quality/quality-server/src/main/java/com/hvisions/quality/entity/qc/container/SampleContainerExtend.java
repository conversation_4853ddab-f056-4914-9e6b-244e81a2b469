package com.hvisions.quality.entity.qc.container;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * 样品容器扩展
 */
@Getter
@Setter
@ToString
@Table(name = "hv_qm_sample_container_extend")
@Entity
public class SampleContainerExtend {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 样品容器ID
     */
    @NotNull
    private Integer sampleContainerId;
}