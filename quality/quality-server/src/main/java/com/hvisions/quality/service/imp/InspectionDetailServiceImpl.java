package com.hvisions.quality.service.imp;

import cn.hutool.json.JSONUtil;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.FileClient;
import com.hvisions.framework.dto.file.FileDTO;
import com.hvisions.quality.AttachmentDTO;
import com.hvisions.quality.dao.TasteTaskDetailMapper;
import com.hvisions.quality.dto.qc.check.standard.CheckStandardItemDTO;
import com.hvisions.quality.dto.qc.formula.FormulaDTO;
import com.hvisions.quality.dto.qc.inspection.InspectionDetailDTO;
import com.hvisions.quality.dto.qc.inspection.InspectionItemDTO;
import com.hvisions.quality.dto.qc.inspection.InspectionItemResultDTO;
import com.hvisions.quality.entity.qc.inspection.Inspection;
import com.hvisions.quality.entity.qc.inspection.InspectionDetail;
import com.hvisions.quality.entity.qc.inspection.InspectionItemResult;
import com.hvisions.quality.enums.qc.InspectionDetailStatus;
import com.hvisions.quality.enums.qc.JudgeResultEnum;
import com.hvisions.quality.repository.InspectionDetailRepository;
import com.hvisions.quality.repository.InspectionRepository;
import com.hvisions.quality.service.FormulaService;
import com.hvisions.quality.service.InspectionDetailService;
import com.hvisions.quality.service.InspectionItemResultService;
import com.hvisions.quality.service.InspectionItemService;
import com.hvisions.quality.util.FormulaUtil;
import com.hvisions.quality.util.MathUtils;
import com.hvisions.quality.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.hvisions.quality.service.api.InspectionApiServiceImpl.roundToDecimalPlaces;

/**
 * 质量检验详情
 */
@Service
@Slf4j
public class InspectionDetailServiceImpl implements InspectionDetailService {
    @Autowired
    private InspectionDetailRepository inspectionDetailRepository;

    @Autowired
    private InspectionItemService inspectionItemService;

    @Autowired
    private FormulaService formulaService;

    @Autowired
    private InspectionItemResultService inspectionItemResultService;

    @Resource
    private TasteTaskDetailMapper tasteTaskDetailMapper;

    @Resource
    private InspectionRepository inspectionRepository;

    @Resource
    private FileClient fileClient;

    @Override
    public Integer save(InspectionDetailDTO inspectionDetailDTO) {
        InspectionDetail inspectionDetail = DtoMapper.convert(inspectionDetailDTO, InspectionDetail.class);
        inspectionDetail = inspectionDetailRepository.saveAndFlush(inspectionDetail);
        Integer inspectionDetailId = inspectionDetail.getId();
        log.info("save inspection detail code: {}, inspectionDetailId: {}", inspectionDetailDTO.getCheckTypeCode(),
                inspectionDetailId);
        return inspectionDetailId;
    }

    private InspectionDetail findById(Integer id) {
        return inspectionDetailRepository.findById(id)
                .orElseThrow(() -> new BaseKnownException("子检验单不存在ID：" + id));
    }

    @Override
    public void saveInspectionDetail(InspectionDetailDTO inspectionDetailDTO, UserInfoDTO userInfo, boolean isTermination) {
        InspectionDetail inspectionDetail = findById(inspectionDetailDTO.getId());
        /*if (StringUtils.equalsAny(inspectionDetail.getInspectionStatus(),
                InspectionDetailStatus.COMPLETED.getName(), InspectionDetailStatus.TERMINATED.getName())) {
            throw new BaseKnownException(String.format("该子检验单状态是[%s]，不允许操作", inspectionDetail.getInspectionStatus()));
        }*/
        if (StringUtils.equalsAny(inspectionDetail.getInspectionStatus(), InspectionDetailStatus.TERMINATED.getName())) {
            throw new BaseKnownException(String.format("该子检验单状态是[%s]，不允许操作", inspectionDetail.getInspectionStatus()));
        }

        if (Objects.isNull(inspectionDetailDTO.getInspectionStartTime())) {
            inspectionDetailDTO.setInspectionStartTime(new Date());
        }
        inspectionDetailDTO.setInspectionAccountId(userInfo.getId());
        inspectionDetailDTO.setInspectionAccount(userInfo.getUserAccount());
        inspectionDetailDTO.setInspectionName(userInfo.getUserName());

        if (BooleanUtils.isTrue(inspectionDetailDTO.getSubmitted())) {
            // 提交需要校验必填字段
            validateRequiredParam(inspectionDetailDTO);
            inspectionDetailDTO.setInspectionStatus(InspectionDetailStatus.COMPLETED.getName());
            inspectionDetailDTO.setInspectionEndTime(new Date());
        } else {
            inspectionDetailDTO.setInspectionStatus(InspectionDetailStatus.CHECKING.getName());
        }
        //扣款比例
        Double deductionRatio = inspectionDetailDTO.getDeductionRatio();
        if (deductionRatio!=null && deductionRatio>0){
            inspectionDetailDTO.setDeductionRatio(deductionRatio);
        }
        //罚款金额
        Double fineAmount = inspectionDetailDTO.getFineAmount();
        if (fineAmount!=null && fineAmount>0){
            inspectionDetailDTO.setFineAmount(fineAmount);
        }
        save(inspectionDetailDTO);
        saveInspectionItem(inspectionDetailDTO.getInspectionItemDTOList(), userInfo);
        if (isTermination) {
            terminateInspection(inspectionDetailDTO.getInspectionId());
        }
    }

    private void validateRequiredParam(InspectionDetailDTO inspectionDetailDTO) {
        Validator<InspectionDetailDTO> validator = new Validator<>();
        validator.validate(inspectionDetailDTO);
    }

    @Override
    public void saveInspectionItem(List<InspectionItemDTO> inspectionItemDTOList, UserInfoDTO userInfo) {
        if (CollectionUtils.isEmpty(inspectionItemDTOList)) {
            return;
        }

        for (InspectionItemDTO itemDTO : inspectionItemDTOList) {
            InspectionItemResultDTO itemResultDTO = Optional.ofNullable(itemDTO.getInspectionItemResultDTO())
                    .orElseGet(InspectionItemResultDTO::new);
            if ("计算值".equals(itemDTO.getCheckResultType())) {
                itemResultDTO.setInspItemId(itemDTO.getId());
                String checkResult = calculate(itemDTO, inspectionItemDTOList);
                itemDTO.getInspectionItemResultDTO().setCheckResult(checkResult);
                itemResultDTO.setCheckResult(checkResult);
                //根据上下限判断是否合格
                Double limit = itemDTO.getCtrlLowerLimit();
                Double upperLimit = itemDTO.getCtrlUpperLimit();

                //判断是否需要判定
                if(!itemDTO.getJudge()){
                    itemResultDTO.setJudgeResult(itemDTO.getInspectionItemResultDTO().getJudgeResult());
                }else {
                    if (StringUtils.isNotBlank(checkResult)){
                        if(limit != null && upperLimit != null){
                            if(Double.parseDouble(checkResult) >= limit && Double.parseDouble(checkResult) <= upperLimit){
                                itemResultDTO.setJudgeResult(JudgeResultEnum.OK.getCode());
                            }else{
                                itemResultDTO.setJudgeResult(JudgeResultEnum.NG.getCode());
                            }
                        }else if(limit == null && upperLimit != null){
                            if(Double.parseDouble(checkResult) <= upperLimit){
                                itemResultDTO.setJudgeResult(JudgeResultEnum.OK.getCode());
                            }else{
                                itemResultDTO.setJudgeResult(JudgeResultEnum.NG.getCode());
                            }
                        }else if (limit != null && upperLimit == null){
                            if(Double.parseDouble(checkResult) >= limit){
                                itemResultDTO.setJudgeResult(JudgeResultEnum.OK.getCode());
                            }else{
                                itemResultDTO.setJudgeResult(JudgeResultEnum.NG.getCode());
                            }
                        }else {
                            itemResultDTO.setJudgeResult(JudgeResultEnum.OK.getCode());
                        }
                    }
                }
                log.info("calculate checkItemCode: {}, checkResult: {}", itemDTO.getCheckItemCode(), checkResult);
            }

            InspectionItemResult itemResult = Objects.isNull(itemResultDTO.getId()) ? null
                    : inspectionItemResultService.findById(itemResultDTO.getId());
            InspectionItemResultDTO originalItemResultDTO = DtoMapper.convert(itemResult, InspectionItemResultDTO.class);
            // 判断检验结果数据是否有变化，有变化则更新检验人信息
            if (!itemResultDTO.isEquals(originalItemResultDTO)) {
                log.info("compare inspection item result is not equals. originalItemResult: {}, newItemResult: {}",
                        JSONUtil.toJsonStr(originalItemResultDTO), JSONUtil.toJsonStr(itemResultDTO));
                itemResultDTO.setInspectorAccount(userInfo.getUserAccount());
                itemResultDTO.setInspectorName(userInfo.getUserName());
            }
            itemDTO.setInspectionItemResultDTO(itemResultDTO);
        }
        log.info("save inspection detail item: {}", JSONUtil.toJsonStr(inspectionItemDTOList));
        inspectionItemService.saveResult(inspectionItemDTOList);
        log.info("save inspection detail item size: {}", inspectionItemDTOList.size());
    }

    public String calculate(InspectionItemDTO itemDTO, List<InspectionItemDTO> inspectionItemDTOList) {
        if (Objects.isNull(itemDTO.getFormulaId())) {
            log.warn("calculate inspectionItemId: {}, checkItemCode: {}, formulaId is null.", itemDTO.getId(),
                    itemDTO.getCheckItemCode());
            return null;
        }

        FormulaDTO formulaDTO = formulaService.query(itemDTO.getFormulaId());
        if (Objects.isNull(formulaDTO)) {
            log.warn("calculate inspectionItemId: {}, checkItemCode: {}, formula not found. formulaId: {}",
                    itemDTO.getId(), itemDTO.getCheckItemCode(), itemDTO.getFormulaId());
            return null;
        }

        if (StringUtils.isBlank(formulaDTO.getFormulaCalcCode())) {
            log.warn("calculate inspectionItemId: {}, checkItemCode: {}, formulaId: {} formulaCalcCode is blank.",
                    itemDTO.getId(), itemDTO.getCheckItemCode(), itemDTO.getFormulaId());
            return null;
        }

        String[] calcCodeArray = FormulaUtil.parseCalcCode(formulaDTO.getFormulaCalcCode());
        Map<String, Object> calcCheckResultMap = new HashMap<>(calcCodeArray.length);
        for (String calcCode : calcCodeArray) {
            InspectionItemDTO calcCodeItemDTO = inspectionItemDTOList.stream()
                    .filter(item -> StringUtils.equals(calcCode, item.getCheckItemCode()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(calcCodeItemDTO)) {
                // 编码为calcCode的检验项目不存在
                log.warn("calculate inspectionDetailId: {}, calcCode: {} not found.", null, calcCode);
                return null;
            }

            InspectionItemResultDTO calcCodeItemResultDTO = calcCodeItemDTO.getInspectionItemResultDTO();
            if (Objects.isNull(calcCodeItemResultDTO) || StringUtils.isBlank(calcCodeItemResultDTO.getCheckResult())) {
                log.warn("checkItemCode: {} itemResult is null or itemResult.checkResult is blank. itemResult: {}",
                        calcCodeItemDTO.getCheckItemCode(), JSONUtil.toJsonStr(calcCodeItemResultDTO));
                return null;
            }
            try {
                log.info("值{}", calcCodeItemResultDTO.getCheckResult());
                double checkResult = Double.parseDouble(calcCodeItemResultDTO.getCheckResult());
                log.info("原始数据checkResult:{}", checkResult);
                // double 四舍五入根据calcCodeItemDTO.getDecimalDigit()保留小数位
                if (Objects.isNull(calcCodeItemDTO.getDecimalDigit())){
                    calcCodeItemDTO.setDecimalDigit(2);
                }
                checkResult = MathUtils.round(checkResult, calcCodeItemDTO.getDecimalDigit());
                log.info("checkItemCode: {}, checkResult: {}", calcCodeItemDTO.getCheckItemCode(), checkResult);
                calcCheckResultMap.put(calcCode, checkResult);
            } catch (NumberFormatException e) {
                log.error("checkItemCode: {} parseDouble: {} exception occurred.", calcCodeItemDTO.getCheckItemCode(),
                        calcCodeItemResultDTO.getCheckResult(), e);
                return null;
            }
        }
        log.info("calculate formulaCalcCode: {}, calcCheckResultMap: {}", formulaDTO.getFormulaCalcCode(),
                JSONUtil.toJsonStr(calcCheckResultMap));
        Double result = FormulaUtil.calculateNew(formulaDTO.getFormulaCalcCode(), calcCheckResultMap,itemDTO);
        if (Objects.isNull(result)){
            return null;
        }

        result=roundToDecimalPlaces(result,5);

        log.info("计算结果进行四舍五入，保留配置的小数，没配置则默认为2位, 原值result: {},", result);
        result=roundToDecimalPlaces(result,itemDTO.getDecimalDigit()==null?2:itemDTO.getDecimalDigit());
        log.info("计算结果进行四舍五入，保留配置的小数，没配置则默认为2位,计算后的result: {},", result);
        //double数转换为字符串时，不用科学计数法表示
        String str = String.format("%.16f", result);
        log.info("calculate result: {}, str: {}", result, str);
        return str;
    }

    @Override
    public void createInspectionDetail(Integer inspectionId, List<CheckStandardItemDTO> standardItemDTOList) {
        Map<Integer, List<CheckStandardItemDTO>> checkTypeMap = standardItemDTOList.stream()
                .collect(Collectors.groupingBy(CheckStandardItemDTO::getCheckTypeId));
        for (Map.Entry<Integer, List<CheckStandardItemDTO>> entry : checkTypeMap.entrySet()) {
            Integer checkTypeId = entry.getKey();
            CheckStandardItemDTO standardItemDTO = entry.getValue().get(0);
            InspectionDetail inspectionDetail = new InspectionDetail();
            inspectionDetail.setInspectionId(inspectionId);
            inspectionDetail.setCheckTypeId(checkTypeId);
            inspectionDetail.setCheckTypeCode(standardItemDTO.getCheckTypeCode());
            inspectionDetail.setCheckTypeName(standardItemDTO.getCheckTypeName());
            inspectionDetail.setInspectionStatus(InspectionDetailStatus.AWAIT_CHECK.getName());
            inspectionDetail.setMenuType(standardItemDTO.getMenuType());
            inspectionDetail = inspectionDetailRepository.save(inspectionDetail);
            inspectionItemService.createInspectionItem(inspectionDetail.getId(), entry.getValue());
        }
    }

    @Override
    public List<InspectionDetailDTO> queryInspectionDetailList(Integer inspectionId) {
        log.info("queryInspectionDetailList inspectionId: {}", inspectionId);
        Optional<Inspection> inspectionOptional = inspectionRepository.findById(inspectionId);

        List<InspectionDetail> inspectionDetailList = inspectionDetailRepository.findAllByInspectionId(inspectionId);
        //文件再处理一次需要加上文件扩展名
        inspectionDetailList.forEach(inspectionDetail -> {
            if (inspectionDetail.getAttachments() != null) {
                //
                List<AttachmentDTO> attachmentDTOList = inspectionDetail.getAttachments();
                //通过文件id集合去获取文件扩展名
                List<Integer> fileIdList = attachmentDTOList.stream().map(AttachmentDTO::getId).collect(Collectors.toList());
                ResultVO<List<FileDTO>> fileResultVO = fileClient.getFileDetailBatch(fileIdList);
                if (!fileResultVO.isSuccess()){
                    log.error("queryInspectionDetailList getFileDetailBatch error, fileIdList: {}", fileIdList);
                }

                attachmentDTOList.stream().forEach(attachmentDTO -> {
                    fileResultVO.getData().stream().filter(fileDTO -> fileDTO.getId().equals(attachmentDTO.getId())).findFirst().ifPresent(fileDTO -> {
                        attachmentDTO.setExtName(fileDTO.getFileExtend());
                    });
                });
            }
        });

        List<InspectionDetailDTO> inspectionDetailDTOList = DtoMapper.convertList(inspectionDetailList, InspectionDetailDTO.class);
        List<Integer> inspDetailIdList = inspectionDetailDTOList.stream().map(InspectionDetailDTO::getId).collect(Collectors.toList());
        List<InspectionItemDTO> inspectionItemDTOList = inspectionItemService.queryItemList(inspDetailIdList);
        //List<InspectionItemDTO> inspectionItemDTOList = inspectionItemService.queryItemListNew(inspDetailIdList);
        Map<Integer, List<InspectionItemDTO>> inspectionItemMap = inspectionItemDTOList.stream()
                .collect(Collectors.groupingBy(InspectionItemDTO::getInspDetailId));
        for (InspectionDetailDTO inspectionDetailDTO : inspectionDetailDTOList) {
            if (inspectionOptional.isPresent()) {
                Inspection inspection = inspectionOptional.get();
                inspectionDetailDTO.setDeductionRatio(inspection.getDeductionRatio());
                inspectionDetailDTO.setFineAmount(inspection.getFineAmount());
            }

            inspectionDetailDTO.setInspectionItemDTOList(inspectionItemMap.get(inspectionDetailDTO.getId()));


            if(("品评").equals(inspectionDetailDTO.getMenuType()) && ("DQPP").equals(inspectionDetailDTO.getCheckTypeCode())){
                String tastePersonName = tasteTaskDetailMapper.selectTastePersonName(inspectionId);
                inspectionDetailDTO.setInspectionName(tastePersonName);
            }

            if(("品评").equals(inspectionDetailDTO.getMenuType())){
                //查询品评综合结论 -- 因为品评只有一条数据，所以直接取
                InspectionItemDTO inspectionItemDTO = inspectionDetailDTO.getInspectionItemDTOList().get(0);
                if (inspectionItemDTO.getInspectionItemResultDTO() != null){
                    String conclusion = tasteTaskDetailMapper.selectConclusion(inspectionId);
                    inspectionItemDTO.getInspectionItemResultDTO().setCheckResult(conclusion);
                }
            }
        }
        return inspectionDetailDTOList;
    }

    @Override
    public InspectionDetailDTO queryById(Integer id) {
        InspectionDetail inspectionDetail = inspectionDetailRepository.findById(id).orElse(null);
        return DtoMapper.convert(inspectionDetail, InspectionDetailDTO.class);
    }

    @Override
    public Double calculate(InspectionItemDTO inspectionItemDTO) {
        FormulaDTO formulaDTO = formulaService.query(inspectionItemDTO.getFormulaId());
        if (Objects.isNull(formulaDTO)) {
            throw new BaseKnownException("该检验项目公式不存在");
        }

        if (StringUtils.isBlank(formulaDTO.getFormulaCalcCode())) {
            log.warn("inspectionItemId: {}, checkItemCode: {}, formulaId: {} formulaCalcCode is blank.",
                    inspectionItemDTO.getId(), inspectionItemDTO.getCheckItemCode(), inspectionItemDTO.getFormulaId());
            return null;
        }

        String[] calcCodeArray = FormulaUtil.parseCalcCode(formulaDTO.getFormulaCalcCode());
        Map<String, Object> checkResultMap = new HashMap<>(calcCodeArray.length);
        List<InspectionItemDTO> inspectionItemDTOList = inspectionItemService.queryItemList(Collections.singletonList(inspectionItemDTO.getInspDetailId()));
        for (String calcCode : calcCodeArray) {
            InspectionItemDTO tempItemDTO = inspectionItemDTOList.stream()
                    .filter(itemDTO -> StringUtils.equals(calcCode, itemDTO.getCheckItemCode()))
                    .findFirst()
                    .orElseThrow(() -> new BaseKnownException(String.format("不存在编码为%s的检验项目", calcCode)));
            InspectionItemResultDTO itemResultDTO = inspectionItemResultService.queryByInspItemId(tempItemDTO.getId());
            if (Objects.isNull(itemResultDTO) || StringUtils.isBlank(itemResultDTO.getCheckResult())) {
                log.warn("checkItemCode: {} itemResult is null or itemResult.checkResult is blank. itemResult: {}",
                        tempItemDTO.getCheckItemCode(), JSONUtil.toJsonStr(itemResultDTO));
                return null;
            }
            try {
                double checkResult = Double.parseDouble(itemResultDTO.getCheckResult());
                checkResultMap.put(calcCode, checkResult);
            } catch (NumberFormatException e) {
                log.error("checkItemCode: {} parseDouble: {} exception occurred.", tempItemDTO.getCheckItemCode(),
                        itemResultDTO.getCheckResult(), e);
                throw new BaseKnownException("解析检验结果异常（非数值）：" + itemResultDTO.getCheckResult());
            }
        }
        return FormulaUtil.calculate(formulaDTO.getFormulaCalcCode(), checkResultMap);
    }

    @Override
    public void terminateInspection(Integer inspectionId) {
        log.info("terminate inspection inspectionId: {}", inspectionId);
        List<InspectionDetail> inspectionDetailList = inspectionDetailRepository.findAllByInspectionId(inspectionId);
        List<InspectionDetail> nonCompletedDetailList = inspectionDetailList.stream()
                .filter(inspectionDetail -> !StringUtils.equals(InspectionDetailStatus.COMPLETED.getName(),
                        inspectionDetail.getInspectionStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nonCompletedDetailList)) {
            log.info("inspection detail not completed is empty.");
            return;
        }

        log.info("inspection detail not completed for idList: {}",
                nonCompletedDetailList.stream().map(InspectionDetail::getId).collect(Collectors.toList()));
        // 终止未完成检验的质量检验明细
        nonCompletedDetailList.forEach(detail -> detail.setInspectionStatus(InspectionDetailStatus.TERMINATED.getName()));
        inspectionDetailRepository.saveAll(nonCompletedDetailList);
        inspectionDetailRepository.flush();
    }

    @Override
    public void copy(Integer inspectionId, Integer newInspectionId, boolean bringOriginalData) {
        List<InspectionDetail> inspectionDetailList = inspectionDetailRepository.findAllByInspectionId(inspectionId);
        List<InspectionDetailDTO> inspectionDetailDTOList = DtoMapper.convertList(inspectionDetailList, InspectionDetailDTO.class);
        for (InspectionDetailDTO inspectionDetailDTO : inspectionDetailDTOList) {
            InspectionDetailDTO newInspectionDetailDTO = DtoMapper.convert(inspectionDetailDTO, InspectionDetailDTO.class);
            newInspectionDetailDTO.setId(null);
            newInspectionDetailDTO.setInspectionId(newInspectionId);
            newInspectionDetailDTO.setInspectionStatus(InspectionDetailStatus.AWAIT_CHECK.getName());
            if (!bringOriginalData) {
                newInspectionDetailDTO.setInspectionAccountId(null);
                newInspectionDetailDTO.setInspectionAccount(null);
                newInspectionDetailDTO.setInspectionName(null);
                newInspectionDetailDTO.setInspectionStartTime(null);
                newInspectionDetailDTO.setInspectionEndTime(null);
                newInspectionDetailDTO.setInspectionResult(null);
                newInspectionDetailDTO.setInspectionConclusion(null);
                newInspectionDetailDTO.setLossQty(null);
                newInspectionDetailDTO.setRemark(null);
                newInspectionDetailDTO.setAttachments(null);
            }
            Integer newInspectionDetailId = save(newInspectionDetailDTO);
            inspectionItemService.copy(inspectionDetailDTO.getId(), newInspectionDetailId, bringOriginalData);
        }
    }

}
