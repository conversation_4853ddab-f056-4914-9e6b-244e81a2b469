package com.hvisions.quality.constant;

/**
 * 品评常量
 */
public interface TasteConstant {
    /**
     * 数据类型
     */
    String MENU_TYPE ="品评";

    /**
     * 品评类型扩展表名
     */
    String TASTE_TYPE_EXTEND_TABLE_NAME = "hv_qm_taste_type_extend";

    /**
     * 品评项目扩展表名
     */
    String TASTE_ITEM_EXTEND_TABLE_NAME = "hv_qm_taste_item_extend";
    /**
     * 品评类别下载模板文件名
     */
    String TASTE_TYPE_TEMPLATE_FILE_NAME = "品评类别模板";
    /**
     * 品评类别导出文件名
     */
    String TASTE_TYPE_EXPORT_FILE_NAME = "品评类别";
    /**
     * 品评项目导出文件名
     */
    String TASTE_ITEM_EXPORT_FILE_NAME = "品评项目";

    /**
     * 品评项目下载模板文件名
     */
    String TASTE_ITEM_TEMPLATE_FILE_NAME = "品评项目模板";
    /**
     * 品评结论表名
     */
    String TASTE_CONCLUSION_TABLE_NAME ="hv_qm_taste_conclusion";
    /**
     * 扩展表中的主表ID字段
     */
    String CONCLUSION_EXTEND_ID ="conclusion_id";
    /**
     * 品评结论评语扩展表名
     */
    String TASTE_CONCLUSION_COMMENT_EXTEND_TABLE_NAME = "hv_qm_taste_conclusion_comment_extend";
    /**
     * 品评结论评语扩展表名
     */
    String TASTE_CONCLUSION_TYPE_EXTEND_TABLE_NAME = "hv_qm_taste_conclusion_type_extend";
    /**
     * 评语编码头
     */
    String CONCLUSION_COMMENT_CODE_HEAD ="PY";
    /**
     * 品评分类编码头
     */
    String CONCLUSION_TYPE_CODE_HEAD="PYLX";

    /**
     * 品评结论编码位数
     */
    Integer CONCLUSION_CODE_LENGTH =5;
    /**
     * 品评结论评语下载模板文件名
     */
    String TASTE_CONCLUSION_COMMENT_TEMPLATE_FILE_NAME = "品评结论评语模板";
    /**
     * 品评结论评语导出文件名
     */
    String TASTE_CONCLUSION_COMMENT_EXPORT_FILE_NAME = "品评结论评语";
    /**
     * 品评结论类别下载模板文件名
     */
    String TASTE_CONCLUSION_TYPE_TEMPLATE_FILE_NAME = "品评结论类别模板";
    /**
     * 品评结论类别导出文件名
     */
    String TASTE_CONCLUSION_TYPE_EXPORT_FILE_NAME = "品评结论类别";
    /**
     * 品评方法表名
     */
    String TASTE_METHOD_TABLE_NAME ="hv_qm_taste_method";
    /**
     * 扩展表中的主表ID字段
     */
    String METHOD_EXTEND_ID ="method_id";
    /**
     * 品评方法扩展表名
     */
    String TASTE_METHOD_EXTEND_TABLE_NAME = "hv_qm_taste_method_extend";
    /**
     * 方法编码头
     */
    String METHOD_CODE_HEAD ="EvM";
    /**
     * 品评方法编码位数
     */
    Integer METHOD_CODE_LENGTH =8;
    /**
     * 品评方法下载模板文件名
     */
    String TASTE_METHOD_TEMPLATE_FILE_NAME = "品评方法模板";
    /**
     * 品评方法导出文件名
     */
    String TASTE_METHOD_EXPORT_FILE_NAME = "品评方法";
    /**
     * 品评任务编码头
     */
    String TASK_CODE_HEAD="RW";
    /**
     * 品评任务编码位数
     */
    Integer TASK_CODE_LENGTH =2;
    /**
     * 登录用户id的redis的key的前缀
     */
    String USER_ID_KEY="auth_";
    /**
     * 品评项目编码头
     */
    String TASTE_ITEM_HEAD="BM";
    /**
     * 品评任务编码位数
     */
    Integer TASTE_ITEM_LENGTH =3;
    /**
     * 品评任务编码前缀
     */
    String TASK_CODE_PREFIX="_PP_";
}
