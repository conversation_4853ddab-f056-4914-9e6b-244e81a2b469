package com.hvisions.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.quality.dto.qc.taste.task.*;
import com.hvisions.quality.entity.qc.taste.task.TasteTask;
import com.hvisions.quality.entity.qc.taste.task.TasteTaskDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 品评任务详情
 */
@Component
@Mapper
public interface TasteTaskDetailMapper extends BaseMapper<TasteTaskDetail> {
    /**
     * 查询详情
     * @param id 任务id
     * @return 详情
     */
    List<TaskDetailDTO> selectDetailList(Integer id);

    /**
     * 查询品评列表
     * @param query 查询条件
     * @return 品评列表
     */
    List<TasteDTO> selectTasteList(TasteQuery query);

    /**
     * 查询还没有品评的数量
     * @param id 品评id
     * @return 没有品评的数量
     */
    Integer selectNotTasteCount(Integer id);

    /**
     * 查询品评详情
     * @param id 品评id
     * @return 品评详情
     */
    TasteDetailDTO selectTasteDetail(Integer id);

    /**
     * 根据品评id获取组号列表
     * @param tasteId 品评id
     * @param userId 用户id
     * @return 组号列表
     */
    List<Integer> selectGroupNoList(@Param("tasteId") Integer tasteId, @Param("userId") Integer userId);

    /**
     * 查询带有用户信息的品评详情
     * @param id 品评任务id
     * @return 带有用户信息的品评详情
     */
    List<TaskDetailAddDTO> selectHaveUserDetailList(Integer id);

    /**
     * 查询app端品评任务详情
     * @param id 品评任务id
     * @return 详情
     */
    List<TaskDetailAppDTO> selectAppTaskDetail(Integer id);

    /**
     * 查询品评的品评项结论
     * @param id 品评id
     * @return 品评项目的结论
     */
    List<ItemConclusionDTO> selectItemConclusionList(Integer id);

    /**
     * 查询还没有品评组号
     * @param groupNo 组号  today当天日期
     * @return 没有品评的数量
     */
    String selectNotTasteGroupNo(String groupNo, String  today);

    /**
     * 查询还没有品评组号
     * @param groupNo 组号  today当天日期
     * @return 没有品评的数量
     */
    String selectNotTasteGroupNoNew(@Param("groupNo") String groupNo,@Param("today") String  today);

    /**
     * 查询品评任务id
     * @param id 品评详情id
     * @return 品评任务id
     */
    TasteTask selectTaskDetailList(Integer id);
    /**
     * 更新品评任务状态
     * @param id 品评任务id
     * @return 详情
     */
    Integer updateStatusById(@Param("id") int id, @Param("status") int status);


    List<Integer> selectInspectionDetailList(@Param("inspectionNumber") String inspectionNumber);

    String selectTastePersonName(@Param("inspectionId") Integer inspectionId);

    String selectConclusion(@Param("inspectionId") Integer inspectionId);
}
