package com.hvisions.quality.dto.qc.inspection;

import com.hvisions.quality.AttachmentDTO;
import com.hvisions.quality.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 质量检验明细-检验类别
 */
@Getter
@Setter
@ToString
public class InspectionDetailDTO extends SysBaseDTO {
    /**
     * 质检表ID
     */
    @ApiModelProperty(value = "质检表ID", required = true)
    private Integer inspectionId;

    /**
     * 检验类别ID
     */
    @ApiModelProperty(value = "检验类别ID", required = true)
    private Integer checkTypeId;

    /**
     * 检验类别编码
     */
    @ApiModelProperty(value = "检验类别编码", required = true)
    private String checkTypeCode;

    /**
     * 检验类别名称
     */
    @ApiModelProperty(value = "检验类别名称", required = true)
    private String checkTypeName;

    /**
     * 菜单类型
     */
    @ApiModelProperty(value = "菜单类型(品评)，取自检验项目")
    private String menuType;

    /**
     * 检验人ID
     */
    @ApiModelProperty(value = "检验人ID", readOnly = true)
    private Integer inspectionAccountId;

    /**
     * 检验人账号
     */
    @ApiModelProperty(value = "检验人账号", readOnly = true)
    private String inspectionAccount;

    /**
     * 检验人姓名
     */
    @ApiModelProperty(value = "检验人姓名", readOnly = true)
    private String inspectionName;

    /**
     * 检验开始时间
     */
    @ApiModelProperty(value = "检验开始时间")
    private Date inspectionStartTime;

    /**
     * 检验结束时间
     */
    @ApiModelProperty(value = "检验结束时间")
    private Date inspectionEndTime;

    /**
     * 检验结果
     */
    @ApiModelProperty(value = "判定结果:OK/NG")
    @NotBlank(message = "子检验单的判定结果不能为空")
    private String inspectionResult;

    /**
     * 检验结论
     */
    @ApiModelProperty(value = "检验结论-待删除")
    private String inspectionConclusion;

    /**
     * 损耗数量
     */
    @ApiModelProperty(value = "损耗数量-待删除")
    @Deprecated
    private Integer lossQty;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 检验状态，枚举值
     */
    @ApiModelProperty(value = "检验状态")
    private String inspectionStatus;

    /**
     * 是否已提交
     */
    @ApiModelProperty(value = "是否已提交", required = true)
    @NotNull(message = "是否已提交不能为空")
    private Boolean submitted;

    /**
     * 质量检验详情条目
     */
    @ApiModelProperty(value = "质量检验详情条目")
    @NotEmpty(message = "质量检验详情条目不能为空")
    @Valid
    private List<InspectionItemDTO> inspectionItemDTOList;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表")
    private List<AttachmentDTO> attachments;

    /**
     * 处理意见
     */
    @ApiModelProperty(value = "处理意见")
    private String suggestion;

    /**
     * 回传rms
     */
    @ApiModelProperty(value = "回传rms物料类型")
    private String materialType;

    @ApiModelProperty("扣款比例")
    private Double deductionRatio;

    @ApiModelProperty("罚款金额")
    private Double fineAmount;
}