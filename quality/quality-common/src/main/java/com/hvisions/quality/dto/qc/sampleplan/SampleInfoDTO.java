package com.hvisions.quality.dto.qc.sampleplan;

/**
 * <AUTHOR>
 */
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Getter
@Setter
@ToString
@AllArgsConstructor
public class SampleInfoDTO {
    /**
     * 样本数量
     */
    @ApiModelProperty(value = "样本数量")
    private Integer sampleCount;
    /**
     * 拒收
     */
    @ApiModelProperty(value = "拒收")
    private Integer re;
    /**
     * 接受
     */
    @ApiModelProperty(value = "接受")
    private Integer ac;
}
