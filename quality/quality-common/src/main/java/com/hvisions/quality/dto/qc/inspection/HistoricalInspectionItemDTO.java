package com.hvisions.quality.dto.qc.inspection;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 历史检验项目
 *
 * <AUTHOR>
 */
@ApiModel(description = "历史检验项目")
@Getter
@Setter
@ToString
public class HistoricalInspectionItemDTO {
    /**
     * 检验单号
     */
    @ApiModelProperty(value = "检验单号")
    private String inspectionNum;

    /**
     * 检验开始时间
     */
    @ApiModelProperty(value = "检验开始时间")
    private Date inspectionStartTime;

    /**
     * 检验结束时间
     */
    @ApiModelProperty(value = "检验结束时间")
    private Date inspectionEndTime;

    /**
     * 检验项目编码
     */
    @ApiModelProperty(value = "检验项目编码")
    private String checkItemCode;

    /**
     * 检验项目名称
     */
    @ApiModelProperty(value = "检验项目名称")
    private String checkItemName;

    /**
     * 检验人ID
     */
    @ApiModelProperty(value = "检验人ID")
    private Integer inspectionAccountId;

    /**
     * 检验人姓名
     */
    @ApiModelProperty(value = "检验人姓名")
    private String inspectionName;

    /**
     * 判定结果
     */
    @ApiModelProperty(value = "判定结果")
    private String judgeResult;

    /**
     * 检验结果值
     */
    @ApiModelProperty(value = "检验结果值")
    private String checkResult;

}
