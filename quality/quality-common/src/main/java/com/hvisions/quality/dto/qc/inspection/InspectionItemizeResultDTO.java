package com.hvisions.quality.dto.qc.inspection;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * 检验单-逐项结果
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(description = "检验单-逐项结果")
public class InspectionItemizeResultDTO {

    /**
     * 结果
     */
    @ApiModelProperty(value = "结果")
    private String result;

    /**
     * 未检出
     */
    @ApiModelProperty(value = "未检出")
    private Boolean undetected;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InspectionItemizeResultDTO that = (InspectionItemizeResultDTO) o;
        return Objects.equals(result, that.result) && Objects.equals(undetected, that.undetected);
    }

    @Override
    public int hashCode() {
        return Objects.hash(result, undetected);
    }
}
