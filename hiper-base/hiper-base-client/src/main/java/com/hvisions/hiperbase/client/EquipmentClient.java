package com.hvisions.hiperbase.client;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.fallback.EquipmentClientFallBack;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentQueryDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.hiperbase.equipment.location.LocationMsgDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: demoClient</p>
 * <p>Description: 设备Feign接口</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/09</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 * @see EquipmentFeignClient
 * @deprecated
 */
@FeignClient(name = "hiper-base", fallback = EquipmentClientFallBack.class)
@Deprecated
public interface EquipmentClient {

    /**
     * 根据设备id列表查询设备明细
     *
     * @param idList 设备id列表
     * @return 设备信息
     */
    @PostMapping("/equipment/getEquipmentByIdList")
    ResultVO<List<EquipmentDTO>> getEquipmentById(@RequestBody List<Integer> idList);


    /**
     * 根据设备id查询设备明细
     *
     * @param id 设备id
     * @return 设备信息
     */
    @GetMapping("/equipment/getEquipmentById/{id}")
    ResultVO<EquipmentDTO> getEquipmentDtoById(@PathVariable int id);

    /**
     * 更新设备信息
     *
     * @param hvEquipmentDTO 设备信息
     * @return 设备id
     */
    @PutMapping("/equipment/updateEquipment")
    ResultVO<EquipmentDTO> updateEquipment(@RequestBody EquipmentDTO hvEquipmentDTO);

    /**
     * 根据设备code，以及设备名称模糊查询分页信息
     *
     * @param queryDTO 查询对象
     * @return 设备分页信息
     */
    @PostMapping("/equipment/getEquipmentPageByNameOrCodeAndEquipmentTypeId")
    ResultVO<HvPage<EquipmentDTO>> getEquipmentPageByNameOrCodeAndEquipmentTypeId(@RequestBody EquipmentQueryDTO queryDTO);


    /**
     * 根据设备code查询设备
     *
     * @param equipmentCode 设备编码
     * @return 设备信息
     */
    @GetMapping(value = "/equipment/getEquipmentByCode/{equipmentCode}")
    ResultVO<EquipmentDTO> getEquipmentByCode(@PathVariable String equipmentCode);

    /**
     * 根据Id删除设备
     * 嘘
     *
     * @param id 设备ID
     * @return 无
     */
    @DeleteMapping(value = "/equipment/deleteEquipmentById/{id}")
    ResultVO deleteEquipmentById(@PathVariable int id);


    /**
     * 根据设备父级id查询子级设备信息列表
     *
     * @param parentId 父级id
     * @return 设备信息列表
     */
    @GetMapping("/equipment/getEquipmentListByParentId/{parentId}")
    ResultVO<List<EquipmentDTO>> getEquipmentListByParentId(@PathVariable int parentId);


    /**
     * 根据设备类型查询设备信息列表
     *
     * @param id 设备类型id
     * @return 设备信息列表
     */
    @GetMapping("/equipment/getEquipmentListByEquipmentTypeId/{id}")
    ResultVO<List<EquipmentDTO>> getEquipmentListByEquipmentTypeId(@PathVariable int id);

    /**
     * 添加设备扩展属性
     *
     * @param extendColumnInfo 扩展属性
     * @return 无
     */
    @PatchMapping(value = "/equipment/createEquipmentColumn")
    ResultVO createEquipmentColumn(@RequestBody ExtendColumnInfo extendColumnInfo);

    /**
     * 删除扩展属性
     *
     * @param columnName 扩展属性名称
     * @return 无
     */
    @DeleteMapping(value = "/equipment/deleteEquipmentColumnByColumnName/{columnName}")
    ResultVO deleteEquipmentColumnByColumnName(@PathVariable String columnName);

    /**
     * 根据CellId查询Cell下所属的设备信息
     *
     * @param id CellID
     * @return 设备信息列表
     */
    @GetMapping("/equipment/getEquipmentListByCellId/{id}")
    ResultVO<List<EquipmentDTO>> getEquipmentListByCellId(@PathVariable int id);


    /**
     * 设备的归属关系
     *
     * @param equipmentId 设备id
     * @return 是否成功
     */
    @ApiOperation(value = "删除设备的Location归属关系")
    @DeleteMapping(value = "/equipment/deleteCellEquipmentRelation/{equipmentId}/{cellId}")
    ResultVO deleteCellEquipmentRelation(@PathVariable int equipmentId, @PathVariable int cellId);

    /**
     * 根据location ID查询扩展属性
     *
     * @param id 主键id
     * @return location信息
     */
    @RequestMapping(value = "/location/getLocationById/{id}", method = RequestMethod.GET)
    ResultVO<LocationDTO> getLocationById(@PathVariable int id);


    /**
     * 根据设备ID查询所在位置信息（集团，工厂，车间，产线）
     *
     * @param id 设备ID
     * @return 设备所在位置信息
     */
    @GetMapping("/equipment/getLocationMsgDtoByEquipmentId/{id}")
    ResultVO<LocationMsgDTO> getLocationMsgByEquipmentId(@PathVariable int id);


    /**
     * 根据设备ID查询所在位置信息（集团，工厂，车间，产线）
     *
     * @param ids 设备Id列表
     * @return 设备所在位置信息列表
     */
    @PostMapping(value = "/equipment/getLocationMsgDtoListByEquipmentIds")
    ResultVO<Map<Integer, LocationMsgDTO>> getLocationMsgDtoListByEquipmentIds(@RequestBody List<Integer> ids);

    /**
     * 根据Code查询工厂建模
     *
     * @param code code
     * @return location信息
     */
    @GetMapping(value = "/location/getHvBmLocationByCode/{code}")
    ResultVO<LocationDTO> getHvBmLocationByCode(@PathVariable String code);

    /**
     * 根据产线ID集合获取产线信息列表
     *
     * @param idList 产线ID列表
     * @return 产线信息列表
     */
    @GetMapping(value = "/location/getAllByCellIdList")
    ResultVO<List<LocationDTO>> getAllByCellIdList(@RequestParam List<Integer> idList);

    /**
     * 根据ID列表查询设备类型
     *
     * @param idList id列表
     * @return 设备类型列表
     */
    @PostMapping(value = "/equipmentType/getTypeAllByIdIn")
    ResultVO<List<EquipmentTypeDTO>> getTypeAllByIdIn(@RequestBody List<Integer> idList);

    /**
     * 查询所有设备信息
     *
     * @return 设备信息列表
     */
    @GetMapping(value = "/equipment/getAllEquipment")
    ResultVO<List<EquipmentDTO>> getAllEquipment();
}
