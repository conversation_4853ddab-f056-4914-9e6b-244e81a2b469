package com.hvisions.hiperbase.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.fallback.ParameterFallBackFactory;
import com.hvisions.hiperbase.route.dto.ParameterDTO;
import com.hvisions.hiperbase.route.dto.ParameterQueryDTO;
import com.hvisions.hiperbase.route.dto.ParameterQueryOperationDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>Title: ParameterController</p>
 * <p>Description: 工艺参数控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/24</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
@FeignClient(path = "parameter", name = "hiper-base", fallbackFactory = ParameterFallBackFactory.class)
public interface ParameterClient {

    /**
     * 创建工艺参数
     *
     * @param parameterDTO 工艺参数dto
     * @return 工艺参数id
     */
    @PostMapping("/createParameter")
    @ApiOperation(value = "创建工艺参数")
    ResultVO<Integer> createParameter(@RequestBody ParameterDTO parameterDTO);

    /**
     * 根据id删除工艺参数
     *
     * @param id 工艺参数id
     */
    @DeleteMapping("/deleteParameterById/{id}")
    @ApiOperation(value = "根据id删除工艺参数")
    ResultVO deleteParameterById(@PathVariable int id);

    /**
     * 根据编码或者名称查询工艺参数
     *
     * @param queryDTO 根据编码或者名称
     * @return 工艺参数分页信息
     */
    @PostMapping("/getParameterByCodeOrName")
    @ApiOperation(value = "根据编码或者名称查询工艺参数")
    ResultVO<HvPage<ParameterDTO>> getParameterByCodeOrName(@RequestBody ParameterQueryDTO queryDTO);

    /**
     * 根据id查询工艺参数
     *
     * @param id 工艺参数id
     * @return 工艺参数
     */
    @GetMapping("/getParameterById/{id}")
    @ApiOperation(value = "根据id查询工艺参数")
    ResultVO<ParameterDTO> getParameterById(@PathVariable int id);

    /**
     * 根据id列表查询工艺参数
     *
     * @param ids 工艺参数id
     * @return 工艺参数列表
     */
    @PostMapping("/getParametersByIds")
    @ApiOperation(value = "根据id列表查询工艺参数列表")
    ResultVO<List<ParameterDTO>> getParameterByIdList(@RequestBody List<Integer> ids);

    /**
     * 更新工艺参数
     *
     * @param parameter 工艺参数
     * @return 工艺参数id
     */
    @PutMapping("/updateParameter")
    @ApiOperation(value = "更新工艺参数")
    ResultVO<Integer> updateParameter(@RequestBody ParameterDTO parameter);


    /**
     * 根据操作Id查询工艺参数
     *
     * @param queryDTO 根据操作DTO
     * @return 工艺参数分页信息
     */
    @PostMapping("/getParameterByOperationIdPage")
    @ApiOperation(value = "根据工艺操作Id查询工艺参数")
    ResultVO<HvPage<ParameterDTO>> getParameterByOperationIdPage(@RequestBody ParameterQueryOperationDTO queryDTO);

}










