package com.hvisions.hiperbase.materials.dto;

import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.common.interfaces.IExtendObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: ExportMaterialDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-17</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ExportMaterialDTO implements IExtendObject {

    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1", readOnly = true)
    private Integer id;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", required = true, readOnly = true)
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    /***
     * 特征值
     */
    @ApiModelProperty(value = "特征值")
    private String eigenvalue;

    /**
     * 是否追溯
     */
    @ApiModelProperty(value = "是否追溯")
    private Boolean serialNumberProfile;

    /**
     * 计量单位名称
     */
    @ApiModelProperty(value = "计量单位名称")
    private String uomName;

    /**
     * 物料分组编码
     */
    @ApiModelProperty(value = "物料分组编码")
    private String materialGroupCode;

    /**
     * 物料分组描述
     */
    @ApiModelProperty(value = "物料分组描述")
    private String materialGroupDesc;

    /**
     * 物料类型编码
     */
    @ApiModelProperty(value = "物料类型编码")
    private String materialTypeCode;

    /**
     * 物料类型名称
     */
    @ApiModelProperty(value = "物料类型名称")
    private String materialTypeName;

    /**
     * 扩展属性
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(name = "扩展属性", value = "扩展属性")
    private Map<String, Object> extend = new HashMap<>();
}