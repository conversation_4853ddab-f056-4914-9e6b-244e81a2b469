package com.hvisions.hiperbase.schedule.enums;

import com.hvisions.common.interfaces.BaseErrorCode;

/**
 * <p>Title: ScheduleExceptionEnum</p>
 * <p>Description: 班次班组异常信息</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/27</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum ScheduleExceptionEnum implements BaseErrorCode {

    //异常信息
    SHIFT_PARSE_ERROR(40010),
    SHIFT_HOUR_PARES_ERROR(40020),
    SHIFT_MINUTE_PARES_ERROR(40030),
    AUTH_SERVICE_ERROR(40040),
    SCHEDULE_OUT_DATE(40050),
    HOLIDAY_NOT_MATCH_YEAR(40060),
    CREW_NOT_SET(40070),
    SHIFT_NOT_SET(40080),
    BEGIN_TIME_LATER_THAN_END_TIME(40090),
    CREW_NOT_EXISTS(40100),
    SHIFT_NOT_EXISTS(40110),
    CREW_IS_NOT_ENABLE(40120),
    SHIFT_NOT_ENABLE(40130),
    CREW_SHIFT_INFO_EMPTY(40140),
    //输入时间错误
    INPUT_TIME_ERROR(40150),
    //不能为空
    NOT_NULL_ERROR(40160),
    //当前班组已经使用删除失败
    CURRENT_TEAM_HAS_FAILED_TO_USE_DELETE(40170),
    ;

    private int code;

    ScheduleExceptionEnum(int code) {
        this.code = code;
    }


    @Override
    public String getMessage() {
        return this.toString();
    }

    @Override
    public Integer getCode() {
        return code;
    }
}









