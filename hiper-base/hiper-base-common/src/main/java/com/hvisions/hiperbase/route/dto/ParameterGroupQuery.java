package com.hvisions.hiperbase.route.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>Title: ParameterGroupQuery</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/11/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Getter
@Setter
@ToString
public class ParameterGroupQuery extends PageInfo {
    public ParameterGroupQuery() {
        this.code = "";
    }

    /**
     * 分组编码
     */
    @ApiModelProperty(value = "分组编码")
    private String code;

}









