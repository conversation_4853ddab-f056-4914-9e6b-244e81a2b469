package com.hvisions.hiperbase.configuration;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentStatus;
import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentSwotUnit;
import com.hvisions.hiperbase.repository.equipment.EquipmentStatusRepository;
import com.hvisions.hiperbase.repository.equipment.EquipmentSwotUnitRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <p>Title: EquipmentStatusRunner</p >
 * <p>Description: 初始化设备状态</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/6</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Component
public class EquipmentStatusRunner extends SafetyCommandLineRunner {

    private final EquipmentStatusRepository equipmentStatusRepository;

    private final EquipmentSwotUnitRepository equipmentSwotUnitRepository;

    @Autowired
    public EquipmentStatusRunner(EquipmentStatusRepository equipmentStatusRepository,
                                 EquipmentSwotUnitRepository equipmentSwotUnitRepository) {
        this.equipmentStatusRepository = equipmentStatusRepository;
        this.equipmentSwotUnitRepository = equipmentSwotUnitRepository;
    }

    private static final List<HvBmEquipmentStatus> HV_BM_EQUIPMENT_STATUSES = Arrays.asList(
            new HvBmEquipmentStatus(0, "broken", "损坏"),
            new HvBmEquipmentStatus(0, "offline", "离线"),
            new HvBmEquipmentStatus(0, "idle", "闲置"),
            new HvBmEquipmentStatus(0, "repair", "维修中"),
            new HvBmEquipmentStatus(1, "ready", "就绪"),
            new HvBmEquipmentStatus(1, "product", "生产中")
    );

    private static final List<HvBmEquipmentSwotUnit> HV_BM_EQUIPMENT_SWOT_UNITS = Arrays.asList(
            new HvBmEquipmentSwotUnit("mm", "毫米"),
            new HvBmEquipmentSwotUnit("cm", "厘米"),
            new HvBmEquipmentSwotUnit("dm", "分米"),
            new HvBmEquipmentSwotUnit("m", "米"),
            new HvBmEquipmentSwotUnit("km", "千米"),
            new HvBmEquipmentSwotUnit("g", "克"),
            new HvBmEquipmentSwotUnit("kg", "千克"),
            new HvBmEquipmentSwotUnit("pcs", "件"),
            new HvBmEquipmentSwotUnit("ml", "毫升"),
            new HvBmEquipmentSwotUnit("l", "升"));

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void callRunner(String... args) {
        // 设置默认设备状态
        saveEquipmentStatus();
        // 设置默认读数单位
        saveEquipmentSwotUnit();
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveEquipmentStatus() {
        List<HvBmEquipmentStatus> equipmentStatuses = equipmentStatusRepository.findAll();
        if (CollectionUtils.isEmpty(equipmentStatuses)) {
            equipmentStatusRepository.saveAll(HV_BM_EQUIPMENT_STATUSES);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveEquipmentSwotUnit() {
        List<HvBmEquipmentSwotUnit> swotUnits = equipmentSwotUnitRepository.findAll();
        if (CollectionUtils.isEmpty(swotUnits)) {
            equipmentSwotUnitRepository.saveAll(HV_BM_EQUIPMENT_SWOT_UNITS);
        }
    }
}
