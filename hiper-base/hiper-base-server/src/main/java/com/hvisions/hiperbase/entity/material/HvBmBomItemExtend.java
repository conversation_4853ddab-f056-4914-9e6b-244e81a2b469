package com.hvisions.hiperbase.entity.material;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * <p>Title: HvBmBomItemExtend</p >
 * <p>Description: bomItem扩展表</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/6/13</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@Entity
public class HvBmBomItemExtend {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;


    /**
     * 替代物料ID
     */
    private Integer bomItemId;
}
