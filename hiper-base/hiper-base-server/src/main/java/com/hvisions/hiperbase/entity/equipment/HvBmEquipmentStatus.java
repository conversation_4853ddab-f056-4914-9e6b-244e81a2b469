package com.hvisions.hiperbase.entity.equipment;

import com.hvisions.hiperbase.entity.SysBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * <p>Title: HvBmEquipmentStatus</p >
 * <p>Description: 设备状态</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/5</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = {"statusCode"}, name = "设备状态编码唯一"))
public class HvBmEquipmentStatus extends SysBase {

    /**
     * 设备状态类型
     */
    private Integer type;

    /**
     * 设备状态编码
     */
    private String statusCode;

    /**
     * 设备状态名称
     */
    private String statusName;
}
