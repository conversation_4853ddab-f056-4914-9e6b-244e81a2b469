<?xml version="1.0"?>
<oor:data xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:oor="http://openoffice.org/2001/registry"><dependency file="main"/><oor:component-schema oor:name="WriterCommands" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates/><component><group oor:name="UserInterface"><set oor:name="Commands" oor:node-type="LabelType" oor:component="org.openoffice.Office.UI.Commands"/><set oor:name="Popups" oor:node-type="LabelType" oor:component="org.openoffice.Office.UI.Commands"/></group></component></oor:component-schema><oor:component-schema oor:name="WriterFormWindowState" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates/><component><group oor:name="UIElements"><set oor:name="States" oor:node-type="WindowStateType" oor:component="org.openoffice.Office.UI.WindowState"/></group></component></oor:component-schema><oor:component-schema oor:name="WriterGlobalWindowState" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates/><component><group oor:name="UIElements"><set oor:name="States" oor:node-type="WindowStateType" oor:component="org.openoffice.Office.UI.WindowState"/></group></component></oor:component-schema><oor:component-schema oor:name="WriterReportWindowState" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates/><component><group oor:name="UIElements"><set oor:name="States" oor:node-type="WindowStateType" oor:component="org.openoffice.Office.UI.WindowState"/></group></component></oor:component-schema><oor:component-schema oor:name="WriterWebWindowState" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates/><component><group oor:name="UIElements"><set oor:name="States" oor:node-type="WindowStateType" oor:component="org.openoffice.Office.UI.WindowState"/></group></component></oor:component-schema><oor:component-schema oor:name="WriterWindowState" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates/><component><group oor:name="UIElements"><set oor:name="States" oor:node-type="WindowStateType" oor:component="org.openoffice.Office.UI.WindowState"/></group></component></oor:component-schema><oor:component-schema oor:name="XFormsWindowState" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates/><component><group oor:name="UIElements"><set oor:name="States" oor:node-type="WindowStateType" oor:component="org.openoffice.Office.UI.WindowState"/></group></component></oor:component-schema><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="WriterCommands" oor:package="org.openoffice.Office.UI"><node oor:name="UserInterface"><node oor:name="Commands"><node oor:name=".uno:AddTextBox" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Add Text Box</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:RemoveTextBox" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Remove Text Box</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:EditGlossary" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">AutoTe~xt...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PrintLayout" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Normal View</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Normal</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertHeader" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Header</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertFooter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Footer</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ExpandGlossary" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Run AutoText Entry</value></prop></node><node oor:name=".uno:ShowHiddenParagraphs" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Field ~Hidden Paragraphs</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>8</value></prop></node><node oor:name=".uno:InsertScript" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">S~cript...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SetAnchorAtChar" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Anchor to Character</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">To ~Character</value></prop></node><node oor:name=".uno:PageOffsetDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Number</value></prop></node><node oor:name=".uno:InsertHeaderFooterMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">He~ader and Footer</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertPageHeader" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">He~ader</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertPageFooter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Foote~r</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PreviewZoom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Preview Zoom</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertEndnote" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Endnote</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Endnote</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TableNumberRecognition" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Recognition</value></prop></node><node oor:name=".uno:InsertSection" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Se~ction...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertMultiIndex" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Table of Contents</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Table of Contents, Index or Bibliography</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Table of Contents, ~Index or Bibliography...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertAuthoritiesEntry" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Bibliography Entry...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:ShadowCursor" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Toggle Direct Cursor Mode</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Direct Cursor Mode</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StartAutoCorrect" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">AutoCorrect</value></prop></node><node oor:name=".uno:FontColor" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Font Color</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:UpdateAllIndexes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Update All</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Indexes and ~Tables</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:UpdateCurIndex" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Update Index</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Current ~Index</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Update index</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:RemoveTableOf" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete index</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:RejectTrackedChange" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Reject</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Reject Track Change</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Reject Change</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:RejectTrackedChangeToNext" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Reject and Move to Next</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Reject Track Change and select the next one</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:RejectAllTrackedChanges" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Reject All</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Reject All Tracked Changes</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Reject All Changes</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AcceptTrackedChange" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Accept</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Accept Track Change</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Accept Change</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AcceptTrackedChangeToNext" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Accept and Move to Next</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Accept Track Change and select the next one</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AcceptAllTrackedChanges" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Accept All</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Accept All Tracked Changes</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Accept All Changes</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:NextTrackedChange" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Next</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Next Track Change</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PreviousTrackedChange" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Pr~evious</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Previous Track Change</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:UpdateAllLinks" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Links</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TrackChanges" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Record</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Record Track Changes</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TrackChangesBar" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Track Changes Functions</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Show Track Changes Functions</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ShowTrackedChanges" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Show</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Show Track Changes</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ViewTrackChanges" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Show ~Tracked Changes</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:ShowTrackedChanges</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ShowInlineTooltips" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">T~ooltips</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Show change authorship in tooltips</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:UseHeaderFooterMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Use header/footer menu</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Use the advanced popup menu to create header/footer on the fly</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ShowOutlineContentVisibilityButton" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Show outline content visibility button</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Show outline content visibility button</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop><prop oor:name="IsExperimental" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name=".uno:ShowChangesInMargin" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Show tracked deletions in margin</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Show tracked deletions in margin</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GotoPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Go t~o Page...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:CommentChangeTracking" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Comment...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Track Change Comment</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:UpdateAll" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Update All</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertEnvelope" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">En~velope...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AcceptTrackedChanges" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Manage...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Manage Track Changes</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:EditCurIndex" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Edit index</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AuthoritiesEntryDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Bibliography Entry...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:UpdateCharts" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Charts</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertBookmark" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bookmar~k...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Bookmark</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SetReminder" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Set Reminder</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Set Reminder</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertAnchor" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Anc~hor...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:InsertBookmark</value></prop></node><node oor:name=".uno:InsertPara" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Paragraph</value></prop></node><node oor:name=".uno:InsertBreak" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Manual ~Break...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertColumnBreak" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Column Break</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~More Fields...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:ChangeDatabaseField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Exchange Data~base...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertCaptionDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Caption...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Caption</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Caption...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertFootnoteDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">F~ootnote or Endnote...</value></prop></node><node oor:name=".uno:InsertReferenceField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cross-reference...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Cross-reference</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertHyperlinkDlg" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Hyperlink</value></prop></node><node oor:name=".uno:InsertLinebreak" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Manual Row Break</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertObjectDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Other Objects</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertPagebreak" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Page Break</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Page Break</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Insert ~Table...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertFrameInteract" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Frame Interactively</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Frame Interactively</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertFrame" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Frame</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">F~rame...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Frame</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertIndexesEntry" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Index Entry...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Index Entry</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertFrameInteractNoColumns" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert single-column frame manually</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ToggleAnchorType" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Change Anchor</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:AnchorMenu</value></prop></node><node oor:name=".uno:SetAnchorToPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Anchor To Page</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">To P~age</value></prop></node><node oor:name=".uno:SetAnchorToPara" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Anchor To Paragraph</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">To ~Paragraph</value></prop></node><node oor:name=".uno:ToggleObjectLayer" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Change Position</value></prop></node><node oor:name=".uno:MergeDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Mail Merge...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeWizard" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Mail Merge Wi~zard...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeFirstEntry" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">First Mail Merge Entry</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergePrevEntry" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Previous Mail Merge Entry</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeCurrentEntry" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Current Mail Merge Entry</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeNextEntry" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Next Mail Merge Entry</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeLastEntry" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Last Mail Merge Entry</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeExcludeEntry" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Exclude Mail Merge Entry</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeCreateDocuments" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Edit Individual Documents</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeSaveDocuments" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Save Merged Documents</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergePrintDocuments" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Print Merged Documents</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MailMergeEmailDocuments" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Send Email Messages</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SetAnchorToFrame" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Anchor To Frame</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">To ~Frame</value></prop></node><node oor:name=".uno:InsertObjectStarMath" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Formula...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Formula</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TextAttributes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Text Attributes...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SetAnchorToChar" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Anchor as Character</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">As C~haracter</value></prop></node><node oor:name=".uno:InsertCtrl" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertObjCtrl" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Object</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:FormatObjectMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Text Box and Shap~e</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertFieldCtrl" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fiel~d</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Field</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:InsertDateField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Date</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:NewGlobalDoc" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Create Master ~Document</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertTimeField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Time</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertPageNumberField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Page Number</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertPageCountField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page ~Count</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertTopicField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Subject</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertTitleField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">T~itle</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertAuthorField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">First ~Author</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertFootnote" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Footnote</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Insert Footnote</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AutoFormatApply" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Appl~y</value></prop></node><node oor:name=".uno:OnlineAutoFormat" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~While Typing</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>8</value></prop></node><node oor:name=".uno:StatePageNumber" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Number</value></prop></node><node oor:name=".uno:PageStyleName" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Style</value></prop></node><node oor:name=".uno:AutoFormatRedlineApply" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Apply and Edit ~Changes</value></prop></node><node oor:name=".uno:SelectionMode" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Se~lection Mode</value></prop></node><node oor:name=".uno:ExecHyperlinks" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Hyperlinks Active</value></prop></node><node oor:name=".uno:SuperScript" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Superscript</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:SubScript" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Subscript</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:CharLeftSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Character Left</value></prop></node><node oor:name=".uno:CharRightSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Character Right</value></prop></node><node oor:name=".uno:LineUpSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Top Line</value></prop></node><node oor:name=".uno:LineDownSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Down</value></prop></node><node oor:name=".uno:StartOfLineSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Begin of Line</value></prop></node><node oor:name=".uno:EndOfLineSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to End of Line</value></prop></node><node oor:name=".uno:StartOfDocumentSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Document Begin</value></prop></node><node oor:name=".uno:EndOfDocumentSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Document End</value></prop></node><node oor:name=".uno:GoToStartOfNextPageSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Begin of Next Page</value></prop></node><node oor:name=".uno:GoToEndOfNextPageSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to End of Next Page</value></prop></node><node oor:name=".uno:GoToStartOfPrevPageSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Begin of Previous Page</value></prop></node><node oor:name=".uno:GoToEndOfPrevPageSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to End of Previous Page</value></prop></node><node oor:name=".uno:GoToStartOfPageSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Page Begin</value></prop></node><node oor:name=".uno:SendOutlineToStarImpress" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Outline to ~Presentation</value></prop></node><node oor:name=".uno:GoToEndOfPageSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Page End</value></prop></node><node oor:name=".uno:SendOutlineToClipboard" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Outline to ~Clipboard</value></prop></node><node oor:name=".uno:RotateLeft" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Rotate 90° ~Left</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:RotateRight" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Rotate 90° ~Right</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Rotate180" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Rotate 1~80°</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:RotateReset" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Reset R~otation</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:NewHtmlDoc" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Create ~HTML Document</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:StartOfParaSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Paragraph Begin</value></prop></node><node oor:name=".uno:EndOfParaSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Paragraph End</value></prop></node><node oor:name=".uno:WordRightSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to End of Word</value></prop></node><node oor:name=".uno:WordLeftSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Begin of Word</value></prop></node><node oor:name=".uno:GotoNextSentenceSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Next Sentence</value></prop></node><node oor:name=".uno:GotoPrevSentenceSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Previous Sentence</value></prop></node><node oor:name=".uno:PageUpSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Previous Page</value></prop></node><node oor:name=".uno:PageDownSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select to Next Page</value></prop></node><node oor:name=".uno:JumpToNextRegion" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Section</value></prop></node><node oor:name=".uno:JumpToPrevRegion" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Section</value></prop></node><node oor:name=".uno:TableNumberFormatDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Format...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:LoadStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Load Styles...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:CreateAbstract" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Create A~utoAbstract...</value></prop></node><node oor:name=".uno:SendAbstractToStarImpress" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">AutoAbst~ract to Presentation...</value></prop></node><node oor:name=".uno:BorderDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Borders</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PageColumnDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Columns</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PageColumnType" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Columns</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:BackgroundDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Background</value></prop></node><node oor:name=".uno:PageDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Page Style...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TitlePageDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Title Page...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:FormatColumns" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Co~lumns...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:FormatDropcap" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Drop Caps</value></prop></node><node oor:name=".uno:FrameDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Frame or Object Properties</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Properties...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GraphicDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Image Properties</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Properties...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TableDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Ta~ble Properties...</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Properties...</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">~Table Properties...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:FootnoteDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Footnotes and Endnotes...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:CurrentFootnoteDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Footnotes and Endnotes...</value></prop></node><node oor:name=".uno:ResetAttributes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Clear</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Clear ~Direct Formatting</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Clear Direct Formatting</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:WrapOff" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">None</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>15</value></prop></node><node oor:name=".uno:WrapOn" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Parallel</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:ShowTwoPages" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Two Pages Preview</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ShowSinglePage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Single Page Preview</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:WrapThrough" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Through</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:ShowMultiplePages" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Multiple Pages Preview</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PrintPagePreview" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Print document</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AlignLeft" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align Left</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ClosePreview" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Close Preview</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AlignRight" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align Right</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AlignHorizontalCenter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Center Horizontal</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AlignTop" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align Top to Anchor</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AlignBottom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align Bottom to Anchor</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AlignVerticalCenter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align Middle to Anchor</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PageStyleApply" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Apply Page Style</value></prop></node><node oor:name=".uno:FieldDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">F~ields...</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Edit F~ields...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:LinkDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Lin~ks to External Files...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ConvertTableText" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Te~xt &lt;-&gt; Table...</value></prop></node><node oor:name=".uno:ConvertTableToText" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">T~able to Text...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ConvertTextToTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Text to Table...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:HeadingRowsRepeat" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Header Rows Repeat Across Pages</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TableSort" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">So~rt...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertRowDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Rows...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertRowsBefore" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Rows Above</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Rows ~Above</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertRowsAfter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Rows Below</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Rows ~Below</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertColumnDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Columns...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertColumnsBefore" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Columns Before</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Columns ~Before</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:InsertColumnsAfter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Columns After</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Columns ~After</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PasteSpecial" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Paste ~Special...</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">~More Options...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PasteNestedTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Paste as Nested Table</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">~Nested Table</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PasteRowsBefore" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Paste as Rows Above</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Rows ~Above</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PasteColumnsBefore" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Paste as Columns Before</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">Columns ~Before</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:DeleteRows" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete Rows</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Rows</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Delete selected rows</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:DeleteColumns" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete Columns</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Columns</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Delete selected columns</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:DeleteTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete Table</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Table</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Delete table</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SplitCell" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Split Cells...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MergeCells" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Merge Cells</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SetRowHeight" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Row Height...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SetColumnWidth" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Column Width...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:OptimizeTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Optimize Size</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:BulletsAndNumberingDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Bullets and Numbering...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:GoLeft" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Character Left</value></prop></node><node oor:name=".uno:IndexEntryDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Index Entry...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoRight" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Go Right</value></prop></node><node oor:name=".uno:EntireRow" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Row</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Row</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:EntireCell" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Cell</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">C~ell</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Select Cell</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoUp" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Top Line</value></prop></node><node oor:name=".uno:EntireColumn" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Column</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Column</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:UpdateFields" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Fields</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SelectTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Table</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Table</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Select Table</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoDown" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Line Below</value></prop></node><node oor:name=".uno:GoToStartOfLine" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Line Begin</value></prop></node><node oor:name=".uno:ExecuteMacroField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Run Macro Field</value></prop></node><node oor:name=".uno:Protect" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Protect Cells</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToEndOfLine" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To End of Line</value></prop></node><node oor:name=".uno:InsertFormula" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fo~rmula</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToStartOfDoc" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Document Begin</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Calc" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Calculate Table</value></prop></node><node oor:name=".uno:UnsetCellsReadOnly" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Unprotect Cells</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToEndOfDoc" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Document End</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:DecrementLevel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Demote One Level</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:GoToStartOfNextPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Begin of Next Page</value></prop></node><node oor:name=".uno:IncrementLevel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Promote One Level</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:SetOptimalColumnWidth" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Optimal Column Width</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:JumpUpThisLevel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Paragraph in Level</value></prop></node><node oor:name=".uno:GoToEndOfNextPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To End of Next Page</value></prop></node><node oor:name=".uno:GoToStartOfPrevPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Begin of Previous Page</value></prop></node><node oor:name=".uno:JumpDownThisLevel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Paragraph in Level</value></prop></node><node oor:name=".uno:MoveUp" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Move Up</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:GoToEndOfPrevPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To End of Previous Page</value></prop></node><node oor:name=".uno:MoveDown" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Move Down</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:GoToStartOfPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Page Begin</value></prop></node><node oor:name=".uno:GoToEndOfPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Page End</value></prop></node><node oor:name=".uno:InsertNeutralParagraph" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Insert Unnumbered Entry</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:RemoveBullets" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Numbering Off</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:SetOptimalRowHeight" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Optimal Row Height</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToStartOfColumn" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Column Begin</value></prop></node><node oor:name=".uno:DecrementSubLevels" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Demote One Level With Subpoints</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:GoToEndOfColumn" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Column End</value></prop></node><node oor:name=".uno:IncrementSubLevels" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Promote One Level With Subpoints</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:MoveUpSubItems" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Move Up with Subpoints</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:GoToStartOfPara" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Paragraph Begin</value></prop></node><node oor:name=".uno:MoveDownSubItems" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Move Down with Subpoints</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:GoToEndOfPara" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Paragraph End</value></prop></node><node oor:name=".uno:UpdateInputFields" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Update Input Fields</value></prop></node><node oor:name=".uno:GoToNextWord" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Word Right</value></prop></node><node oor:name=".uno:GoToPrevWord" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Word Left</value></prop></node><node oor:name=".uno:GoToNextSentence" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Sentence</value></prop></node><node oor:name=".uno:NumberOrNoNumber" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Numbering On/Off</value></prop></node><node oor:name=".uno:GoToPrevSentence" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Sentence</value></prop></node><node oor:name=".uno:GotoNextInputField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Input Field</value></prop></node><node oor:name=".uno:SwBackspace" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Backspace</value></prop></node><node oor:name=".uno:GotoPrevInputField" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Input Field</value></prop></node><node oor:name=".uno:DelToEndOfSentence" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete to End of Sentence</value></prop></node><node oor:name=".uno:RepeatSearch" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Repeat Search</value></prop></node><node oor:name=".uno:DelToStartOfSentence" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete to Start of Sentence</value></prop></node><node oor:name=".uno:DelToEndOfWord" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete to End of Word</value></prop></node><node oor:name=".uno:DelToStartOfWord" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete to Start of Word</value></prop></node><node oor:name=".uno:DelToEndOfLine" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete to End of Line</value></prop></node><node oor:name=".uno:DelToStartOfLine" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete to Start of Line</value></prop></node><node oor:name=".uno:DelToEndOfPara" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete to End of Paragraph</value></prop></node><node oor:name=".uno:DelToStartOfPara" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete to Start of Paragraph</value></prop></node><node oor:name=".uno:DelLine" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Delete Row</value></prop></node><node oor:name=".uno:PageUp" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Previous Page</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:JumpToSpecificPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Jump To Specific Page</value></prop></node><node oor:name=".uno:PageDown" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Next Page</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToPrevPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Page</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">To Previous Page</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToNextPage" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Page</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">To Next Page</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SetMultiSelection" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">MultiSelection On</value></prop></node><node oor:name=".uno:Repaginate" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Formatting</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SetExtSelection" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Extended Selection On</value></prop></node><node oor:name=".uno:EditFootnote" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Footnote or Endnote...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Escape" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cancel</value></prop></node><node oor:name=".uno:ShiftBackspace" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Shift+Backspace</value></prop></node><node oor:name=".uno:SelectWord" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Word</value></prop></node><node oor:name=".uno:SelectSentence" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Sentence</value></prop></node><node oor:name=".uno:NumberFormatStandard" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Format: Standard</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:EditRegion" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Sections...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:EditCurrentRegion" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Edit Section...</value></prop></node><node oor:name=".uno:JumpToReference" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Reference</value></prop></node><node oor:name=".uno:GotoNextObject" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Object</value></prop></node><node oor:name=".uno:NumberFormatDecimal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Format: Decimal</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GotoPrevObject" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Object</value></prop></node><node oor:name=".uno:NumberFormatScientific" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Format: Exponential</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:JumpToNextBookmark" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Bookmark</value></prop></node><node oor:name=".uno:NumberFormatDate" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Format: Date</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:JumpToPrevBookmark" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Bookmark</value></prop></node><node oor:name=".uno:GoToStartOfTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Table Begin</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ResetTableProtection" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Unprotect sheet</value></prop></node><node oor:name=".uno:NumberFormatTime" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Format: Time</value></prop></node><node oor:name=".uno:GoToEnd" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Table End</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:NumberFormatCurrency" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Format: Currency</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:JumpToNextTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Table</value></prop></node><node oor:name=".uno:NumberFormatPercent" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number Format: Percent</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:JumpToPrevTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Table</value></prop></node><node oor:name=".uno:GoToStartOfNextColumn" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Begin of Next Column</value></prop></node><node oor:name=".uno:WrapIdeal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Optimal</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToEndOfNextColumn" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To End of Next Column</value></prop></node><node oor:name=".uno:WrapThroughTransparent" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">In ~Background</value></prop></node><node oor:name=".uno:WrapThroughTransparencyToggle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">In ~Background</value></prop></node><node oor:name=".uno:GoToStartOfPrevColumn" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Begin of Previous Column</value></prop></node><node oor:name=".uno:GoToEndOfPrevColumn" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Column</value></prop></node><node oor:name=".uno:AlignRowTop" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align to Top of Line</value></prop></node><node oor:name=".uno:JumpToFootnoteOrAnchor" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Footnote Anchor</value></prop></node><node oor:name=".uno:AlignRowBottom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align to Bottom of Line</value></prop></node><node oor:name=".uno:JumpToNextFootnote" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Footnote</value></prop></node><node oor:name=".uno:AlignVerticalRowCenter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align to Vertical Center of Line</value></prop></node><node oor:name=".uno:JumpToPrevFootnote" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Footnote</value></prop></node><node oor:name=".uno:AlignCharTop" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align to Top of Character</value></prop></node><node oor:name=".uno:JumpToNextFrame" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Frame</value></prop></node><node oor:name=".uno:ChainFrames" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Link Frames</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:AlignCharBottom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align to Bottom of Character</value></prop></node><node oor:name=".uno:UnhainFrames" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Unlink Frames</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToAnchor" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Set Cursor To Anchor</value></prop></node><node oor:name=".uno:AlignVerticalCharCenter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Align to Vertical Center of Character</value></prop></node><node oor:name=".uno:NumberingStart" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Restart Numbering</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>15</value></prop></node><node oor:name=".uno:JumpToHeader" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Header</value></prop></node><node oor:name=".uno:WrapLeft" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Before</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:JumpToFooter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Footer</value></prop></node><node oor:name=".uno:WrapRight" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">After</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MirrorOnEvenPages" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Mirror Object on Even Pages</value></prop></node><node oor:name=".uno:IndexMarkToIndex" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Index Mark to Index</value></prop></node><node oor:name=".uno:JumpToFootnoteArea" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Edit Footnote/Endnote</value></prop></node><node oor:name=".uno:MirrorGraphicOnEvenPages" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Flip Images on Even Pages</value></prop></node><node oor:name=".uno:SplitTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Split Table...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:WrapAnchorOnly" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wrap First Paragraph</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~First Paragraph</value></prop></node><node oor:name=".uno:SetMinimalColumnWidth" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Minimize Column Width</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:DistributeColumns" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Distribute Columns Evenly</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:IncrementIndentValue" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Increment Indent Value</value></prop></node><node oor:name=".uno:SetMinimalRowHeight" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Minimize Row Height</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:DistributeRows" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Distribute Rows Evenly</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:WrapContour" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wrap Contour On</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Contour</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>7</value></prop></node><node oor:name=".uno:DecrementIndentValue" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Decrement Indent Value</value></prop></node><node oor:name=".uno:MergeTable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Merge Table</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:GoToPrevPara" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Paragraph</value></prop></node><node oor:name=".uno:RowSplit" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Row to ~Break Across Pages</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>8</value></prop></node><node oor:name=".uno:SelectText" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Paragraph</value></prop></node><node oor:name=".uno:GoToNextPara" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Paragraph</value></prop></node><node oor:name=".uno:GotoNextPlacemarker" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Next Placeholder</value></prop></node><node oor:name=".uno:GotoPrevPlacemarker" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To Previous Placeholder</value></prop></node><node oor:name=".uno:TableModeFix" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Table: Fixed</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:JumpToStartOfDoc" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Directly to Document Begin</value></prop></node><node oor:name=".uno:TableModeFixProp" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Table: Fixed, Proportional</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:RefreshView" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Restore View</value></prop></node><node oor:name=".uno:JumpToEndOfDoc" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Directly to Document End</value></prop></node><node oor:name=".uno:TableModeVariable" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Table: Variable</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TextWrap" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Text Wrap...</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Edit...</value></prop></node><node oor:name=".uno:GotoNextIndexMark" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Go to Next Index Mark</value></prop></node><node oor:name=".uno:GotoPrevIndexMark" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Go to Previous Index Mark</value></prop></node><node oor:name=".uno:GotoNextTableFormula" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Go to next table formula</value></prop></node><node oor:name=".uno:GotoPrevTableFormula" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Go to previous table formula</value></prop></node><node oor:name=".uno:GotoNextWrongTableFormula" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Go to next faulty table formula</value></prop></node><node oor:name=".uno:GotoPrevWrongTableFormula" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Go to previous faulty table formula</value></prop></node><node oor:name=".uno:SelectTextMode" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Select Text</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Ruler" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Rulers</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:LineNumberingDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Line Numbering...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ShowResolvedAnnotations" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Show resolved comme~nts</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Resolved Comments</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ShowGraphics" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">View Images and Charts</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">~Images and Charts</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:CharColorExt" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Font Color Fill</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>8</value></prop></node><node oor:name=".uno:ViewBounds" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Te~xt Boundaries</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ThesaurusDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Thesaurus...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:BackColor" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Highlight Color</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Fields" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fields</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:CharBackgroundExt" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Highlight Fill</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>8</value></prop></node><node oor:name=".uno:VRuler" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Vertical Ruler</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Hyphenate" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Hyphenation...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:VScroll" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Vertical Scroll Bar</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AddAllUnknownWords" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Add Unknown Words</value></prop></node><node oor:name=".uno:HScroll" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Horizontal Scroll Bar</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ChapterNumberingDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Chapter ~Numbering...</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Set Chapter Numbering</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ControlCodes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Formatting Marks</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">For~matting Marks</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Toggle Formatting Marks</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:HideWhitespace" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Hide Whitespac~e</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ShowWhitespace" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Show Whitespac~e</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:SortDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">So~rt...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Marks" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fie~ld Shadings</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:CalculateSel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Calculat~e</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Fieldnames" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Field Names</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TableBoundaries" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Table Boundaries</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ShowBookview" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Book Preview</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:RemoveDirectCharFormats" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Remove Direct Character Formats</value></prop></node><node oor:name=".uno:SendMailDocAsMS" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Email as ~Microsoft Word...</value></prop></node><node oor:name=".uno:SendMailDocAsOOo" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Email as ~OpenDocument Text...</value></prop></node><node oor:name=".uno:SelectionModeDefault" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Standard</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>8</value></prop></node><node oor:name=".uno:SelectionModeBlock" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Block Area</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>8</value></prop></node><node oor:name=".uno:OpenFromWriter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Open...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AutoCorrectDlg?OpenSmartTag:bool=true" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Smart ~Tag Options...</value></prop></node><node oor:name=".uno:FormatPaintbrush" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Clone</value></prop><prop oor:name="ContextLabel" oor:type="xs:string"><value xml:lang="en-US">Clone Formatting</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Clone Formatting (double click and Ctrl or Cmd to alter behavior)</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node></node><node oor:name="Popups"><node oor:name=".uno:CharacterMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">C~haracter</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ParagraphMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">P~aragraph</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:NumberingMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Bullets and Numbering</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TableSelectMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Select</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TableConvertMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Convert</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:UpdateMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Update</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:IndexesMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Table of Contents and Inde~x</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:FormatAllNotes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Comments...</value></prop><prop oor:name="PopupLabel" oor:type="xs:string"><value xml:lang="en-US">~Format All Comments...</value></prop></node><node oor:name=".uno:WordCountDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Word Count...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AccessibilityCheck" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Accessibility Check...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:StylesMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Styl~es</value></prop></node><node oor:name=".uno:WrapMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Wrap</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ArrangeFrameMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Arrange</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AutoFormatMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">AutoCorr~ect</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PageSettingDialog" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Settings - Paper format</value></prop></node><node oor:name=".uno:SelectionModeMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Selection Mode</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ContinueNumbering" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Continue previous numbering</value></prop></node><node oor:name=".uno:NavElement" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Navigate By</value></prop></node><node oor:name=".uno:ScrollToPrevious" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Previous Element</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ScrollToNext" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Next Element</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:NavigateBack" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Back</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:NavigateForward" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Forward</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Horizontal Line&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Horizontal ~Line</value></prop></node><node oor:name=".uno:HorizontalLine" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Horizontal ~Line</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Horizontal Line&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Standard&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Default ~Paragraph</value></prop></node><node oor:name=".uno:DefaultParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Default ~Paragraph</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Default Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Standard&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Title&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Title</value></prop></node><node oor:name=".uno:TitleParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Title</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Title Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Title&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Subtitle&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Su~btitle</value></prop></node><node oor:name=".uno:SubtitleParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Su~btitle</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Subtitle Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Subtitle&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Heading 1&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~1</value></prop></node><node oor:name=".uno:Heading1ParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~1</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Heading 1 Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Heading 1&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Heading 2&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~2</value></prop></node><node oor:name=".uno:Heading2ParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~2</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Heading 2 Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Heading 2&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Heading 3&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~3</value></prop></node><node oor:name=".uno:Heading3ParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~3</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Heading 3 Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Heading 3&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Heading 4&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~4</value></prop></node><node oor:name=".uno:Heading4ParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~4</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Heading 4 Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Heading 4&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Heading 5&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~5</value></prop></node><node oor:name=".uno:Heading5ParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~5</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Heading 5 Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Heading 5&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Heading 6&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~6</value></prop></node><node oor:name=".uno:Heading6ParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heading ~6</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Heading 6 Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Heading 6&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Quotations&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Quotations</value></prop></node><node oor:name=".uno:QuoteParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Quotations</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Quotations Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Quotations&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Preformatted Text&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Pre~formatted Text</value></prop></node><node oor:name=".uno:PreformattedParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Pre~formatted Text</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Preformatted Text Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Preformatted Text&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Text body&amp;FamilyName:string=ParagraphStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Text Body</value></prop></node><node oor:name=".uno:TextBodyParaStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Text Body</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Text Body Paragraph Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Text body&amp;FamilyName:string=ParagraphStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Standard&amp;FamilyName:string=CharacterStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Default ~Character</value></prop></node><node oor:name=".uno:DefaultCharStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Default ~Character</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Default Character Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Standard&amp;FamilyName:string=CharacterStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Emphasis&amp;FamilyName:string=CharacterStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">E~mphasis</value></prop></node><node oor:name=".uno:EmphasisCharStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">E~mphasis</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Emphasis Character Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Emphasis&amp;FamilyName:string=CharacterStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Strong Emphasis&amp;FamilyName:string=CharacterStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Strong Emphasis</value></prop></node><node oor:name=".uno:StrongEmphasisCharStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Strong Emphasis</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Strong Emphasis Character Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Strong Emphasis&amp;FamilyName:string=CharacterStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Citation&amp;FamilyName:string=CharacterStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Qu~otation</value></prop></node><node oor:name=".uno:QuoteCharStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Qu~otation</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Quotation Character Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Citation&amp;FamilyName:string=CharacterStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Source Text&amp;FamilyName:string=CharacterStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Sou~rce Text</value></prop></node><node oor:name=".uno:SourceCharStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Sou~rce Text</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Source Text Character Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Source Text&amp;FamilyName:string=CharacterStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>9</value></prop></node><node oor:name=".uno:StyleApply?Style:string=List 1&amp;FamilyName:string=NumberingStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bullet List</value></prop></node><node oor:name=".uno:BulletListStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bullet List</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Bullet List Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=List 1&amp;FamilyName:string=NumberingStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>11</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Numbering 123&amp;FamilyName:string=NumberingStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number List</value></prop></node><node oor:name=".uno:NumberListStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Number List</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Number List Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Numbering 123&amp;FamilyName:string=NumberingStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>11</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Numbering ABC&amp;FamilyName:string=NumberingStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Alphabet Uppercase List</value></prop></node><node oor:name=".uno:AlphaListStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Alphabet Uppercase List</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Alphabet Uppercase List Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Numbering ABC&amp;FamilyName:string=NumberingStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>11</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Numbering abc&amp;FamilyName:string=NumberingStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Alphabet Lowercase List</value></prop></node><node oor:name=".uno:AlphaLowListStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Alphabet Lowercase List</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Alphabet Lowercase List Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Numbering abc&amp;FamilyName:string=NumberingStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>11</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Numbering IVX&amp;FamilyName:string=NumberingStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Roman Uppercase List</value></prop></node><node oor:name=".uno:RomanListStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Roman Uppercase List</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Roman Uppercase List Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Numbering IVX&amp;FamilyName:string=NumberingStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>11</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Numbering ivx&amp;FamilyName:string=NumberingStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Roman Lowercase List</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Default Style&amp;FamilyName:string=TableStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Default Style</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Academic&amp;FamilyName:string=TableStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Academic</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Elegant&amp;FamilyName:string=TableStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Elegant</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Financial&amp;FamilyName:string=TableStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Financial</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Box List Blue&amp;FamilyName:string=TableStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Box List Blue</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Box List Green&amp;FamilyName:string=TableStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Box List Green</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Box List Red&amp;FamilyName:string=TableStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Box List Red</value></prop></node><node oor:name=".uno:StyleApply?Style:string=Box List Yellow&amp;FamilyName:string=TableStyles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Box List Yellow</value></prop></node><node oor:name=".uno:RomanLowListStyle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Roman Lowercase List</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Roman Lowercase List Style</value></prop><prop oor:name="TargetURL" oor:type="xs:string"><value>.uno:StyleApply?Style:string=Numbering ivx&amp;FamilyName:string=NumberingStyles</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>11</value></prop></node><node oor:name=".uno:StyleApply" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Paragraph Style</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Set Paragraph Style</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Orientation" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Orientation</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:AttributePageSize" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Size</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:PageMargin" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Page Margins</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:HangingIndent" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Hanging Indent</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:Watermark" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Watermark...</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:TableCellBackgroundColor" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Table Cell Background Color</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:MSCompatActiveXControls" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">ActiveX Controls</value></prop></node><node oor:name=".uno:MSCompatLegacyControls" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Legacy Form Fields</value></prop></node><node oor:name=".uno:MSCompatContentControls" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Content Controls</value></prop></node><node oor:name=".uno:ProtectMenu" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">~Protect Document</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ProtectFields" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Protect Fields</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Protect fields in current document</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ProtectBookmarks" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Protect Bookmarks</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Protect bookmarks in current document</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop></node><node oor:name=".uno:ToggleOutlineContentVisibility" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Toggle Outline Content Visibility</value></prop><prop oor:name="TooltipLabel" oor:type="xs:string"><value xml:lang="en-US">Fold or unfold outline content in document</value></prop><prop oor:name="Properties" oor:type="xs:int"><value>1</value></prop><prop oor:name="IsExperimental" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name=".uno:InspectorDeck" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Inspector Deck</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="WriterFormWindowState" oor:package="org.openoffice.Office.UI"><node oor:name="UIElements"><node oor:name="States"><node oor:name="private:resource/popupmenu/annotation" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Comment</value></prop></node><node oor:name="private:resource/popupmenu/draw" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape</value></prop></node><node oor:name="private:resource/popupmenu/drawtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape Text</value></prop></node><node oor:name="private:resource/popupmenu/form" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Control</value></prop></node><node oor:name="private:resource/popupmenu/formrichtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop></node><node oor:name="private:resource/popupmenu/frame" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Frame</value></prop></node><node oor:name="private:resource/popupmenu/graphic" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop></node><node oor:name="private:resource/popupmenu/media" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media</value></prop></node><node oor:name="private:resource/popupmenu/oleobject" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE Object</value></prop></node><node oor:name="private:resource/popupmenu/preview" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop></node><node oor:name="private:resource/popupmenu/table" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop></node><node oor:name="private:resource/popupmenu/text" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text</value></prop></node><node oor:name="private:resource/toolbar/standardbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/textobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Formatting</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/mailmerge" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Mail Merge</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/toolbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Tools</value></prop></node><node oor:name="private:resource/toolbar/tableobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/numobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Bullets and Numbering</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawingobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing Object Properties</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/alignmentbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Align Objects</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/bezierobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Edit Points</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/extrusionobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">3D-Settings</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formtextobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsfilterbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Filter</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsnavigationbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Navigation</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formcontrols" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Controls</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>2</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formdesign" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Design</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/frameobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Frame</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fullscreenbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Style" oor:type="xs:int"><value>2</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Full Screen</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graffilterbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image Filter</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graphicobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/insertbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert</value></prop></node><node oor:name="private:resource/toolbar/insertobjectbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert Object</value></prop></node><node oor:name="private:resource/toolbar/oleobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE-Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/optimizetablebar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Optimize</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/previewobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawtextobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/viewerbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard (Viewing Mode)</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>1,0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing</value></prop></node><node oor:name="private:resource/toolbar/mediaobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media Playback</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/colorbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Color</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/basicshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Basic Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/arrowshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Block Arrows</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/flowchartshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Flowchart</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/starshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Stars and Banners</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/symbolshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Symbol Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/calloutshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Callouts</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkshapetype" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork Shape</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="WriterGlobalWindowState" oor:package="org.openoffice.Office.UI"><node oor:name="UIElements"><node oor:name="States"><node oor:name="private:resource/popupmenu/annotation" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Comment</value></prop></node><node oor:name="private:resource/popupmenu/draw" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape</value></prop></node><node oor:name="private:resource/popupmenu/drawtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape Text</value></prop></node><node oor:name="private:resource/popupmenu/form" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Control</value></prop></node><node oor:name="private:resource/popupmenu/formrichtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop></node><node oor:name="private:resource/popupmenu/frame" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Frame</value></prop></node><node oor:name="private:resource/popupmenu/graphic" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop></node><node oor:name="private:resource/popupmenu/media" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media</value></prop></node><node oor:name="private:resource/popupmenu/oleobject" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE Object</value></prop></node><node oor:name="private:resource/popupmenu/preview" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop></node><node oor:name="private:resource/popupmenu/table" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop></node><node oor:name="private:resource/popupmenu/text" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text</value></prop></node><node oor:name="private:resource/toolbar/standardbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/findbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Find</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/textobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Formatting</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/toolbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Tools</value></prop></node><node oor:name="private:resource/toolbar/tableobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,3</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/numobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,4</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Bullets and Numbering</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawingobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing Object Properties</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/alignmentbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Align Objects</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/bezierobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Edit Points</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/extrusionobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">3D-Settings</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formtextobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsfilterbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Filter</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsnavigationbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Navigation</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formcontrols" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Controls</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/formdesign" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Design</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/frameobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Frame</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fullscreenbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Style" oor:type="xs:int"><value>2</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Full Screen</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graffilterbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image Filter</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graphicobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>1,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/insertbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert</value></prop></node><node oor:name="private:resource/toolbar/oleobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE-Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/optimizetablebar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Optimize</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/previewobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawtextobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/viewerbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard (Viewing Mode)</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing</value></prop></node><node oor:name="private:resource/toolbar/mediaobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media Playback</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/colorbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Color</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/basicshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Basic Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/arrowshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Block Arrows</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/flowchartshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Flowchart</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/starshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Stars and Banners</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/symbolshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Symbol Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/calloutshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Callouts</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkshapetype" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork Shape</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/navigationobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Navigation</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/changes" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Track Changes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/textstylebar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Formatting (Styles)</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="WriterReportWindowState" oor:package="org.openoffice.Office.UI"><node oor:name="UIElements"><node oor:name="States"><node oor:name="private:resource/popupmenu/annotation" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Comment</value></prop></node><node oor:name="private:resource/popupmenu/draw" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape</value></prop></node><node oor:name="private:resource/popupmenu/drawtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape Text</value></prop></node><node oor:name="private:resource/popupmenu/form" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Control</value></prop></node><node oor:name="private:resource/popupmenu/formrichtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop></node><node oor:name="private:resource/popupmenu/frame" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Frame</value></prop></node><node oor:name="private:resource/popupmenu/graphic" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop></node><node oor:name="private:resource/popupmenu/media" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media</value></prop></node><node oor:name="private:resource/popupmenu/oleobject" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE Object</value></prop></node><node oor:name="private:resource/popupmenu/preview" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop></node><node oor:name="private:resource/popupmenu/table" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop></node><node oor:name="private:resource/popupmenu/text" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text</value></prop></node><node oor:name="private:resource/toolbar/standardbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/textobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Formatting</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/mailmerge" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Mail Merge</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/toolbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Tools</value></prop></node><node oor:name="private:resource/toolbar/tableobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/numobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Bullets and Numbering</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawingobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing Object Properties</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/alignmentbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Align Objects</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/bezierobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Edit Points</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/extrusionobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">3D-Settings</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formtextobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsfilterbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Filter</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsnavigationbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Navigation</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formcontrols" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Controls</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>2</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formdesign" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Design</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/frameobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Frame</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fullscreenbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Style" oor:type="xs:int"><value>2</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Full Screen</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graffilterbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image Filter</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graphicobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/insertbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert</value></prop></node><node oor:name="private:resource/toolbar/insertobjectbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert Object</value></prop></node><node oor:name="private:resource/toolbar/oleobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE-Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/optimizetablebar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Optimize</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/previewobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawtextobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/viewerbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard (Viewing Mode)</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>1,0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing</value></prop></node><node oor:name="private:resource/toolbar/mediaobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media Playback</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/colorbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Color</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/basicshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Basic Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/arrowshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Block Arrows</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/flowchartshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Flowchart</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/starshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Stars and Banners</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/symbolshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Symbol Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/calloutshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Callouts</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkshapetype" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork Shape</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="WriterWebWindowState" oor:package="org.openoffice.Office.UI"><node oor:name="UIElements"><node oor:name="States"><node oor:name="private:resource/popupmenu/annotation" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Comment</value></prop></node><node oor:name="private:resource/popupmenu/form" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Control</value></prop></node><node oor:name="private:resource/popupmenu/formrichtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop></node><node oor:name="private:resource/popupmenu/frame" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Frame</value></prop></node><node oor:name="private:resource/popupmenu/graphic" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop></node><node oor:name="private:resource/popupmenu/oleobject" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE Object</value></prop></node><node oor:name="private:resource/popupmenu/preview" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop></node><node oor:name="private:resource/popupmenu/source" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">HTML Source</value></prop></node><node oor:name="private:resource/popupmenu/table" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop></node><node oor:name="private:resource/popupmenu/text" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text</value></prop></node><node oor:name="private:resource/toolbar/standardbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/findbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Find</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/textobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Formatting</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/toolbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Tools</value></prop></node><node oor:name="private:resource/toolbar/oleobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE-Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/tableobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/frameobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Frame</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graphicobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawtextobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawingobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing Object Properties</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/bezierobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Edit Points</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkshapetype" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork Shape</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formtextobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsfilterbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Filter</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsnavigationbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Navigation</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formcontrols" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Controls</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/formdesign" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Design</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/fullscreenbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Style" oor:type="xs:int"><value>2</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Full Screen</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graffilterbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image Filter</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/insertbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/numobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,3</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Bullets and Numbering</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/previewobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/viewerbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard (Viewing Mode)</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/mediaobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media Playback</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/colorbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Color</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/basicshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Basic Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/arrowshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Block Arrows</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/flowchartshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Flowchart</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/starshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Stars and Banners</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/symbolshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Symbol Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/calloutshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Callouts</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="WriterWindowState" oor:package="org.openoffice.Office.UI"><node oor:name="UIElements"><node oor:name="States"><node oor:name="private:resource/popupmenu/annotation" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Comment</value></prop></node><node oor:name="private:resource/popupmenu/draw" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape</value></prop></node><node oor:name="private:resource/popupmenu/drawtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape Text</value></prop></node><node oor:name="private:resource/popupmenu/form" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Control</value></prop></node><node oor:name="private:resource/popupmenu/formrichtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop></node><node oor:name="private:resource/popupmenu/frame" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Frame</value></prop></node><node oor:name="private:resource/popupmenu/graphic" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop></node><node oor:name="private:resource/popupmenu/media" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media</value></prop></node><node oor:name="private:resource/popupmenu/notebookbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Notebookbar</value></prop></node><node oor:name="private:resource/popupmenu/oleobject" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE Object</value></prop></node><node oor:name="private:resource/popupmenu/preview" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop></node><node oor:name="private:resource/popupmenu/table" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop></node><node oor:name="private:resource/popupmenu/text" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text</value></prop></node><node oor:name="private:resource/toolbar/standardbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/notebookbarshortcuts" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Notebookbar shortcuts</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/findbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Find</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/textobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Formatting</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/mailmerge" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Mail Merge</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/toolbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Tools</value></prop></node><node oor:name="private:resource/toolbar/linesbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Curves and Polygons</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/arrowsbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Lines and Arrows</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/classificationbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">TSCP Classification</value></prop></node><node oor:name="private:resource/toolbar/tableobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,3</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/numobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,4</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Bullets and Numbering</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawingobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing Object Properties</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/alignmentbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Align Objects</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/bezierobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Edit Points</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/extrusionobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">3D-Settings</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formtextobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsfilterbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Filter</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsnavigationbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Navigation</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formcontrols" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Controls</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/formdesign" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Design</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/frameobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Frame</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fullscreenbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Style" oor:type="xs:int"><value>2</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Full Screen</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graffilterbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image Filter</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graphicobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>1,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/insertbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert</value></prop></node><node oor:name="private:resource/toolbar/oleobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE-Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/optimizetablebar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Optimize Size</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/previewobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawtextobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/viewerbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard (Viewing Mode)</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing</value></prop></node><node oor:name="private:resource/toolbar/mediaobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media Playback</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/colorbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Color</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/basicshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Basic Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/arrowshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Block Arrows</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/flowchartshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Flowchart</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/starshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Stars and Banners</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/symbolshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Symbol Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/calloutshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Callouts</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkshapetype" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork Shape</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/navigationobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Navigation</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/changes" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Track Changes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/singlemode" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard (Single Mode)</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/textstylebar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Formatting (Styles)</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="XFormsWindowState" oor:package="org.openoffice.Office.UI"><node oor:name="UIElements"><node oor:name="States"><node oor:name="private:resource/popupmenu/annotation" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Comment</value></prop></node><node oor:name="private:resource/popupmenu/draw" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape</value></prop></node><node oor:name="private:resource/popupmenu/drawtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Shape Text</value></prop></node><node oor:name="private:resource/popupmenu/form" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Control</value></prop></node><node oor:name="private:resource/popupmenu/formrichtext" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop></node><node oor:name="private:resource/popupmenu/frame" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Frame</value></prop></node><node oor:name="private:resource/popupmenu/graphic" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop></node><node oor:name="private:resource/popupmenu/media" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media</value></prop></node><node oor:name="private:resource/popupmenu/oleobject" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE Object</value></prop></node><node oor:name="private:resource/popupmenu/preview" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop></node><node oor:name="private:resource/popupmenu/table" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop></node><node oor:name="private:resource/popupmenu/text" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text</value></prop></node><node oor:name="private:resource/toolbar/standardbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/findbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,3</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Find</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/textobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Formatting</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/toolbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Tools</value></prop></node><node oor:name="private:resource/toolbar/tableobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/numobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Bullets and Numbering</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawingobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing Object Properties</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/alignmentbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Align Objects</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/bezierobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Edit Points</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/extrusionobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">3D-Settings</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formtextobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsfilterbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Filter</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsnavigationbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Navigation</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formcontrols" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Controls</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formdesign" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Design</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,2</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/frameobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Frame</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fullscreenbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Style" oor:type="xs:int"><value>2</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Full Screen</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graffilterbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image Filter</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graphicobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Image</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/insertbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert</value></prop></node><node oor:name="private:resource/toolbar/insertobjectbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert Object</value></prop></node><node oor:name="private:resource/toolbar/oleobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">OLE-Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/optimizetablebar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Optimize</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/previewobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Print Preview</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawtextobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Object</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/viewerbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard (Viewing Mode)</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing</value></prop></node><node oor:name="private:resource/toolbar/mediaobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media Playback</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/colorbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Color</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/basicshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Basic Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/arrowshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Block Arrows</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/flowchartshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Flowchart</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/starshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Stars and Banners</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/symbolshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Symbol Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/calloutshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Callouts</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkshapetype" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork Shape</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Filter"><node oor:name="Filters"><node oor:name="Text (encoded) (StarWriter/GlobalDocument)" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.Writer.FilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value>TEXT_DLG</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="ExportExtension"><value>txt</value></prop><prop oor:name="Type"><value>generic_Text</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.GlobalDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Text - Choose Encoding (Master Document)</value></prop></node><node oor:name="writer_globaldocument_StarOffice_XML_Writer" oor:op="replace"><prop oor:name="Flags"><value>TEMPLATE ALIEN ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXML</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>writer_StarOffice_XML_Writer</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.GlobalDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">OpenOffice.org 1.0 Text Document</value></prop></node><node oor:name="writer_globaldocument_StarOffice_XML_Writer_GlobalDocument" oor:op="replace"><prop oor:name="Flags"><value>IMPORT TEMPLATE OWN ALIEN PREFERRED ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXML</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>writer_globaldocument_StarOffice_XML_Writer_GlobalDocument</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.GlobalDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">OpenOffice.org 1.0 Master Document</value></prop></node><node oor:name="writer_globaldocument_pdf_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.PDF.PDFDialog</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.PDF.PDFFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">PDF - Portable Document Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pdf_Portable_Document_Format</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.GlobalDocument</value></prop></node><node oor:name="writerglobal8" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE OWN PREFERRED ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXML</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>writerglobal8</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.GlobalDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">ODF Master Document</value></prop></node><node oor:name="writerglobal8_template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE TEMPLATEPATH OWN ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXMLV</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>writerglobal8_template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.GlobalDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">ODF Master Document Template</value></prop></node><node oor:name="writerglobal8_writer" oor:op="replace"><prop oor:name="Flags"><value>EXPORT TEMPLATE DEFAULT ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXML</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>writer8</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.GlobalDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">ODF Text Document</value></prop></node><node oor:name="writerglobal8_HTML" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>HTML</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>generic_HTML</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.GlobalDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">HTML (Writer/Global)</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Types"><node oor:name="Types"><node oor:name="generic_Text" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.PlainTextFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>csv tsv tab txt</value></prop><prop oor:name="MediaType"><value>text/plain</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="UIName"><value>Text</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_StarOffice_XML_Writer" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sxw</value></prop><prop oor:name="MediaType"><value>application/vnd.sun.xml.writer</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>StarOffice XML (Writer)</value></prop><prop oor:name="UIName"><value>OpenOffice.org 1.0 Text Document</value></prop><prop oor:name="ClipboardFormat"><value>Writer 6.0</value></prop></node><node oor:name="writer_globaldocument_StarOffice_XML_Writer_GlobalDocument" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sxg</value></prop><prop oor:name="MediaType"><value>application/vnd.sun.xml.writer.global</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>writer_globaldocument_StarOffice_XML_Writer_GlobalDocument</value></prop><prop oor:name="UIName"><value>Writer 6.0 Master Document</value></prop><prop oor:name="ClipboardFormat"><value>Writer/Global 6.0</value></prop></node><node oor:name="pdf_Portable_Document_Format" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pdf</value></prop><prop oor:name="MediaType"><value>application/pdf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>PDF - Portable Document Format</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writerglobal8" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"><value>private:factory/swriter/GlobalDocument*</value></prop><prop oor:name="Extensions"><value>odm</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.text-master</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>writerglobal8</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Writer 8 Master Document</value></prop><prop oor:name="ClipboardFormat"><value>Writer/Global 8</value></prop></node><node oor:name="writerglobal8_template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"><value>private:factory/swriter/GlobalDocument*</value></prop><prop oor:name="Extensions"><value>otm</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.text-master-template</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>writerglobal8_template</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Writer 8 Master Document Template</value></prop><prop oor:name="ClipboardFormat"><value>Writer/Global 8 Template</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Filter"><node oor:name="Filters"><node oor:name="HTML" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>HTML</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>generic_HTML</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">HTML Document</value></prop></node><node oor:name="Text (StarWriter/Web)" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>TEXT</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="ExportExtension"><value>txt</value></prop><prop oor:name="Type"><value>generic_Text</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Text (Writer/Web)</value></prop></node><node oor:name="Text (encoded) (StarWriter/Web)" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.Writer.FilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value>TEXT_DLG</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="ExportExtension"><value>txt</value></prop><prop oor:name="Type"><value>generic_Text</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Text - Choose Encoding (Writer/Web)</value></prop></node><node oor:name="writer_web_HTML_help" oor:op="replace"><prop oor:name="Flags"><value>IMPORT INTERNAL NOTINFILEDIALOG READONLY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>HTML_HELP</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Help content</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_web_HTML_help</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop></node><node oor:name="writer_web_StarOffice_XML_Writer" oor:op="replace"><prop oor:name="Flags"><value>EXPORT TEMPLATE ALIEN ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXML</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>writer_StarOffice_XML_Writer</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">OpenOffice.org 1.0 Text Document (Writer/Web)</value></prop></node><node oor:name="writer_web_StarOffice_XML_Writer_Web_Template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT TEMPLATE TEMPLATEPATH OWN ALIEN ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXMLVWEB</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>writer_web_StarOffice_XML_Writer_Web_Template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">OpenOffice.org 1.0 HTML Template</value></prop></node><node oor:name="writer_web_pdf_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.PDF.PDFDialog</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.PDF.PDFFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">PDF - Portable Document Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pdf_Portable_Document_Format</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop></node><node oor:name="writer_web_png_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.GraphicExportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">PNG - Portable Network Graphic</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>png_Portable_Network_Graphic</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop></node><node oor:name="writer_web_jpg_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.GraphicExportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">JPEG - Joint Photographic Experts Group</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>jpg_JPEG</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop></node><node oor:name="writerweb8_writer_template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE TEMPLATEPATH OWN ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXMLVWEB</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>writerweb8_writer_template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">HTML Document Template</value></prop></node><node oor:name="writerweb8_writer" oor:op="replace"><prop oor:name="Flags"><value>EXPORT TEMPLATE ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXML</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>writer8</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.WebDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Text (Writer/Web)</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Types"><node oor:name="Types"><node oor:name="generic_HTML" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.PlainTextFilterDetect</value></prop><prop oor:name="URLPattern"><value>private:factory/swriter/web*</value></prop><prop oor:name="Extensions"><value>html xhtml htm</value></prop><prop oor:name="MediaType"><value>text/html</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>HTML</value></prop><prop oor:name="UIName"><value>HTML Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="generic_Text" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.PlainTextFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>csv tsv tab txt</value></prop><prop oor:name="MediaType"><value>text/plain</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="UIName"><value>Text</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_web_HTML_help" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.text.FormatDetector</value></prop><prop oor:name="URLPattern"><value>vnd.sun.star.help://*</value></prop><prop oor:name="Extensions"/><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>writer_web_HTML_help</value></prop><prop oor:name="UIName"><value>Help content</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_StarOffice_XML_Writer" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sxw</value></prop><prop oor:name="MediaType"><value>application/vnd.sun.xml.writer</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>StarOffice XML (Writer)</value></prop><prop oor:name="UIName"><value>OpenOffice.org 1.0 Text Document</value></prop><prop oor:name="ClipboardFormat"><value>Writer 6.0</value></prop></node><node oor:name="writer_web_StarOffice_XML_Writer_Web_Template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>stw</value></prop><prop oor:name="MediaType"><value>application/vnd.sun.xml.writer.web</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>writer_web_StarOffice_XML_Writer_Web_Template</value></prop><prop oor:name="UIName"><value>Writer/Web 6.0 Template</value></prop><prop oor:name="ClipboardFormat"><value>Writer/Web 6.0</value></prop></node><node oor:name="pdf_Portable_Document_Format" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pdf</value></prop><prop oor:name="MediaType"><value>application/pdf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>PDF - Portable Document Format</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writerweb8_writer_template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>oth</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.text-web</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>writerweb8_writer_template</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Writer/Web 8 Template</value></prop><prop oor:name="ClipboardFormat"><value>Writer/Web 8</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Filter"><node oor:name="Filters"><node oor:name="HTML (StarWriter)" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>HTML</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>generic_HTML</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">HTML Document (Writer)</value></prop></node><node oor:name="MS WinWord 5" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>WW6</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft WinWord 1/2/5</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_WinWord_5</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MS WinWord 6.0" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CWW6</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft Word 6.0</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_WinWord_60</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MS Word 95" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CWW6</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft Word 95</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Word_95</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MS Word 95 Vorlage" oor:op="replace"><prop oor:name="Flags"><value>IMPORT TEMPLATE TEMPLATEPATH ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CWW6</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Word_95_Vorlage</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft Word 95 Template</value></prop></node><node oor:name="MS Word 97" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN PREFERRED ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CWW8</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Word 97–2003</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Word_97</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MS Word 97 Vorlage" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE TEMPLATEPATH ALIEN ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CWW8</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Word_97_Vorlage</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Word 97–2003 Template</value></prop></node><node oor:name="OpenDocument Text Flat XML" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_ODT_FlatXML</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="UserData"><value oor:separator=",">com.sun.star.comp.filter.OdfFlatXml,,com.sun.star.comp.Writer.XMLOasisImporter,com.sun.star.comp.Writer.XMLOasisExporter,,,true</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value xml:lang="en-US">Flat XML ODF Text Document</value></prop><prop oor:name="Flags"><value>IMPORT EXPORT OWN 3RDPARTYFILTER</value></prop></node><node oor:name="Rich Text Format" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.RtfFilter</value></prop><prop oor:name="UserData"><value>RTF</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Rich Text</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_Rich_Text_Format</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="StarOffice XML (Writer)" oor:op="replace"><prop oor:name="Flags"><value>IMPORT TEMPLATE OWN ALIEN PREFERRED ENCRYPTION EXOTIC</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXML</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>writer_StarOffice_XML_Writer</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">OpenOffice.org 1.0 Text Document</value></prop></node><node oor:name="WordPerfect" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.WordPerfectImportFilter</value></prop><prop oor:name="UserData"><value>WPD</value></prop><prop oor:name="UIName"><value xml:lang="en-US">WordPerfect Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_WordPerfect_Document</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MS_Works" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MSWorksImportFilter</value></prop><prop oor:name="UserData"><value>WPS</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft Works Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Works_Document</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MS_Write" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MSWorksImportFilter</value></prop><prop oor:name="UserData"><value>WRI</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft Write</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Write</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="DosWord" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MSWorksImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft Word for DOS</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_DosWord</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="ClarisWorks" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">ClarisWorks/AppleWorks Text Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_ClarisWorks</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Mac_Word" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft Word for Mac (v1 - v5)</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_Mac_Word</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Mac_Works" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">Microsoft Works for Mac Text Document (v1 - v4)</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_Mac_Works</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MacWrite" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">MacWrite Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MacWrite</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Mariner_Write" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">Mariner Write Mac Classic v1.6 - v3.5</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_Mariner_Write</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="WriteNow" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">WriteNow Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_WriteNow</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="AbiWord" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER EXOTIC</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.AbiWordImportFilter</value></prop><prop oor:name="UserData"><value>ABW</value></prop><prop oor:name="UIName"><value xml:lang="en-US">AbiWord Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_AbiWord_Document</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="T602Document" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED EXOTIC</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.T602ImportFilter</value></prop><prop oor:name="UserData"><value>602</value></prop><prop oor:name="UIName"><value xml:lang="en-US">T602 Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_T602_Document</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="LotusWordPro" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.LotusWordProImportFilter</value></prop><prop oor:name="UserData"><value>WPD</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Lotus WordPro Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_LotusWordPro_Document</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Text" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>TEXT</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="ExportExtension"><value>txt</value></prop><prop oor:name="Type"><value>generic_Text</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Text</value></prop></node><node oor:name="Text (encoded)" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.Writer.FilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value>TEXT_DLG</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="ExportExtension"><value>txt</value></prop><prop oor:name="Type"><value>generic_Text</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Text - Choose Encoding</value></prop></node><node oor:name="writer_MIZI_Hwp_97" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER EXOTIC</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.comp.hwpimport.HwpImportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">Hangul WP 97</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MIZI_Hwp_97</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="writer_StarOffice_XML_Writer_Template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT TEMPLATE TEMPLATEPATH OWN ALIEN ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXMLV</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>writer_StarOffice_XML_Writer_Template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">OpenOffice.org 1.0 Text Document Template</value></prop></node><node oor:name="writer_pdf_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.PDF.PDFDialog</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.PDF.PDFFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">PDF - Portable Document Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pdf_Portable_Document_Format</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="writer8" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE OWN DEFAULT PREFERRED ENCRYPTION PASSWORDTOMODIFY GPGENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXML</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>writer8</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">ODF Text Document</value></prop></node><node oor:name="writer8_template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE TEMPLATEPATH OWN ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXMLV</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>writer8_template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIName"><value xml:lang="en-US">ODF Text Document Template</value></prop></node><node oor:name="MS Word 2007 XML" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER ENCRYPTION PASSWORDTOMODIFY SUPPORTSSIGNING</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.WriterFilter</value></prop><prop oor:name="UserData"><value>OXML</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Word 2007–365</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Word_2007</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MS Word 2007 XML Template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER TEMPLATE TEMPLATEPATH</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.WriterFilter</value></prop><prop oor:name="UserData"><value>OXML</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Word 2007–365 Template</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Word_2007_Template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MS Word 2007 XML VBA" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER ENCRYPTION PASSWORDTOMODIFY SUPPORTSSIGNING</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.WriterFilter</value></prop><prop oor:name="UserData"><value>OXML</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Word 2007–365 VBA</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_MS_Word_2007_VBA</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Office Open XML Text" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER ENCRYPTION PASSWORDTOMODIFY</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.WriterFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">Office Open XML Text (Transitional)</value></prop><prop oor:name="FileFormatVersion"><value>1</value></prop><prop oor:name="Type"><value>writer_OOXML</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Office Open XML Text Template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER TEMPLATE TEMPLATEPATH</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.WriterFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">Office Open XML Text Template (Transitional)</value></prop><prop oor:name="FileFormatVersion"><value>1</value></prop><prop oor:name="Type"><value>writer_OOXML_Text_Template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="writer_layout_dump" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.LayoutDump</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="en-US">Writer Layout XML</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_layout_dump_xml</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="BroadBand eBook" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="FilterService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">BroadBand eBook</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_BroadBand_eBook</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="FictionBook 2" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="FilterService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">FictionBook 2.0</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_FictionBook_2</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="PalmDoc" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="FilterService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">PalmDoc eBook</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_PalmDoc</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Plucker eBook" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="FilterService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Plucker eBook</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_Plucker_eBook</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Apple Pages" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="FilterService"><value>org.libreoffice.comp.Writer.PagesImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Apple Pages</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_ApplePages</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="MWAW_Text_Document" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED EXOTIC</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Legacy Mac Text Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>MWAW_Text_Document</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="Palm_Text_Document" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="FilterService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Palm Text Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>Palm_Text_Document</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="StarOffice_Writer" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="FilterService"><value>org.libreoffice.comp.Writer.StarOfficeWriterImportFilter</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Legacy StarOffice Text Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>StarOffice_Writer</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="EPUB" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.Writer.EPUBExportUIComponent</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.EPUBExportFilter</value></prop><prop oor:name="UserData"><value>EPUB</value></prop><prop oor:name="UIName"><value xml:lang="en-US">EPUB Document</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_EPUB_Document</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop></node><node oor:name="PocketWord File" oor:op="replace"><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>writer_PocketWord_File</value></prop><prop oor:name="DocumentService"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.MSWorksImportFilter</value></prop><prop oor:name="TemplateName"/><prop oor:name="UIName"><value>Pocket Word</value></prop><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Types"><node oor:name="Types"><node oor:name="generic_HTML" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.PlainTextFilterDetect</value></prop><prop oor:name="URLPattern"><value>private:factory/swriter/web*</value></prop><prop oor:name="Extensions"><value>html xhtml htm</value></prop><prop oor:name="MediaType"><value>text/html</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>HTML</value></prop><prop oor:name="UIName"><value>HTML Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="calc_MS_Excel_40" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.calc.ExcelBiffFormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>xls xlw xlc xlm</value></prop><prop oor:name="MediaType"><value>application/vnd.ms-excel</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS Excel 4.0</value></prop><prop oor:name="UIName"><value>Microsoft Excel 4.0</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="calc_MS_Excel_5095" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.calc.ExcelBiffFormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>xls xlc xlm xlw</value></prop><prop oor:name="MediaType"><value>application/vnd.ms-excel</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS Excel 5.0/95</value></prop><prop oor:name="UIName"><value>Microsoft Excel 5.0</value></prop><prop oor:name="ClipboardFormat"><value>Biff5</value></prop></node><node oor:name="calc_MS_Excel_95" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.calc.ExcelBiffFormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>xls xlc xlm xlw</value></prop><prop oor:name="MediaType"><value>application/vnd.ms-excel</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS Excel 95</value></prop><prop oor:name="UIName"><value>Microsoft Excel 95</value></prop><prop oor:name="ClipboardFormat"><value>Biff5</value></prop></node><node oor:name="writer_MS_WinWord_5" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.text.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>doc</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS WinWord 5</value></prop><prop oor:name="UIName"><value>Microsoft WinWord 1/2/5</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_MS_WinWord_60" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.text.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>doc</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS WinWord 6.0</value></prop><prop oor:name="UIName"><value>Microsoft Word 6.0</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_MS_Word_95" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.text.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>doc</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS Word 95</value></prop><prop oor:name="UIName"><value>Microsoft Word 95</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_MS_Word_95_Vorlage" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.text.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>dot</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS Word 95 Vorlage</value></prop><prop oor:name="UIName"><value>MS Word 95 Template</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_MS_Word_97" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.text.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>doc wps</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MS Word 97</value></prop><prop oor:name="UIName"><value>Word 97–2003</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_MS_Word_97_Vorlage" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.text.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>dot wpt</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MS Word 97 Vorlage</value></prop><prop oor:name="UIName"><value>Word 97–2000 Template</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_ODT_FlatXML" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.XMLFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>fodt odt xml</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.text-flat-xml</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>OpenDocument Text Flat XML</value></prop><prop oor:name="UIName"><value xml:lang="en-US">OpenDocument Text (Flat XML)</value></prop><prop oor:name="ClipboardFormat"><value>doctype:office:mimetype="application/vnd.oasis.opendocument.text"</value></prop></node><node oor:name="writer_Rich_Text_Format" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.text.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>rtf</value></prop><prop oor:name="MediaType"><value>application/rtf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>Rich Text Format</value></prop><prop oor:name="UIName"><value>Rich Text Format</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_StarOffice_XML_Writer" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sxw</value></prop><prop oor:name="MediaType"><value>application/vnd.sun.xml.writer</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>StarOffice XML (Writer)</value></prop><prop oor:name="UIName"><value>OpenOffice.org 1.0 Text Document</value></prop><prop oor:name="ClipboardFormat"><value>Writer 6.0</value></prop></node><node oor:name="writer_WordPerfect_Document" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.WordPerfectImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>wpd</value></prop><prop oor:name="MediaType"><value>application/vnd.wordperfect</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>WordPerfect</value></prop><prop oor:name="UIName"><value>WordPerfect Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_MS_Works_Document" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MSWorksImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>wps</value></prop><prop oor:name="MediaType"><value>application/vnd.ms-works</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MS_Works</value></prop><prop oor:name="UIName"><value>Microsoft Works Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_MS_Write" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MSWorksImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>wri</value></prop><prop oor:name="MediaType"><value>application/x-mswrite</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MS_Write</value></prop><prop oor:name="UIName"><value>Microsoft Write</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_DosWord" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MSWorksImportFilter</value></prop><prop oor:name="Extensions"><value>doc</value></prop><prop oor:name="PreferredFilter"><value>DosWord</value></prop><prop oor:name="UIName"><value>Microsoft Word for DOS</value></prop></node><node oor:name="writer_ClarisWorks" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>cwk</value></prop><prop oor:name="MediaType"><value>application/clarisworks</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>ClarisWorks</value></prop><prop oor:name="UIName"><value>ClarisWorks/AppleWorks Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_Mac_Word" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>doc</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Mac_Word</value></prop><prop oor:name="UIName"><value>Microsoft Word for Mac (v1 - v5)</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_Mac_Works" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>wps</value></prop><prop oor:name="MediaType"><value>application/vnd.ms-works</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Mac_Works</value></prop><prop oor:name="UIName"><value>Microsoft Works for Mac Document (v1 - v4)</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_MacWrite" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>mw mcw</value></prop><prop oor:name="MediaType"><value>application/macwriteii</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MacWrite</value></prop><prop oor:name="UIName"><value>MacWrite Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_Mariner_Write" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>mwd</value></prop><prop oor:name="MediaType"><value/></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Mariner_Write</value></prop><prop oor:name="UIName"><value>Mariner Write Mac Classic v1.6 - v3.5</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_WriteNow" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>wn nx^d</value></prop><prop oor:name="MediaType"><value/></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>WriteNow</value></prop><prop oor:name="UIName"><value>WriteNow Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_AbiWord_Document" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.AbiWordImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>abw zabw</value></prop><prop oor:name="MediaType"><value>application/x-abiword</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>AbiWord</value></prop><prop oor:name="UIName"><value>AbiWord Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_T602_Document" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.T602ImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>602 txt</value></prop><prop oor:name="MediaType"><value>application/x-t602</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>T602Document</value></prop><prop oor:name="UIName"><value>T602 Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_LotusWordPro_Document" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.LotusWordProImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>lwp</value></prop><prop oor:name="MediaType"><value>application/vnd.lotus-wordpro</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>LotusWordPro</value></prop><prop oor:name="UIName"><value>LotusWordPro Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="generic_Text" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.PlainTextFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>csv tsv tab txt</value></prop><prop oor:name="MediaType"><value>text/plain</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="UIName"><value>Text</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_MIZI_Hwp_97" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.comp.hwpimport.HwpImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>hwp</value></prop><prop oor:name="MediaType"><value>application/x-hwp</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>writer_MIZI_Hwp_97</value></prop><prop oor:name="UIName"><value>Hangul WP 97</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_StarOffice_XML_Writer_Template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>stw</value></prop><prop oor:name="MediaType"><value>application/vnd.sun.xml.writer.template</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>writer_StarOffice_XML_Writer_Template</value></prop><prop oor:name="UIName"><value>Writer 6.0 Template</value></prop><prop oor:name="ClipboardFormat"><value>Writer 6.0</value></prop></node><node oor:name="pdf_Portable_Document_Format" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pdf</value></prop><prop oor:name="MediaType"><value>application/pdf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>PDF - Portable Document Format</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer8_template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>ott</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.text-template</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>writer8_template</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Writer 8 Template</value></prop><prop oor:name="ClipboardFormat"><value>Writer 8 Template</value></prop></node><node oor:name="writer8" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.StorageFilterDetect</value></prop><prop oor:name="URLPattern"><value>private:factory/swriter</value></prop><prop oor:name="Extensions"><value>odt</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.text</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>writer8</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Writer 8</value></prop><prop oor:name="ClipboardFormat"><value>Writer 8</value></prop></node><node oor:name="writer_MS_Word_2007" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.oox.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>docx</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MS Word 2007 XML</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Word 2007–365</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_MS_Word_2007_Template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.oox.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>dotx dotm</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MS Word 2007 XML Template</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Word 2007–365 Template</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_MS_Word_2007_VBA" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.oox.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>docm</value></prop><prop oor:name="MediaType"><value>application/msword</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MS Word 2007 XML VBA</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Word 2007–365 VBA</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_OOXML" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.oox.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>docx docm</value></prop><prop oor:name="MediaType"><value>application/vnd.openxmlformats-officedocument.wordprocessingml.document</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Office Open XML Text</value></prop><prop oor:name="UIName"><value>Office Open XML Text Document (Transitional)</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_OOXML_Text_Template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.oox.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>dotx dotm</value></prop><prop oor:name="MediaType"><value>application/vnd.openxmlformats-officedocument.wordprocessingml.template</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Office Open XML Text Template</value></prop><prop oor:name="UIName"><value>Office Open XML Text Template (Transitional)</value></prop><prop oor:name="ClipboardFormat"><value>MSWordDoc</value></prop></node><node oor:name="writer_layout_dump_xml" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>xml</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>Writer Layout Dump</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_BroadBand_eBook" oor:op="replace"><prop oor:name="DetectService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="Extensions"><value>lrf</value></prop><prop oor:name="MediaType"><value>application/x-sony-bbeb</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>BroadBand eBook</value></prop><prop oor:name="UIName"><value>BroadBand eBook</value></prop></node><node oor:name="writer_FictionBook_2" oor:op="replace"><prop oor:name="DetectService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="Extensions"><value>fb2 zip</value></prop><prop oor:name="MediaType"><value>application/x-fictionbook+xml</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>FictionBook 2</value></prop><prop oor:name="UIName"><value>FictionBook 2.0</value></prop></node><node oor:name="writer_PalmDoc" oor:op="replace"><prop oor:name="DetectService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="Extensions"><value>pdb</value></prop><prop oor:name="MediaType"><value>application/x-aportisdoc</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>PalmDoc</value></prop><prop oor:name="UIName"><value>PalmDoc eBook</value></prop></node><node oor:name="writer_Plucker_eBook" oor:op="replace"><prop oor:name="DetectService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="Extensions"><value>pdb</value></prop><prop oor:name="MediaType"><value>application/prs.plucker</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Plucker eBook</value></prop><prop oor:name="UIName"><value>Plucker eBook</value></prop></node><node oor:name="writer_ApplePages" oor:op="replace"><prop oor:name="DetectService"><value>org.libreoffice.comp.Writer.PagesImportFilter</value></prop><prop oor:name="Extensions"><value>pages</value></prop><prop oor:name="MediaType"><value>application/x-iwork-pages-sffpages</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Apple Pages</value></prop><prop oor:name="UIName"><value>Apple Pages</value></prop></node><node oor:name="MWAW_Text_Document" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MWAWImportFilter</value></prop><prop oor:name="Extensions"><value>*</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>MWAW_Text_Document</value></prop><prop oor:name="UIName"><value>Legacy Mac Text Document</value></prop></node><node oor:name="Palm_Text_Document" oor:op="replace"><prop oor:name="DetectService"><value>org.libreoffice.comp.Writer.EBookImportFilter</value></prop><prop oor:name="Extensions"><value>pdb</value></prop><prop oor:name="MediaType"><value>application/vnd.palm</value></prop><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Palm_Text_Document</value></prop><prop oor:name="UIName"><value>Palm Text Document</value></prop></node><node oor:name="StarOffice_Writer" oor:op="replace"><prop oor:name="DetectService"><value>org.libreoffice.comp.Writer.StarOfficeWriterImportFilter</value></prop><prop oor:name="Extensions"><value>sdw</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>StarOffice_Writer</value></prop><prop oor:name="UIName"><value>Legacy StarOffice Text Document</value></prop></node><node oor:name="writer_EPUB_Document" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>epub</value></prop><prop oor:name="MediaType"><value>application/epub+zip</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>EPUB Document</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="writer_PocketWord_File" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.Writer.MSWorksImportFilter</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>psw</value></prop><prop oor:name="MediaType"><value>application/x-pocket-word</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PocketWord File</value></prop><prop oor:name="UIName"><value>Pocket Word</value></prop><prop oor:name="ClipboardFormat"><value>doctype:pwi</value></prop></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="Common" oor:package="org.openoffice.Office"><node oor:name="Menus"><node oor:name="New"><node oor:name="m0" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>private:factory/swriter</value></prop><prop oor:name="Title"><value xml:lang="en-US">~Text Document</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_default</value></prop></node><node oor:name="m7" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>private:factory/swriter/web</value></prop><prop oor:name="Title"><value xml:lang="en-US">~HTML Document</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_default</value></prop></node><node oor:name="m8" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>private:factory/swriter?slot=21053</value></prop><prop oor:name="Title"><value xml:lang="en-US">~XML Form Document</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_default</value></prop></node><node oor:name="m10" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>private:factory/swriter?slot=21051</value></prop><prop oor:name="Title"><value xml:lang="en-US">~Labels</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_default</value></prop><prop oor:name="ImageIdentifier" oor:type="xs:string"><value>private:image/3255</value></prop></node><node oor:name="m11" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>private:factory/swriter?slot=21052</value></prop><prop oor:name="Title"><value xml:lang="en-US">B~usiness Cards</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_default</value></prop><prop oor:name="ImageIdentifier" oor:type="xs:string"><value>private:image/3255</value></prop></node><node oor:name="m13" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>private:factory/swriter/GlobalDocument</value></prop><prop oor:name="Title"><value xml:lang="en-US">M~aster Document</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_default</value></prop></node></node><node oor:name="Wizard"><node oor:name="m0" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>service:com.sun.star.wizards.letter.CallWizard?insert</value></prop><prop oor:name="Title"><value xml:lang="en-US">~Letter...</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_self</value></prop></node><node oor:name="m1" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>service:com.sun.star.wizards.fax.CallWizard?insert</value></prop><prop oor:name="Title"><value xml:lang="en-US">~Fax...</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_self</value></prop></node><node oor:name="m2" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>service:com.sun.star.wizards.agenda.CallWizard?insert</value></prop><prop oor:name="Title"><value xml:lang="en-US">~Agenda...</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_self</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:package="org.openoffice.Office" oor:name="Embedding"><node oor:name="Objects"><node oor:name="8BC6B165-B1B2-4EDD-AA47-DAE2EE689DD6" oor:op="replace"><prop oor:name="ObjectFactory"><value>com.sun.star.embed.OOoEmbeddedObjectFactory</value></prop><prop oor:name="ObjectDocumentServiceName"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="ObjectMiscStatus"/><prop oor:name="ObjectVerbs"><value>PRIMARY SHOW OPEN HIDE UIACTIVATE IPACTIVATE SAVECOPYAS</value></prop></node></node><node oor:name="ObjectNames"><node oor:name="Writer" oor:op="replace"><prop oor:name="ObjectUIName"><value xml:lang="en-US">%PRODUCTNAME %PRODUCTVERSION Text</value></prop><prop oor:name="ClassID"><value>8BC6B165-B1B2-4EDD-AA47-DAE2EE689DD6</value></prop></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="Setup" oor:package="org.openoffice"><node oor:name="Office"><node oor:name="Factories"><node oor:name="com.sun.star.text.GlobalDocument" oor:op="replace"><prop oor:name="ooSetupFactoryDocumentService" oor:finalized="true"><value>com.sun.star.text.GlobalDocument</value></prop><prop oor:name="ooSetupFactoryCommandConfigRef"><value>WriterCommands</value></prop><prop oor:name="ooSetupFactoryActualFilter" oor:finalized="true"><value>writerglobal8</value></prop><prop oor:name="ooSetupFactoryActualTemplateFilter" oor:finalized="true"><value>writerglobal8_template</value></prop><prop oor:name="ooSetupFactoryDefaultFilter"><value>writerglobal8</value></prop><prop oor:name="ooSetupFactoryEmptyDocumentURL" oor:finalized="true"><value>private:factory/swriter/GlobalDocument</value></prop><prop oor:name="ooSetupFactoryWindowAttributes"><value>,,,;4;</value></prop><prop oor:name="ooSetupFactoryIcon"><value>10</value></prop><prop oor:name="ooSetupFactoryTemplateFile"><value/></prop><prop oor:name="ooSetupFactorySystemDefaultTemplateChanged"><value>false</value></prop><prop oor:name="ooSetupFactoryShortName"><value>sglobal</value></prop><prop oor:name="ooSetupFactoryUIName"><value>Writer</value></prop><prop oor:name="ooSetupFactoryWindowStateConfigRef"><value>WriterGlobalWindowState</value></prop><prop oor:name="ooSetupFactoryCmdCategoryConfigRef"><value>GenericCategories</value></prop></node><node oor:name="com.sun.star.text.TextDocument" oor:op="replace"><prop oor:name="ooSetupFactoryDocumentService" oor:finalized="true"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="ooSetupFactoryCommandConfigRef"><value>WriterCommands</value></prop><prop oor:name="ooSetupFactoryActualFilter" oor:finalized="true"><value>writer8</value></prop><prop oor:name="ooSetupFactoryActualTemplateFilter" oor:finalized="true"><value>writer8_template</value></prop><prop oor:name="ooSetupFactoryDefaultFilter"><value>writer8</value></prop><prop oor:name="ooSetupFactoryEmptyDocumentURL" oor:finalized="true"><value>private:factory/swriter</value></prop><prop oor:name="ooSetupFactoryWindowAttributes"><value>,,,;4;</value></prop><prop oor:name="ooSetupFactoryIcon"><value>2</value></prop><prop oor:name="ooSetupFactoryTemplateFile"><value/></prop><prop oor:name="ooSetupFactorySystemDefaultTemplateChanged"><value>false</value></prop><prop oor:name="ooSetupFactoryShortName"><value>swriter</value></prop><prop oor:name="ooSetupFactoryUIName"><value>Writer</value></prop><prop oor:name="ooSetupFactoryWindowStateConfigRef"><value>WriterWindowState</value></prop><prop oor:name="ooSetupFactoryCmdCategoryConfigRef"><value>GenericCategories</value></prop></node><node oor:name="com.sun.star.sdb.FormDesign" oor:op="replace"><prop oor:name="ooSetupFactoryDocumentService" oor:finalized="true"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="ooSetupFactoryActualFilter" oor:finalized="true"><value>writer8</value></prop><prop oor:name="ooSetupFactoryActualTemplateFilter" oor:finalized="true"><value>writer8_template</value></prop><prop oor:name="ooSetupFactoryDefaultFilter"><value>writer8</value></prop><prop oor:name="ooSetupFactoryEmptyDocumentURL" oor:finalized="true"><value>private:factory/swriter</value></prop><prop oor:name="ooSetupFactoryWindowAttributes"><value>,,,;4;</value></prop><prop oor:name="ooSetupFactoryCommandConfigRef"><value>WriterCommands</value></prop><prop oor:name="ooSetupFactoryIcon"><value>2</value></prop><prop oor:name="ooSetupFactoryShortName"><value>swform</value></prop><prop oor:name="ooSetupFactoryUIName"><value xml:lang="en-US">Base: Database Form</value></prop><prop oor:name="ooSetupFactoryWindowStateConfigRef"><value>WriterFormWindowState</value></prop><prop oor:name="ooSetupFactoryCmdCategoryConfigRef"><value>GenericCategories</value></prop></node><node oor:name="com.sun.star.sdb.TextReportDesign" oor:op="replace"><prop oor:name="ooSetupFactoryDocumentService" oor:finalized="true"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="ooSetupFactoryActualFilter" oor:finalized="true"><value>writer8</value></prop><prop oor:name="ooSetupFactoryActualTemplateFilter" oor:finalized="true"><value>writer8_template</value></prop><prop oor:name="ooSetupFactoryDefaultFilter"><value>writer8</value></prop><prop oor:name="ooSetupFactoryEmptyDocumentURL" oor:finalized="true"><value>private:factory/swriter</value></prop><prop oor:name="ooSetupFactoryCommandConfigRef"><value>WriterCommands</value></prop><prop oor:name="ooSetupFactoryIcon"><value>2</value></prop><prop oor:name="ooSetupFactoryShortName"><value>swreport</value></prop><prop oor:name="ooSetupFactoryUIName"><value xml:lang="en-US">Base: Report Design</value></prop><prop oor:name="ooSetupFactoryWindowStateConfigRef"><value>WriterReportWindowState</value></prop><prop oor:name="ooSetupFactoryCmdCategoryConfigRef"><value>GenericCategories</value></prop></node><node oor:name="com.sun.star.xforms.XMLFormDocument" oor:op="replace"><prop oor:name="ooSetupFactoryDocumentService" oor:finalized="true"><value>com.sun.star.text.TextDocument</value></prop><prop oor:name="ooSetupFactoryActualFilter" oor:finalized="true"><value>writer8</value></prop><prop oor:name="ooSetupFactoryActualTemplateFilter" oor:finalized="true"><value>writer8_template</value></prop><prop oor:name="ooSetupFactoryDefaultFilter"><value>writer8</value></prop><prop oor:name="ooSetupFactoryEmptyDocumentURL" oor:finalized="true"><value>private:factory/swriter?slot=21053</value></prop><prop oor:name="ooSetupFactoryCommandConfigRef"><value>WriterCommands</value></prop><prop oor:name="ooSetupFactoryWindowAttributes"><value>,,,;4;</value></prop><prop oor:name="ooSetupFactoryIcon"><value>2</value></prop><prop oor:name="ooSetupFactoryShortName"><value>swxform</value></prop><prop oor:name="ooSetupFactoryUIName"><value>XML Form Document</value></prop><prop oor:name="ooSetupFactoryWindowStateConfigRef"><value>XFormsWindowState</value></prop><prop oor:name="ooSetupFactoryCmdCategoryConfigRef"><value>GenericCategories</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="Drivers" oor:package="org.openoffice.Office.DataAccess"><node oor:name="Installed"><node oor:name="sdbc:writer:*" oor:op="replace"><prop oor:name="Driver"><value>com.sun.star.comp.sdbc.writer.ODriver</value></prop><prop oor:name="DriverTypeDisplayName" oor:type="xs:string"><value xml:lang="en-US">Writer Document</value></prop><node oor:name="Properties"><node oor:name="EscapeDateTime" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node><node oor:name="Features"><node oor:name="EscapeDateTime" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node><node oor:name="MetaData"><node oor:name="SupportsBrowsing" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="FileSystemBased" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="MediaType" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value>application/vnd.oasis.opendocument.text</value></prop></node></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="Drivers" oor:package="org.openoffice.Office.DataAccess"><node oor:name="Installed"><node oor:name="sdbc:mysql:jdbc:*" oor:op="replace"><prop oor:name="Driver"><value>org.openoffice.comp.drivers.MySQL.Driver</value></prop><prop oor:name="DriverTypeDisplayName" oor:type="xs:string"><value xml:lang="en-US">MySQL (JDBC)</value></prop><node oor:name="Properties"><node oor:name="CharSet" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value/></prop></node><node oor:name="JavaDriverClass" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value>com.mysql.jdbc.Driver</value></prop></node><node oor:name="AddIndexAppendix" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="ParameterNameSubstitution" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node><node oor:name="Features"><node oor:name="UseKeywordAsBeforeAlias" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="IgnoreDriverPrivileges" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="DisplayVersionColumns" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="UseDOSLineEnds" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="BooleanComparisonMode" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="FormsCheckRequiredFields" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node><node oor:name="MetaData"><node oor:name="SupportsTableCreation" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="UseJava" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="Authentication" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value>UserPassword</value></prop></node><node oor:name="SupportsColumnDescription" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node></node><node oor:name="sdbc:mysql:odbc:*" oor:op="replace"><prop oor:name="Driver"><value>org.openoffice.comp.drivers.MySQL.Driver</value></prop><prop oor:name="DriverTypeDisplayName" oor:type="xs:string"><value xml:lang="en-US">MySQL (ODBC)</value></prop><node oor:name="Properties"><node oor:name="CharSet" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value/></prop></node><node oor:name="AddIndexAppendix" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="ParameterNameSubstitution" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node><node oor:name="Features"><node oor:name="UseKeywordAsBeforeAlias" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="IgnoreDriverPrivileges" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="DisplayVersionColumns" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="UseDOSLineEnds" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="BooleanComparisonMode" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="FormsCheckRequiredFields" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node><node oor:name="MetaData"><node oor:name="SupportsTableCreation" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="SupportsBrowsing" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="Authentication" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value>UserPassword</value></prop></node></node></node><node oor:name="sdbc:mysql:mysqlc:*" oor:op="replace"><prop oor:name="Driver"><value>org.openoffice.comp.drivers.MySQL.Driver</value></prop><prop oor:name="DriverTypeDisplayName" oor:type="xs:string"><value xml:lang="en-US">MySQL (Native)</value></prop><node oor:name="Properties"><node oor:name="CharSet" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value/></prop></node><node oor:name="LocalSocket" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value/></prop></node><node oor:name="NamedPipe" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value/></prop></node><node oor:name="AddIndexAppendix" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="ParameterNameSubstitution" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node><node oor:name="Features"><node oor:name="UseKeywordAsBeforeAlias" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="IgnoreDriverPrivileges" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="DisplayVersionColumns" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="UseDOSLineEnds" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="BooleanComparisonMode" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="FormsCheckRequiredFields" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node><node oor:name="MetaData"><node oor:name="SupportsTableCreation" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="Authentication" oor:op="replace"><prop oor:name="Value" oor:type="xs:string"><value>UserPassword</value></prop></node><node oor:name="SupportsColumnDescription" oor:op="replace"><prop oor:name="Value" oor:type="xs:boolean"><value>true</value></prop></node></node></node></node></oor:component-data></oor:data>
