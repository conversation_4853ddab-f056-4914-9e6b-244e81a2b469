#
# This file is part of the LibreOffice project.
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# This file incorporates work covered by the following license notice:
#
#   Licensed to the Apache Software Foundation (ASF) under one or more
#   contributor license agreements. See the NOTICE file distributed
#   with this work for additional information regarding copyright
#   ownership. The ASF licenses this file to you under the Apache
#   License, Version 2.0 (the "License; you may not use this file
#   except in compliance with the License. You may obtain a copy of
#   the License at http://www.apache.org/licenses/LICENSE-2.0 .
#
# x-no-translate

#
#  resources.properties
#
#  resources for com.sun.star.wizards
#
RID_COMMON_0=ཡིག་ཁུག་འཛུགས་ཐབས་བྲལ་ '%1' 。<BR>ཁྱེད་ཀྱི་མཁྲིགས་སྡེར་སྤྱོད་པའི་བར་སྟོང་མི་འདུག་།
RID_COMMON_1=ཡིག་དེབ་ཀྱི་ཡིག་ཚགས་གསར་བཟོ་བྱེད་ཐབས་བྲལ། 'PRODUCTNAME Writer'དཔེ་རྡོག་བསྒར་སྒྲིག་བྱས་ཡོད་མེད་ཞིབ་བཤེར་བྱེད་དགོས།
RID_COMMON_2=ལས་ཁྲ་སྐྱེས་གྲུབ་བྱ་ཐབས་བྲལ་<BR>བྱ་རིམ་རྨོ་ལིང་'StarSuite Calc' སྒྲིག་འཇུག་ཟིན་མིན་བརྟག་དཔྱད་བྱེད་རོགས།
RID_COMMON_3=གསལ་སྟོན་ཡིག་ཟིན་སྐྱེས་གྲུབ་བྱ་ཐབས་བྲལ་<BR>བྱ་རིམ་རྨོ་ལིང་' 'StarSuite Impress' སྒྲིག་འཇུག་ཟིན་མིན་བརྟག་དཔྱད་བྱེད་རོགས།
RID_COMMON_4=རིས་འགོད་སྐྱེས་གྲུབ་བྱ་ཐབས་བྲལ<BR>བྱ་རིམ་རྨོ་ལིང་' 'StarOffice Draw' སྒྲིག་འཇུག་ཟིན་མིན་བརྟག་དཔྱད་བྱེད་རོགས།
RID_COMMON_5=སྤྱི་འགྲོས་སྐྱེས་གྲུབ་བྱ་ཐབས་བྲལ་<BR>བྱ་རིམ་རྨོ་ལིང་''StarSuite Math' སྒྲིག་འཇུག་ཟིན་མིན་བརྟག་དཔྱད་བྱེད་རོགས།
RID_COMMON_6=དགོང་ངེས་ཀྱི་ཡིག་ཆ་འཚོལ་མི་ཐུབ་<BR>ཁྱེད་ཀྱིས་ %PRODUCTNAME སྒུལ་སློང་བྱས་ནས་སྒྲིག་སྦྱོརབྱ་རིམ་ལྟར་བསྐྱར་བཅོས་ལག་བསྟར་བྱེད།
RID_COMMON_7=ཡིག་ཆ་ '<PATH>' གནས་ཡོད་<BR><BR>ཁྱེད་རང་གིས་ད་ཡོད་ཀྱི་ཡིག་ཆ་བཟོ་འབྲི་བྱེད།
RID_COMMON_8=Yes
RID_COMMON_9=ཡིན་ བཟོ་འབྲི་ཚང་མའི།
RID_COMMON_10=མིན།
RID_COMMON_11=Cancel
RID_COMMON_12=~Finish
RID_COMMON_13=< ~Back
RID_COMMON_14=གོམ་སྟབས་རྗེས་མ།(~N) >
RID_COMMON_15=རོགས་རམ།(~H)
RID_COMMON_16=Steps
RID_COMMON_17=Close
RID_COMMON_18=OK
RID_COMMON_19=ཡིག་ཆ་འདི་གནས་ཡོད་དེ་བཀབ་དགོས་སམ།
RID_COMMON_20= <current_date> བརྒྱུད་པའི་<wizard_name>གསར་བཟོའི་མ་པང།
RID_COMMON_21=The wizard could not be run, because important files were not found.\nUnder 'Tools - Options - %PRODUCTNAME - Paths' click the 'Default' button to reset the paths to the original default settings.\nThen run the wizard again.
RID_REPORT_0=སྙན་ཐོའི་རང་འགུལ་ཡིག་ཆའི་ལས་རོགས།
RID_REPORT_3=~Table
RID_REPORT_4=སྟར།(~M)
RID_REPORT_7=སྙན་ཐོ་_
RID_REPORT_8=- undefined -
RID_REPORT_9=སྙན་ཐོའི་ནང་གི་ཡིག་དུམ།(~F)
RID_REPORT_11=ཚོ་སྒྲིག
RID_REPORT_12=རིམ་སྒྲིག་འདེམས་པ།
RID_REPORT_13=པར་ཚུལ་འདེམས་པ།
RID_REPORT_14=སྙན་ཐོ་ཉར་ཚགས།
RID_REPORT_15=གཞི་གྲངས་པར་དབྱིབས།
RID_REPORT_16=ཤོག་སྨིན་དང་ཤོག་ཞབས་པར་དབྱིབས།
RID_REPORT_19=Fields
RID_REPORT_20=རིམ་སྒྲིག།(~S)
RID_REPORT_21=སྙོམས་གཤིབ།
RID_REPORT_22=Orientation
RID_REPORT_23=གཞུང་ཕྱོགས།
RID_REPORT_24=འཕྲེད་ཕྱོགས།
RID_REPORT_28=ཁྱེད་ཀྱི་སྙན་ཐོའི་ནང་ཡིག་དུམ་གང་འདྲ་ཞིག་དགོས་སམ།
RID_REPORT_29=ཁྱེད་ལ་རིམ་དབྱེའི་གསབ་སྣོན་དགོས་སམ།
RID_REPORT_30=ཡིག་དུམ་གང་འདྲ་ཞིག་ལ་བརྟེན་ནས་གྲངས་ཀ་རིམ་སྒྲིག་བྱེད་དམ།
RID_REPORT_31=ཁྱེད་ཀྱི་སྙན་ཐོའི་རྣམ་གཞག་གི་བཟོ་ལྟའི་རེ་བ་གང་།
RID_REPORT_32=ཁྱེད་ཀྱི་སྙན་ཐོའི་རྣམ་གཞག་གི་བཟོ་ལྟའི་རེ་བ་གང་།
RID_REPORT_33=སྙན་ཐོའི་ཁ་བྱང་།
RID_REPORT_34=སྙན་ཐོ་མངོན་པ།
RID_REPORT_35=སྙན་ཐོ་ཉར་ཚགས།
RID_REPORT_36=Ascending
RID_REPORT_37=Descending
RID_REPORT_40=འགྱུར་ཚུལ་ཀྱི་སྙན་ཐོ་བཟོ་ལྟ།
RID_REPORT_41=སྙན་ཐོའི་བཟོ་ལྟ་མྱུར་དུ་བེད་སྤྱོད་བྱེད།
RID_REPORT_42=སྙན་ཐོ་བཟོ་ལྟ་བཟོ་བཅོས་བྱེད།
RID_REPORT_43=རྒྱས་བཅད་ཚུལ་གྱི་སྙན་ཐོ།
RID_REPORT_44=གཞན་ཉར།
RID_REPORT_50=ཚོ་དབྱེ།
RID_REPORT_51=དེ་རྗེས་གཞི་འཛིན་ས།(~Y)
RID_REPORT_52=དེ་རྗེས་གཞི་འཛིན་ས།(~T)
RID_REPORT_53=གོང་ལ།(~E)
RID_REPORT_54=གོང་ལ།(~I)
RID_REPORT_55=གོང་ལ།(~G)
RID_REPORT_56=འོག་ལ།(~S)
RID_REPORT_57= འོག་ལ།(~C)
RID_REPORT_58=འོག་ལ།(~S)
RID_REPORT_60=སྙན་ཐོའི་ནང་གཉིས་སྒྲིལ་ལུགས་ཀྱི་ཡིག་དུམ་མངོན་ཐབས་བྲལ།
RID_REPORT_61=རེའུ་མིག་ '<TABLENAME>' གནས་མེད།
RID_REPORT_62=སྙན་ཐོ་བཟོ་གཞིན་པ།..
RID_REPORT_63=གཞི་གྲངསདཀར་ཆག་བསྒར་འཛུད་ཀྱི་གྲངས་ཀ <COUNT>
RID_REPORT_64=རེའུ་མིག་ '<REPORTFORM>' གནས་མེད།
RID_REPORT_65=ཞིབ་བརྗོད་<BR>'<STATEMENT>' <BR>བྱེད་པའི་བཙ་ལ་འདྲིའི་བཀའ་ཚིག་ཚུད་པ་ལག་བསྟར་བྱ་ཐབས་བྲལ་<BR>བེད་སྤྱོད་པའི་གཞི་གྲངས་ཁུངས་བརྟག་དཔྱད་བྱེད་རོགས།
RID_REPORT_66=གཤམ་གྱི་རེའུ་མིག་ '<REPORTFORM>' ནང་གབ་པའི་ཚོད་འཛིན་རྣམ་གྲངས་ཀློག་ལེན་བྱ་ཐབས་བྲལ། '<CONTROLNAME>'
RID_REPORT_67=གཞི་གྲངས་ནང་བཅུག་བྱེད་བཞིན་ཡོད།...
RID_REPORT_68=ཡིག་དུམ་མིང་གསལ་སྟོན་བྱེད།
RID_REPORT_69=ཁྱེད་ཀྱིས་ཡིག་དུམ་མིང་གསལ་སྟོན་བྱེད་དམ།
RID_REPORT_70=Label
RID_REPORT_71=ཡིག་དུམ།
RID_REPORT_72=རང་འགུལ་ཡིག་ཆའི་ལས་རོགས་ནོར་འཁྲུལ་ཞིག་ཐོན་<BR>བཟོ་ལྟ་ནང་ '%PATH' ནོར་འཁྲུལ་ཚུད་ཡོད་<BR>དགོས་ངེས་ཀྱི་ས་ཁོངས་རེའུ་མིག་གནས་མེད་ཡང་ན་གཏན་ཕབ་པའི་མིང་ཕན་མེད་ཡིན་<BR>རོགས་རམ་ནང་འབྲེལ་ཡོད་ཞིབ་ཕྲའི་གསལ་བཤད་ཀློག་བཤེར་བྱེད་<BR>བཟོ་ལྟ་གཞན་ཞིག་འདེམས་རོགས།
RID_REPORT_73=རེའུ་མིག་ནང་ཕན་མེད་ཀྱི་རང་མངགས་ཡིག་དུམ་གཅིག་གནས་ཡོད།
RID_REPORT_74=The sort criterion '<FIELDNAME>' was chosen twice. Each criterion can only be chosen once.
RID_REPORT_75=དོ་སྣང་བྱ་རྒྱུར། སྙན་ཐོ་བཟོ་བའི་སྐབས་དཔེ་སྟོན་ཡི་གེ་དེ་གཞི་གྲངས་མཛོད་ཀྱི་ཡི་གེའི་ནང་དོན་གྱིས་ཚབ་བྱེད་ཀྱི་ཡོད།
RID_REPORT_76=གཞི་གྲངས་མཛོད་ནང་སྙན་ཐོ་ '%REPORTNAME'གནས་ཡོད། མིང་གཞན་གཏན་ཕབ་བྱེད།
RID_REPORT_78=གསར་བཟོའི་སྙན་ཐོ་འཕུལ་རྗེས་མུ་མཐུད་རིགས་གང་འདྲ་བཀོལ་སྤྱོད་བྱེད་དམ།
RID_REPORT_79=ཁྱེད་ཀྱི་རིགས་གང་འདྲ་སྙན་ཐོ་གསར་་བཟོ་བྱེད་དམ།
RID_REPORT_80=རེའུ་མིག་རྣམ་པ།
RID_REPORT_81=རྣམ་པའི་གསལ་ཐོ། སྟར་པ་གཅིག
RID_REPORT_82=རྣམ་པའི་གསལ་ཐོ། སྟར་པ་གཉིས།
RID_REPORT_83=རྣམ་པའི་གསལ་ཐོ། སྟར་པ་གསུམ།
RID_REPORT_84=པང་གི་དཔེ་རྣམ་། དཔེ་འཛར་གཡོན་དུ་ཡོད་པ།
RID_REPORT_85=པང་གི་དཔེ་རྣམ་། དཔེ་འཛར་ཐོག་ཏུ་ཡོད་པ།
RID_REPORT_86=ཁ་བྱང་།:
RID_REPORT_87=རྩོམ་པ་པོ།:
RID_REPORT_88=ཚོས་གྲངས།
# Please don't translate the words #page# and #count#, these are placeholders.
RID_REPORT_89=ཤོག་གྲངས་ #page# ཁྱོན་ཤོག་གྲངས་#count#
RID_REPORT_90=ཤོག་ཨང་།:
RID_REPORT_91=ཤོག་གྲངས།:
RID_REPORT_92=རྩིས་འགྲོ་ཡོད་པའི་གོང་འབུལ་རེའུ་མིག་གི་དཔེ་པང་བཪྙེད་མ་སོང་།
RID_REPORT_93=Page:
RID_REPORT_94=Align Left - Border
RID_REPORT_95=Align Left - Compact
RID_REPORT_96=Align Left - Elegant
RID_REPORT_97=Align Left - Highlighted
RID_REPORT_98=Align Left - Modern
RID_REPORT_99=Align Left - Red & Blue
RID_REPORT_100=Default
RID_REPORT_101=Outline - Borders
RID_REPORT_102=Outline - Compact
RID_REPORT_103=Outline - Elegant
RID_REPORT_104=Outline - Highlighted
RID_REPORT_105=Outline - Modern
RID_REPORT_106=Outline - Red & Blue
RID_REPORT_107=Outline, indented - Borders
RID_REPORT_108=Outline, indented - Compact
RID_REPORT_109=Outline, indented - Elegant
RID_REPORT_110=Outline, indented - Highlighted
RID_REPORT_111=Outline, indented - Modern
RID_REPORT_112=Outline, indented - Red & Blue
RID_REPORT_113=Bubbles
RID_REPORT_114=Cinema
RID_REPORT_115=Controlling
RID_REPORT_116=Default
RID_REPORT_117=Drafting
RID_REPORT_118=ནོར་དོན།
RID_REPORT_119=Flipchart
RID_REPORT_120=Formal with Company Logo
RID_REPORT_121=Generic
RID_REPORT_122=Worldmap
RID_DB_COMMON_0=C~reate
RID_DB_COMMON_1=~Cancel
RID_DB_COMMON_2=< ~Back
RID_DB_COMMON_3=གོམ་སྟབས་རྗེས་མ།(~N) >
RID_DB_COMMON_4=གཞི་གྲངས་མཛོད།(~D)
RID_DB_COMMON_5=རེའུ་མིག་མིང་།(~T)
RID_DB_COMMON_6=རང་འགུལ་ཡིག་ཆའི་ལས་རོགས་ལག་སྟར་བྱེད་སྐབས་ནོར་འཁྲུལ་གཅིག་ཐོན་ཡོད་ད་ལྟ་རང་འགུལ་ཡིག་ཆའི་ལས་རོགས་མཇུག་སྒྲིལ་ཡོད།
RID_DB_COMMON_8=No database has been installed. At least one database is required before the wizard for forms can be started.
RID_DB_COMMON_9=The database does not contain any tables.
RID_DB_COMMON_10=This title already exists in the database. Please enter another name.
RID_DB_COMMON_11=The title must not contain any spaces or special characters.
RID_DB_COMMON_12=The database service (com.sun.data.DatabaseEngine) could not be instantiated.
RID_DB_COMMON_13=The selected table or query could not be opened.
RID_DB_COMMON_14=གཞི་གྲངས་མཛོད་དང་འབྲེལ་མཐུད་བྱ་ཐབས་བྲལ།
RID_DB_COMMON_20=རོགས་རམ།(~H)
RID_DB_COMMON_21=སྐབས་འཇོག།(~S)
RID_DB_COMMON_30=ཡིག་ཚགས་འདི་ཉར་ཚགས་བྱ་ཐབས་བྲལ།
RID_DB_COMMON_33=རང་འགུལ་ཡིག་ཆའི་ལས་རོགས་ཁ་རྒྱབ་པ།
RID_DB_COMMON_34=གཞི་གྲངས་ཁུངས་འབྲེལ་མཐུད་བྱེད་བཞིན་པ་...
RID_DB_COMMON_35=གཞི་གྲངས་ཁུངས་འབྲེལ་མཐུད་བྱ་ཐབས་བྲལ།
RID_DB_COMMON_36=ཁྱེད་རང་གིས་ནང་བཅུག་པའི་ཡིག་ཆའི་རྒྱུད་ལམ་ཕན་མེད་ཡིན།
RID_DB_COMMON_37=གཞི་གྲངས་ཁུངས་གཅིག་འདེམ་རོགས།
RID_DB_COMMON_38=རེའུ་མིག་གམ་བཙལ་འདྲི་གཅིག་འདེམ་རོགས།
RID_DB_COMMON_39=ཡིག་དུམ་གསར་སྣོན།
RID_DB_COMMON_40=ཡིག་དུམ་བསུབ་པ།
RID_DB_COMMON_41=ཡིག་དུམ་ཚང་མ་གསར་སྣོན།
RID_DB_COMMON_42=ཡིག་དུམ་ཚང་མ་བསུབ།
RID_DB_COMMON_43=ཡིག་དུམ་གོང་ལ་སྤོ།
RID_DB_COMMON_44=ཡིག་དུམ་འོག་ལ་སྤོ།
RID_DB_COMMON_45=ཡིག་དུམ་མིང་ '%NAME' བྱང་འཚོལ་བྱ་ཐབས་བྲལ།
RID_QUERY_0=བཙ་ལ་འདྲི་སྣེ་འཁྲེད།
RID_QUERY_1=Query
RID_QUERY_2=བཙ་ལ་འདྲི་སྣེ་འཁྲེད།
RID_QUERY_3=རེའུ་མིག།(~T)
RID_QUERY_4=སྤྱོད་ཆོག་པའི་ཡིག་དུམ།(~V)
RID_QUERY_5=བཙ་ལ་འདྲི་མིང།(~O)
RID_QUERY_6=བཙ་ལ་འདྲི་མངོན་པ།(~Q)
RID_QUERY_7=བཙ་ལ་འདྲི་བཟོ་བཅོས།(~M)
RID_QUERY_8=རྩད་བཤེར་བྱས་རྗེས་མུ་མཐུད་བཀོལ་སྤྱོད་(~H)གང་ཞིག་བྱེད་མིན་གསར་འཛུགས།
RID_QUERY_9=གཤམ་གྱི་རྣམ་གྲངས་ཚང་མ་ཟླ་སྒྲིག་བྱེད།(~A)
RID_QUERY_10=གཤམ་གྱི་རྣམ་གྲངས་ཤིག་ཟླ་སྒྲིག་བྱེད།(~M)
RID_QUERY_11=ཞིབ་ཕྲའི་བཙ་ལ་འདྲི་བྱེད་（བཙ་ལ་འདྲི་བྱེད་པའི་ཟིན་ཐོ་ཚང་མ་མངོན་པ་）(~D)
RID_QUERY_12=བཙ་ལ་འདྲི་ཕྱོགས་སྒྲིག་（འདུ་སྤྱོར་རྟེན་གྲངས་ཀྱི་མཇུག་སྦྲས་ཁོ་ན་མངོན་པ་。）(~S)
RID_QUERY_16=འདུ་སྤྱོར་རྟེན་གྲངས།
RID_QUERY_17=Fields
RID_QUERY_18=ཚོ་སྒྲིག་གི་རྟེན་གཞི།(~G)
RID_QUERY_19=ཡིག་དུམ།
RID_QUERY_20=Alias
RID_QUERY_21=Table:
RID_QUERY_22=Query:
RID_QUERY_24=ཆ་རྐྱེན།
RID_QUERY_25=Value
RID_QUERY_26=མཚུངས།
RID_QUERY_27=མི་མཚུངས།
RID_QUERY_28=ཆུང་བ།
RID_QUERY_29=ཆེ་བ།
RID_QUERY_30=is equal or less than
RID_QUERY_31=མཚུངས་པའམ་ཆེ་བ།
RID_QUERY_32=འདྲ་བ།
RID_QUERY_33=not like
RID_QUERY_34=is null
RID_QUERY_35=is not null
RID_QUERY_36=true
RID_QUERY_37=false
RID_QUERY_38=and
RID_QUERY_39=ཡང་ན།
RID_QUERY_40=སྡོམ་ཐོབ་འཚོལ་བ།
RID_QUERY_41=ཆ་སྙོམས་ཐང་འཚོལ་བ།
RID_QUERY_42=ཐང་ཆུང་ཤོས་འཚོལ།
RID_QUERY_43=ཐང་ཆེ་་ཤོས་འཚོལ།
RID_QUERY_44=get the count of
RID_QUERY_48=（མེད་）
RID_QUERY_50=Fie~lds in the Query:
RID_QUERY_51=Sorting order:
RID_QUERY_52=རིམ་སྒྲིག་ཡིག་དུམ་གཏན་མ་ཕབ་པ།
RID_QUERY_53=Search conditions:
RID_QUERY_54=ཆ་རྐྱེན་གཏན་ཕབ་མེད་པ།
RID_QUERY_55=Aggregate functions:
RID_QUERY_56=འདུ་སྤྱོར་རྟེན་གྲངས་གཏན་མ་ཕབ་པ།
RID_QUERY_57=Grouped by:
RID_QUERY_58=ཚོ་སྒྲིག་གཏན་མ་ཕབ་པ།
RID_QUERY_59=Grouping conditions:
RID_QUERY_60=ཚོ་སྒྲིག་ཆ་རྐྱེན་གཏན་མ་ཕབ་པ།
RID_QUERY_70=བཙ་ལ་འདྲིའི་ཡིག་དུམ་འདེམས་པ་（སྟར་）
RID_QUERY_71=རིམ་སྒྲིག་གོ་རིམ་འདེམས་པ།
RID_QUERY_72=་བཤེར་འཚོལ་ཆ་རྐྱེན་འདེམས་པ།
RID_QUERY_73=བཙ་ལ་འདྲིའི་རིགས་འདེམས་པ།
RID_QUERY_74=ཚོ་སྒྲིག་འདེམས་པ།
RID_QUERY_75=་ཚོ་སྒྲིག་ཆ་རྐྱེན་འདེམས་པ།
RID_QUERY_76=དགོས་མཁོའི་སྐབས་མིང་གཞན་འདེམས་པ།
RID_QUERY_77=གནས་ཚུལ་རགས་བསྡུས་བརྟག་དཔྱད་བྱེད་པ་མ་ཟད་མུ་མཐུད་བཀོལསྤྱོད་རིགས་གང་ཞིག་བྱེད་མིན་གཏན་འཁེལ་བྱེད་དགོས།
RID_QUERY_80=ཡིག་དུམ་འདེམས་པ།
RID_QUERY_81=རིམ་སྒྲིག་གོ་རིམ།
RID_QUERY_82=བཤེར་འཚོལ་ཆ་རྐྱེན།
RID_QUERY_83=གསལ་ཕྲད་དམ་ཕྱོགས་སྒྲིག
RID_QUERY_84=ཚོ་སྒྲིག
RID_QUERY_85=ཚོ་སྒྲིག་ཆ་རྐྱེན།
RID_QUERY_86=མིང་གཞན།
RID_QUERY_87=Overview
RID_QUERY_88=གཏན་མ་ཕབ་པའི་འདུ་འཛོམས་རྟེན་གྲངས་ཀྱི་ཡིག་དུམ་དེ་ངེས་པར་དུ་ཚོ་སྒྲིག་ནང་སྤྱོད་དགོས།
RID_QUERY_89=ཆ་རྐྱེན་  '<FIELDNAME> <LOGICOPERATOR> <VALUE>' ནི་ཐེངས་གཉིས་བདམས་ཟིན་ ཆ་རྐྱེན་རེ་རེ་ཐེངས་རེ་ལས་བདམས་མི་ཆོག
RID_QUERY_90=འདུ་འཛོམས་རྐྱེན་གྲངས་ <FUNCTION> གཏན་ཕབ་པ་ fieldname '<NUMERICFIELD>' ཐེངས་གཉིས་ཟིན།
RID_QUERY_91=,
RID_QUERY_92=<FIELDTITLE> (<FIELDNAME>)
RID_QUERY_93=<FIELDNAME> (<SORTMODE>)
RID_QUERY_94=<FIELDNAME> <LOGICOPERATOR> <VALUE>
RID_QUERY_95=<CALCULATEDFUNCTION> <FIELDNAME>
RID_QUERY_96=<FIELDNAME> <LOGICOPERATOR> <VALUE>
RID_FORM_0=རང་འགུལ་ཡིག་ཆའི་ལས་རོགས་རེའུ་མིག
RID_FORM_1=ཁ་བྱང་འདི་བེད་སྤྱོད་བྱ་ཐབས་བྲལ་ ཁྱེད་ཀྱི་ཁ་བྱང་གཞན་ཞིག་འདེམས་རོགས།
RID_FORM_2=Binary fields are always listed and selectable from the left list.\nIf possible, they are interpreted as images.
RID_FORM_3=A subform is a form that is inserted in another form.\nUse subforms to show data from tables or queries with a one-to-many relationship.
RID_FORM_4= (ཚེས་གྲངས་)
RID_FORM_5= (དུས་ཚོད་)
RID_FORM_6=རེའུ་མིག་དང་བཙལ་འདྲི།
RID_FORM_7=ཡིག་དུམ་འདེམས་པ།
RID_FORM_8=འབྲེལ་བ་གང་ཞིག་ཁ་སྣོན་བྱེད་དགོས་སམ(~W)
RID_FORM_9=བཟོ་ལྟ།
RID_FORM_12=ད་ཡོད་ཀྱི་ཡིག་དུམ།(~I)
RID_FORM_13=རེའུ་མིག་ནང་གི་ཡིག་དུམ།
RID_FORM_19=The join '<FIELDNAME1>' and '<FIELDNAME2>' has been selected twice.\nBut joins may only be used once.
RID_FORM_20=ཟློས་བརྩེགས།(~S)
RID_FORM_21=ཤོག་ངོས་བཟོ་ལྟ།(~P)
RID_FORM_22=ཁྱེད་རང་གིས་ཁ་བྱང་གཅིག་ནང་བཅུག་བྱེད།
RID_FORM_23=ལེགས་གྲུབ་བྱུང་རྗེས།(~A)...
RID_FORM_24=ཡིག་ཚགས་འཕྲལ་དུ་བེད་སྤྱོད་བྱེད།
RID_FORM_25=ཡིག་ཚགས་ཉར་ཚགས།
RID_FORM_26=རེའུ་མིག་འདི་བཙུགས་པ་མ་ཟད་གཞི་གྲངས་མཛོད་ནང་ཉར་ཚགས་ཟིན།
RID_FORM_27=རེའུ་མིག་མིང་མང་ཤོས་ལ་ཡིག་རྟགས་32གིས་གྲུབ་ཡོད།
RID_FORM_28=ཡིག་དུམ་མཐའ་སྒྲོམ།
RID_FORM_29=མཐའ་སྒྲོམ་མེད་པ།
RID_FORM_30=རྩེ་3ཕྱེ་དབྱིབས།
RID_FORM_31=ངོས་དབྱེབས།
RID_FORM_32=ཤོག་བྱང་ཡིག་དུམ་སྙོམས་གཤིབ།
RID_FORM_33=གཡོན་ལ་སྙོམས་གཤིབ།
RID_FORM_34=གཡས་ལ་སྙོམས་གཤིབ།
RID_FORM_35=གཞི་གྲངས་མཛོད་ཡིག་དུམ་སྙོམས་གཤིབ།
RID_FORM_36=སྟར་ནང་ - ཤོག་བྱང་གཡོན་ལ།
RID_FORM_37=སྟར་ནང་  - ཤོག་བྱང་གོང་ལ།
RID_FORM_38=ནར་ལེབ་ནང་ - ཤོག་བྱང་གཡོན་ལ།
RID_FORM_39=ནར་ལེབ་ནང་ - ཤོག་བྱང་གོང་ལ།
RID_FORM_40=གཞི་གྲངས་རེའུ་མིག་བྱེད་པ།
RID_FORM_41=སྒྲོམ་གཟུགས་གཙོ་བོའི་སྟར་སྒྲིག
RID_FORM_42=སྒྲོམ་གཟུགས་བུའི་སྟར་སྒྲིག
RID_FORM_44=སྒྲོམ་གཟུགས་ལ་གཞི་གྲངས་གསར་པ་ནང་བཅུག་ཁོ་ན་སྤྱོད་ཀྱི་ཡོད།(~U)
RID_FORM_45=Existing data will not be displayed
RID_FORM_46=པར་དབྱིབས།
RID_FORM_47=ལེགས་སྒྲུབ།
RID_FORM_48=ད་ཡོད་གཞི་གྲངས་བསུབ་མིག་ཆོག།(~D)
RID_FORM_49=གཞི་གྲངས་གསར་པ་གསབ་སྣོན་བྱེད་མི་ཆོག(~A)
RID_FORM_50=གཉིས་གོང་སྒྲིལ་གྱི་ཡིག་དུམ་མངོན་པ།
RID_FORM_51=གཉིས་གོང་སྒྲིལ་གྱི་ཡིག་དུམ་སྣང་མེད།
RID_FORM_52=གཉིས་གོང་སྒྲིལ་གྱི་ཡིག་དུམ་རིས་དབྱིབས་ལ་རྩིས་་པ།
RID_FORM_53=སྒྲོམ་གཟུགས་བཟོ་བཅོས།(~M)
RID_FORM_55=རྒྱབ་ལྗོངས་ཚོས་གཞི།
RID_FORM_80=ཡིག་དུམ་འདེམས་པ།
RID_FORM_81=སྒྲོམ་གཟུགས་སུ་བཀོད་སྒྲིག་བྱེད།
RID_FORM_82=སྒྲོམ་གཟུགས་བུའི་ཡིག་དུམ་གསབ་སྣོན་བྱེད།
RID_FORM_83=ཚོ་སྒྲིག་ཡིག་དུམ་ཐོབ་ལེན་བྱེད།
RID_FORM_84=སྟར་སྒྲིག་ཚོད་འཛིན།
RID_FORM_85=གཞི་གྲངས་དཀར་ཆག་བཀོད་སྒྲིག
RID_FORM_86=བེད་སྤྱོད་ཀྱི་བཟོ་ལྟ།
RID_FORM_87=བཀོད་སྒྲིག་མིང་།
RID_FORM_88=（ཚེ་གྲངས་）
RID_FORM_89=（དུས་ཚོད་）
RID_FORM_90=སྒྲོམ་གཟུགས་ཡིག་དུམ་འདེམས་པ།
RID_FORM_91=སྒྲོམ་གཟུགས་བུ་བཀོད་སྒྲིག་བྱེད་མིན།
RID_FORM_92=སྒྲོམ་གཟུགས་བུའི་ཡིག་དུམ་བཀོད་སྒྲིག་བྱེད་མིན།
RID_FORM_93=སྒྲོམ་གཟུགས་ཁག་དབར་གྱི་ཚོ་སྒྲིག་འདེམས་པ།
RID_FORM_94=སྒྲོམ་གཟུགས་སྟེང་ཚོད་འཛིན་ཆས་སྟར་སྒྲིག་བྱེད།
RID_FORM_95=གཞི་གྲངསདཀར་ཆག་མ་ཚུལ་འདེམས་པ།
RID_FORM_96=སྒྲོམ་གཟུགས་སྤྱོད་པའི་བཟོ་ལྟ།
RID_FORM_97=སྒྲོམ་གཟུགས་ཀྱི་མིང་བཀོད་སྒྲིག
RID_FORM_98=A form with the name '%FORMNAME' already exists.\nChoose another name.
RID_TABLE_1=རེའུ་མིག་སྣེ་སྟོན།
RID_TABLE_2=ཡིག་དུམ་འདེམས་པ།
RID_TABLE_3=རིགས་དང་རྣམ་གཞག་བཟོ་སྒྲིག
RID_TABLE_4=འགག་གནད་ཡི་གེ་གཙོ་བོ་བཀོད་སྒྲིག
RID_TABLE_5=རེའུ་མིག་གསར་བཟོ།
RID_TABLE_8=རེའུ་མིག་ཆེད་ཡིག་དུམ་འདེམས་པ།
RID_TABLE_9=ཡིག་དུམ་རིགས་དང་རྣམ་གཞག་བཀོད་སྒྲིག་བྱེད།
RID_TABLE_10=འགག་གནད་ཡི་གེ་གཙོ་བོ་བཀོད་སྒྲིག
RID_TABLE_11=རེའུ་མིག་གསར་བཟོ།
RID_TABLE_14=སྣེ་སྟོན་འདིས་ཁྱེད་རང་ལ་གཞི་གྲངས་མཛོད་ཀྱི་རེའུ་མིག་གཅིག་གསར་བཟོ་བྱ་རྒྱུ་་རོགས་རམ་བྱེད་ཀྱི་རེད་ རེའུ་མིག་རིགས་དང་རེའུ་མིག་དཔེ་མཚོན་འདེམས་པའི་རྗེས་རེའུ་མིག་ནང་སྣོན་པའི་ཡིག་དུམ་འདེམས།
RID_TABLE_15=རིགས།(~T)
RID_TABLE_16=ཁེ་ལས།(~U)
RID_TABLE_17=སྒེར།(~E)
RID_TABLE_18=རེའུ་མིག་དཔེ།(~S)
RID_TABLE_19=སྤྱོད་ཆོག་པའི་ཡིག་དུམ།(~V)
RID_TABLE_20=ཡིག་དུམ་ཆ་འཕྲིན།
RID_TABLE_21=+
RID_TABLE_22=-
RID_TABLE_23=Field name
RID_TABLE_24=ཡིག་དུམ་རིགས།
RID_TABLE_25=ཡིག་དུམ་འདེམས་པ།(~S)
RID_TABLE_26=འགག་གནད་ཡི་གེ་གཙོ་བོ་ནི་གཞི་གྲངས་མཛོད་ཀྱི་རེའུ་མིག་ནང་གི་ཟིན་ཐོ་ཚང་མ་སྤྱོད་པའི་མཚོན་རྟགས་གཅིག་པོ་དེ་ཡིན་ འགག་གནད་ཡི་གེ་གཙོ་བོ་རྒྱུད་ཁེར་རྐྱང་གི་རེའུ་མིག་ཁག་ནང་གི་ཆ་འཕྲིན་སྟབས་བདེའི་སྒོ་ནས་ཐག་སྦྲེལ་བྱེད་ཐུབ་ ང་ཚོ་བྱས་ན་རེའུ་མིག་རེའི་ནང་འགག་གནད་ཡི་གེ་གཙོ་བོ་རེ་བཀོད་སྒྲིག་བྱེད་དགོས་ གལ་སྲིད་འགག་གནད་ཡི་གེ་གཙོ་བོ་མེད་ན་རེའུ་མིག་འདིའི་ནང་གཞི་གྲངས་བཅུག་ཐབས་བྲལ།
RID_TABLE_27=འགག་གནད་ཡི་གེ་གཙོ་བོ་གསར་བཟོ(~C)
RID_TABLE_28=རང་འགུལ་གྱིས་འགག་གནད་ཡི་གེ་གཙོ་བོ་གསབ་སྣོན་བྱེད།(~A)
RID_TABLE_29=བེད་སྤྱོད་པའི་ད་ཡོད་ཀྱི་ཡིག་དུམ་དེ་འགག་གནད་ཡི་གེ་གཙོ་བོ་བྱེད།(~U)
RID_TABLE_30=Define p~rimary key as a combination of several fields
RID_TABLE_31=ཡིག་དུམ་མིང་།(~I)
RID_TABLE_32=འགག་གནད་ཡི་གེ་གཙོ་བོའི་ཡིདུམ།(~P)
RID_TABLE_33=རང་འགུལ་ཐང་ལེན།(~V)
RID_TABLE_34=ཁྱེད་རང་རེའུ་མིག་ལ་མིང་ག་རེ་གཏན་ཕབ་དགོས་སམ།
RID_TABLE_35=ཁྱེད་ལ་བཀྲ་ཤིས་ཞུ།  ཁྱེད་ཀྱིས་རེའུ་མིག་གསར་བཟོ་བྱེད་པའི་ཆ་འཕྲིན་ཚང་མ་ནང་བཅུག་ཟིན།
RID_TABLE_36=གཤལ་ལ་ཁྱེད་རང་གང་ཞིག་བྱ་འདོད་དམ།
RID_TABLE_37=རེའུ་མིག་འཆར་འགོད་བཟོ་བཅོས་བྱེད།
RID_TABLE_38=གཞི་གྲངས་བསྒར་འཛུད་མྱུར་དུ་བྱེད།
RID_TABLE_39=C~reate a form based on this table
RID_TABLE_40=ཁྱེད་ཀྱི་གསར་བཟོ་བྱེད་པའི་རེའུ་མིག་ཁ་ཕྱེ་ཐབས་བྲལ།
RID_TABLE_41=རེའུ་མིག་མིང་ '%TABLENAME' ནང་གཞི་གྲངས་མཛོད་ཀྱིས་རྒྱབ་སྐྱོར་མ་བྱེད་པའི་ཡིག་རྟགས་ ('%SPECIALCHAR')ཚུད་ཟིན།
RID_TABLE_42=ཡིག་དུམ་མིང་ '%FIELDNAME' ནང་གཞི་གྲངས་མཛོད་ཀྱིས་རྒྱབ་སྐྱོར་མ་བྱེད་པའི་ཡིག་རྟགས('%SPECIALCHAR')ཚུད་ཟིན།
RID_TABLE_43=ཡིག་དུམ།
RID_TABLE_44=MyTable
RID_TABLE_45=ཡིག་དུམ་གསབ་སྣོན།
RID_TABLE_46=འདེམས་པའི་ཡིག་དུམ་བསུབ་པ།
RID_TABLE_47=ཡིག་དུམ་བསྒར་འཛུད་བྱ་ཐབས་བྲལ་ དེ་ནི་གཞི་གྲངས་མཛོད་ཀྱི་རེའུ་མིག་ནང་ཆོག་པའི་ཡིག་དུམ་གྲངས་ཆེ་ཤོས་ %COUNT བརྒལ་བའི་རྐྱེན་གྱིས་རེད།
RID_TABLE_48=The name '%TABLENAME' already exists.\nPlease enter another name.
RID_TABLE_49=རེའུ་མིག་དཀར་ཆག
RID_TABLE_50=རེའུ་མིག་མ་ཚུལ།
RID_TABLE_51=ཡིག་དུམ་དེ'%FIELDNAME' གནས་ཟིན།
STEP_ZERO_0=~Cancel
STEP_ZERO_1=རོགས་རམ།(~H)
STEP_ZERO_2=< ~Back
STEP_ZERO_3=~Convert
STEP_ZERO_4=དྲན་སྟོན། ཕྱི་ནས་ཡོངས་བའི་གཞི་གྲངས་ཀྱི་དངུལ་ལོའི་གྲངས་འབོར་དང་སྤྱི་འགྲོས་ནང་གི་དངུལ་ལོའི་བརྗེ་རྩིས་བཏགས་གྲངས་འདྲིན་སྤྱོད་སྒྱུར་བརྗེ་བྱ་ཐབས་བྲལ།
STEP_ZERO_5=སྔོན་ལ་ལས་ཁྲའི་སྲུང་སྐྱོབ་མཐའ་དག་རྩིས་མེད་གཏོང་།
STEP_ZERO_6=དངུལ་ལོར།:
STEP_ZERO_7=C~ontinue >
STEP_ZERO_8=ཁ་རྒྱབ་པ།(~L)
STEP_CONVERTER_0=ཡིག་ཚགས་ཚང་མ། (~E)
STEP_CONVERTER_1=Selection
STEP_CONVERTER_2=དྲ་མིག་བཟོ་ལྟ།(~T)
STEP_CONVERTER_3=དེ་སྔའི་ལས་ཁྲའི་ནང་གི་དངུལ་ལོའི་དྲ་མིག(~S)
STEP_CONVERTER_4=ཡིག་ཚགས་ཧྲིལ་པོ་་ནང་གི་དངུལ་ལོའི་དྲ་མིག(~D)
STEP_CONVERTER_5=བདམས་པའི་ས་ཁོངས་(~S)
STEP_CONVERTER_6=དྲ་མིག་བཟོ་ལྟ་འདེམས།
STEP_CONVERTER_7=དངུལ་ལོའི་དྲ་མིག་འདེམས།
STEP_CONVERTER_8=དངུལ་ལོའི་ས་ཁོངས།
STEP_CONVERTER_9=བཟོ་ལྟ།
STEP_AUTOPILOT_0=ཁྱབ་ཁོངས།
STEP_AUTOPILOT_1=རྐྱང་པའི་ %PRODUCTNAME ཡིག་ཚགས།(~S)
STEP_AUTOPILOT_2=དཀར་ཆག་ཚང་མ་(~D)
STEP_AUTOPILOT_3=ཡིག་ཚགས་ཁུངས།
STEP_AUTOPILOT_4=དཀར་ཆག་ཁུངས།
STEP_AUTOPILOT_5=དཀར་ཆག་བུ་ཚུད་ཡོད་(~I)
STEP_AUTOPILOT_6=དམིགས་འཕེན་དཀར་ཆག
STEP_AUTOPILOT_7=ཐད་ཀར་རེའུ་མིག་སྲུང་སྐྱོབ་རྩིས་མེད་གཏོང་རྒྱུ་དེ་གནས་སྐབས་གཏན་འཁེལ་བྱེད་དགོས་མེད།
STEP_AUTOPILOT_10=ཡིག་དེབ་ཡིག་ཚགས་ནང་གི་ཡིག་དུམ་བཀར་བརྟ་དང་རེའུ་མིག
STATUSLINE_0=Conversion status:
STATUSLINE_1=དྲ་མིག་བཟོ་ལྟའི་བརྗེ་སྒྲུར་གྱི་རྣམ་པ།
STATUSLINE_2=བརྗེ་རྩིས་ཀྱི་ས་ཁོངས་ནང་བཅུག་ ལས་ཀའི་དེབ་ %2TotPageCount%2 ནང་གི་ %1Number%1
STATUSLINE_3=བརྗེ་རྩིས་ཀྱི་ས་ཁོངས་ནང་བཅུག...
STATUSLINE_4=རེའུ་མིག་རེའི་སྲུང་སྐྱོབ་སླང་ཡང་བསྐྱར་གསོ་བྱེད།...
STATUSLINE_5=དྲ་མིག་བཟོ་ལྟའི་ནང་དངུལ་ལོའི་ཚན་པའི་བརྗེ་སྒྱུར།...
MESSAGES_0=~Finish
MESSAGES_1=དཀར་ཆག་གཅིག་འདེམས་རོགས།
MESSAGES_2=ཁྱེད་རང་གི་ཡིག་ཆ་གཅིག་འདེམས་རོག
MESSAGES_3=དམིགས་འབེན་དཀར་ཆག་གཅིག་འདེམས་རོག
MESSAGES_4=non-existent
MESSAGES_5=ཡོ་སྒོར་བརྗེ་རྩིས་ཆས།
MESSAGES_6=སྲུང་སྐྱོབ་ཐོབཔའི་རེའུ་མིག་གནས་སྐབས་སྲུང་སྐྱོབ་རྩིས་མེད་གཏོང་ངམ།
MESSAGES_7=གསང་ཨང་གཅུག་རོགས་ རེའུ་མིག%1TableName%1སྲུང་སྐྱོབ་འཆིང་རྒྱབ་རྩེས་མེད་ཐོངས།
MESSAGES_8=གསང་ཨང་ཕན་མེད།
MESSAGES_9=སྲུང་སྐྱོབ་ཐོབ་པའི་རེའུ་མིག
MESSAGES_10=མཐའ་ཚིག
MESSAGES_11=རེའུ་མིག་གི་སྲུང་སྐྱོབ་རྩིས་མེད་གཏོང་ཐབས་བྲལ།
MESSAGES_12=རེའུ་མིག་གི་སྲུང་སྐྱོབ་རྩིས་མེད་གཏོང་ཐབས་བྲལ།
MESSAGES_13=རང་འགུལ་ཡིག་ཆའི་ལས་རོགས་ཀྱིས་ཡིག་ཚགས་འདེ་རྩོམ་སྒྲིག་བྱེད་ཐབས་བྲལ་ཡིག་ཚགས་ནང་སྲུང་སྐྱོབ་ཐོབ་པའི་ལས་ཁྲ་ཚུད་པའི་སྐབས་དྲ་མིག་གི་བའི་བཟོ་ལྟ་བཟོ་བཅོས་བྱེད་ཐབས་བྲལ་པའི་རྐྱེན་གྱི་་ཡིན།
MESSAGES_14=དོ་སྣང་བྱེད། ད་ལྟ་མིན་ན་ཡོ་སྒོར་བརྗེ་རྩིས་ཆས་ཀྱི་ཡིག་ཚགས་འདི་རྩོམ་སྒྲིག་བྱེད་ཐབས་བྲལ།
MESSAGES_15=སྔོན་ལ་སྒྱུར་བརྗེའི་བྱེད་པའི་དངུལ་ལོར་འདེམས་རོགས།
MESSAGES_16=Password:
MESSAGES_17=OK
MESSAGES_18=Cancel
MESSAGES_19=ཁྱེད་རང་གྱི་རྩོམ་སྒྲིག་བྱེད་པའི་ %PRODUCTNAME Calc ཡིག་ཚགས་འདེམས་རོག
MESSAGES_20='<1>' དཀར་ཆག་གཅིག་མ་རེད།
MESSAGES_21=ཡིག་ཚགས་ནི་བཅོས་འབྲི་འགོག་པ་ཡིན།
MESSAGES_22=The '<1>' file already exists.<CR>Do you want to overwrite it?
MESSAGES_23=Do you really want to terminate conversion at this point?
MESSAGES_24=Cancel Wizard
CURRENCIES_0=ཕོར་ཐི་ཀོ་ཨེ་སི་ཁུ་ཏོའི་སྐད།
CURRENCIES_1=ཧའོ་ལན་ཏུན།
CURRENCIES_2=ཕ་རོན་སིའི་ཧྥ་ལང་།
CURRENCIES_3=སི་ཕེན་སིའི་སེ་ཐ།
CURRENCIES_4=དབྱི་ཐ་ལིའི་ལིའི་ལ།
CURRENCIES_5=འཇར་ཕན་མ་ཁེ།
CURRENCIES_6=པེར་ཀྲེམ་གྱི་ཧྥ་ལང་།
CURRENCIES_7=ཨིར་ལན་པང་།
CURRENCIES_8=ལུ་ཙེམ་པོག་ཧྥ་ལང་།
CURRENCIES_9=ཨོ་སི་ཁྲུའུ་རི་ཡ་ཤན་ལིང་།
CURRENCIES_10=ཧྥིན་ལན་མ་ཁེ།
CURRENCIES_11=ཀེ་རི་སིའི་ཏེ་ལ་ཧེ་མ།
CURRENCIES_12=ཡིག་ཚགས་ནང་བཀོད་སྒྲིག་བྱེད་པའི་དངུལ་ལོ་ནི་ཡོ་རོབ་ལིན་གི་ཐུན་མོང་།
CURRENCIES_13=Cypriot Pound
CURRENCIES_14=Maltese Lira
CURRENCIES_15=Slovak Koruna
CURRENCIES_16=Estonian Kroon
CURRENCIES_17=Latvian Lats
CURRENCIES_18=Lithuanian Litas
STEP_LASTPAGE_0=ལེགས་གྲུབ་ཟིན་པ།
STEP_LASTPAGE_1=འབྲེལ་ཡོད་ཡིག་ཆ་ཞིབ་དཔྱད་དང་བཙལ་འཚོལ།...
STEP_LASTPAGE_2=ཡིག་ཚགས་སྒྲུར་བརྗེ།...
STEP_LASTPAGE_3=བཀོད་སྒྲིག
STEP_LASTPAGE_4=རང་འགུལ་རེའུ་མིག་གི་སྲུང་སྐྱོབ་རྩིས་མེད་གཏོང་བ།
STYLES_0=རྣམ་པ་གདམ་ག
STYLES_1=དེ་སྔའི་ཡིག་ཚགས་ཉར་ཚགས་བྱེད་སྐབས་ནོརའཁྲུལ་ཐོན་ཡོད་ གཤམ་གྱི་བཀོལ་སྤྱོད་རྩིས་མེད་གཏོང་ཐབས་བྲལ།
STYLES_2=~Cancel
STYLES_3=གཏན་འཁེལ་(~O)
STYLENAME_0=(Standard)
STYLENAME_1=Autumn Leaves
STYLENAME_2=Be
STYLENAME_3=Black and White
STYLENAME_4=Blackberry Bush
STYLENAME_5=Blue Jeans
STYLENAME_6=Fifties Diner
STYLENAME_7=Glacier
STYLENAME_8=Green Grapes
STYLENAME_9=སྔོ་ནག
STYLENAME_10=Millennium
STYLENAME_11=Nature
STYLENAME_12=Neon
STYLENAME_13=Night
STYLENAME_14=PC Nostalgia
STYLENAME_15=Pastel
STYLENAME_16=Pool Party
STYLENAME_17=Pumpkin
CorrespondenceDialog_0=གཏོང་ཡིག་ལེན་མཁན།
CorrespondenceDialog_1=ལེན་མཁན་གཅིག་རྐྱང།
CorrespondenceDialog_2=ལེན་མཁན་ཕལ་མོ་ཆེ། (འཕྲིན་གཏོང་དེབ་ཀྱི་གཞི་གྲངས་ཐོ་མཛོད)
CorrespondenceDialog_3=དཔེ་པང་བེད་སྤྱོད་བྱེད་རྒྱུ།
CorrespondenceMsgError=ནོར་སྐྱོན་བྱུང་བ།
CorrespondenceFields_0=འདིར་གནོན། འགོ་བཙུགས་ཏེ་ཡི་གེ་འཇུག
CorrespondenceFields_1=ཀུང་སི།
CorrespondenceFields_2=སྡེ་ཚན།
CorrespondenceFields_3=མིང་།
CorrespondenceFields_4=Last Name
CorrespondenceFields_5=ཁྲོམ་གཞུང་།
CorrespondenceFields_6=རྒྱལ་ཁབ།
CorrespondenceFields_7=སྦྲག་སྲིད་སྒྲིག་ཨང་།
CorrespondenceFields_8=གྲོང་ཁྱེར།
CorrespondenceFields_9=Title
CorrespondenceFields_10=Position
CorrespondenceFields_11=གཏོང་ཡིག་ལེན་མཁན་འབོད་སྟངས།
CorrespondenceFields_12=སྐུངས་ཡིག
CorrespondenceFields_13=Salutation
CorrespondenceFields_14=སྒེར་གྱི་ཁ་པར།
CorrespondenceFields_15=གཞུང་དོན་ཁང་པར།
CorrespondenceFields_16=བཪྙན་སྐྱེལ།
CorrespondenceFields_17=Email
CorrespondenceFields_18=URL
CorrespondenceFields_19=Notes
CorrespondenceFields_20=གྲ་སྒྲིག་སྒྲོམ་1
CorrespondenceFields_21=གྲ་སྒྲིག་སྒྲོམ་2
CorrespondenceFields_22=གྲ་སྒྲིག་སྒྲོམ་3
CorrespondenceFields_23=གྲ་སྒྲིག་སྒྲོམ་4
CorrespondenceFields_24=ID
CorrespondenceFields_25=State
CorrespondenceFields_26=གཞུང་ལས་ཁང་གི་ཁ་པར།
CorrespondenceFields_27=Pager
CorrespondenceFields_28=ལག་ཐོགས་ཁ་པར།
CorrespondenceFields_29=ཁ་པར་གཞན་དག
CorrespondenceFields_30=ལོ་ཐོ།
CorrespondenceFields_31=གདན་ཞུ།
CorrespondenceNoTextmark_0=ཤོག་འཛར་'Recipient' མིན་འདུག
CorrespondenceNoTextmark_1=སྦྲག་བསྐུར་ཟླ་སྒྲིལ་གྱི་ཡིག་དུམ་བར་འཇུག་བྱེད་ཐབས་བྲལ།
AgendaDlgName=ཟིན་ཐོ་འགོད་པའི་དཔེ་པང་།
AgendaDlgNoCancel=ངེས་པར་དུ་གདམ་ཚན་ཞིག་གདམ་དགོས།
AgendaDlgFrame=ཟིན་ཐོ་འགོད་པའི་རིགས།
AgendaDlgButton1=གྲོས་རིམ་བྱས་འབྲས་ཀྱི་ཟིན་ཐོ།
AgendaDlgButton2=གྲོས་རིམ་བརྒྱུད་རིམ་གྱི་ཟིན་ཐོ།
TextField=མཚན་ཉིད་བཞག་མེད་པའི་སྤྱོད་དུད་གྲངས་གཞིའི་ཡིག་དུམ།
NoDirCreation=The '%1' directory cannot be created:
MsgDirNotThere=དཀར་ཆག་གི་'%1'གནས་མིན་འདུག
QueryfornewCreation=དཀར་ཆག་དེ་བཟོ་མཁན་ཡིན་ནམ།
HelpButton=རོགས་རམ།(~H)
CancelButton=~Cancel
BackButton=< ~Back
NextButton=Ne~xt >
BeginButton=~Convert
CloseButton=~Close
WelcometextLabel1=This wizard convert legacy format documents to Open Document Format for Office Applications.
WelcometextLabel3=བརྗེ་སྒྱུར་བྱེད་རྒྱུའི་ཡིག་ཆའི་རིགས་གང་ཡིན་བདམས་དང་།
MSTemplateCheckbox_1_=Word དཔེ་པང་།
MSTemplateCheckbox_2_=Excel དཔེ་པང་།
MSTemplateCheckbox_3_=PowerPoint དཔེ་པང་།
MSDocumentCheckbox_1_=Word ཡིག་ཚགས།
MSDocumentCheckbox_2_=Excel ཡིག་ཚགས།
MSDocumentCheckbox_3_=PowerPoint/Publisher documents
MSContainerName=Microsoft Office
SummaryHeader=གནད་བསྡུས།
GroupnameDefault=ནང་འདྲེན་བྱས་པའི་དཔེ་པང་།
ProgressMoreDocs=ཡིག་ཚགས།
ProgressMoreTemplates=Templates
FileExists=The '<1>' file already exists.<CR>Do you want to overwrite it?
MorePathsError3=དཀར་ཆག་གནས་མེད་པ།
ConvertError1=Do you really want to terminate conversion at this point?
ConvertError2=Cancel Wizard
RTErrorDesc=An error has occurred in the wizard.
RTErrorHeader=ནོར་བ།
OverwriteallFiles=ཁྱེད་ཀྱིས་འདྲི་རྩད་མི་བྱེད་པར་ཐད་ཀར་ཡིག་ཆ་འབྲི་བཅོས་བྱེད་ཀྱི་ཡིན་ནམ།
ReeditMacro=ངེས་པར་དུ་ཡིག་ཚགས་རགས་བསྐྱར་དུ་རྩོམ་སྒྲིག་བྱེད་དགོས།
CouldNotsaveDocument=ཡིག་ཚགས་ '<1>'ཉར་ཐབས་བྲལ།
CouldNotopenDocument=ཡིག་ཚགས་'<1>' ཁ་འབྱེད་ཐབས་བྲལ།
PathDialogMessage=ཁྱེད་ཀྱིས་དཀར་ཆག་གཅིག་བདམས་དང་།
DialogTitle=ཡིག་ཚགས་སྒྱུར་ཆས།
SearchInSubDir=ཚུད་པའི་ཡན་ལག་དཀར་ཆག
ProgressPage1=ལེགས་གྲུབ་ཟིན་པ།
ProgressPage2=འབྲེལ་ཡོད་ཀྱི་ཡིག་ཚགས་ཕྱོགས་བསྒྲིགས།
ProgressPage3=ཡིག་ཚགས་སུ་ལྡིང་སྐོར་བྱེད་པ།
ProgressFound=ཪྙེད་ཟིན་པ།:
ProgressPage5="%1 found
Ready=ལེགས་སྒྲུབ།
SourceDocuments=གཞིའི་ཡིག་ཚགས།
TargetDocuments=དམིགས་ཡུལ་གྱི་ཡིག་ཆ།
LogfileSummary=<COUNT>ཡིག་ཆ་བརྗེ་སྒྱུར་ཟིན།
SumInclusiveSubDir=ཚུད་པའི་ཡན་ལག་དཀར་ཆག་ཡོད་ཚད།
SumSaveDokumente=གཤམ་གྱི་དཀར་ཆག་བར་ཕྱིར་འདོན་བྱེད།
TextImportLabel=ཡོང་ཁུངས་ནང་འཇུག
TextExportLabel=ལ་ཉར་ཚགས་བྱེད།
CreateLogfile=ཉིན་ཐོའི་ཡིག་ཆ་བཟོ་བ།
LogfileHelpText=ཁྱེད་ཀྱི་ལས་ཀའི་དཀར་ཆག་ནང་ཉིན་ཐོ་ཡིག་ཆ་གསར་བཟོ་བྱེད་དགོས།
ShowLogfile=ཉིན་ཐོའི་ཡིག་ཆ་གསལ་བ།་
SumMSTextDocuments=གཤམ་གྱི་དཀར་ཆག་ནང་གི་ Word ཚང་མ་ཡིག་ཚགས་ནང་འཇུག་པ།
SumMSTableDocuments=གཤམ་གྱི་དཀར་ཆག་ནང་གི་ Excel ཚང་མ་ཡིག་ཚགས་ནང་འཇུག་པ།
SumMSDrawDocuments=All PowerPoint/Publisher documents contained in the following directory will be imported:
SumMSTextTemplates=གཤམ་གྱི་དཀར་ཆག་ནང་གི་ Word ཚང་མ་དཔེ་པང་ནང་འཇུག་པ།
SumMSTableTemplates=གཤམ་གྱི་དཀར་ཆག་ནང་གི་ Excel ཚང་མ་དཔེ་པང་ནང་འཇུག་པ།
SumMSDrawTemplates=གཤམ་གྱི་དཀར་ཆག་ནང་གི་ PowerPoint ཚང་མ་དཔེ་པང་ནང་འཇུག་པ།
