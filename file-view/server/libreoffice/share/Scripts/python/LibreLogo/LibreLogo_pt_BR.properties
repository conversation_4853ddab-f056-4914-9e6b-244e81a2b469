# turtle graphics

FORWARD=paraFrente|pf
BACKWARD=paraTrás|pt
TURNLEFT=paraEsquerda|pe|girarEsquerda|giraEsquerda
TURNRIGHT=paraDireita|pd|girarDireita|giraDireita
PENUP=usarNada|un|useNada
PENDOWN=usarLápis|ul|useLápis
HOME=paraCentro|centro|pc
POINT=ponto
CIRCLE=círculo|circunferência
ELLIPSE=elipse
SQUARE=quadrado
RECTANGLE=retângulo
LABEL=rotular|rotule
PENCOLOR=mudarCorDoLápis|mCorLa|mudeCorDoLápis|mudecl
ANY=qualquer
PENWIDTH=mudarEspessuraDoLápis|mEspLa
PENSTYLE=mudarEstiloDoLápis|mEstLa
PENJOINT=mudarCantoDaLinha|mCanLi
PENCAP=mudarPontaDoLápis|mPonLa|mPonLi
NONE=nenhum
BEVEL=cortado
MITER=pontudo
ROUNDED=arredondado
SOLID=sólido
DASH=tracejado
DOTTED=pontilhado
CLOSE=fechar|feche
FILL=pintar|pinte|preencher
FILLCOLOR=mudarCorDaPintura|mCorPi|mudecp
FILLTRANSPARENCY=mudarTransparênciaDaPintura|mTraPi
PENTRANSPARENCY=mudarTransparênciaDoLápis|mTraLa|mTraLi
FILLSTYLE=mudarEstiloDaPintura|mEstPi
FONTCOLOR=mudarCorDaLetra|mCorLe
FONTTRANSPARENCY=transparẽnciafonte|transparênciatexto
FONTHEIGHT=mudarTamanhoDaLetra|mTamLe
FONTWEIGHT=mudarEspessuraDaLetra|mEspLe
FONTSTYLE=mudarEstiloDaLetra|mEstLe
BOLD=negrito
ITALIC=itálico
UPRIGHT=vertical
NORMAL=normal
FONTFAMILY=mudarTipoDaLetra|mTipLe
CLEARSCREEN=tartaruga|tat
TEXT=texto
HIDETURTLE=desaparecerTat|dt|desapareçaTat|ocultarTat|escondeTat
SHOWTURTLE=mostrarTat|mostreTat|at|apareçaTat|aparecerTat
POSITION=posicionar|pos
HEADING=mudarDireção|mDir|direção
PAGESIZE=tamanhoDaPágina|tamPág
GROUP=agrupar|grupo|grp|figura

# control structures

TO=aprender|aprenda
END=fim
STOP=parar|pare
REPEAT=repetir|repita
REPCOUNT=contVezes|conteVezes
BREAK=interromper|interrompa
CONTINUE=continuar|continue
WHILE=enquanto
FOR=para
IN=em
IF=se
OUTPUT=retornar|retorne|devolver|devolva|envie
LEFTSTRING=“|‘
RIGHTSTRING=”|’
TRUE=verdadeiro|verd
FALSE=falso
NOT=não
AND=e
OR=ou
INPUT=ler|leia
PRINT=escrever|esc|escreva
SLEEP=esperar|espere
GLOBAL=global

# functions
RANDOM=aleatório|sorteieNúmero|sortNum
INT=int
FLOAT=real
STR=str
SQRT=raiz|raizQ
LOG10=log10
SIN=sen
COS=cos
ROUND=arred|arredonde
ABS=abs
COUNT=contagem
SET=conjunto
RANGE=intervalo
LIST=lista
TUPLE=tupla
SORTED=ordenado
RESUB=subst
RESEARCH=pesquisa
REFINDALL=localizaTudo
MIN=mínimo|mín
MAX=máximo|máx

PI=pi|π

# measurement
DECIMAL=.
DEG=°
HOUR=h
MM=mm
CM=cm
PT=pt
INCH=pol|"

# color codes

INVISIBLE=invisível
BLACK=preto
SILVER=prata
GRAY=cinza
WHITE=branco
MAROON=castanho
RED=vermelho
PURPLE=roxo
FUCHSIA=magenta
GREEN=verde
LIME=lima
OLIVE=oliva
YELLOW=amarelo
NAVY=marinho
BLUE=azul
TEAL=turquesa
AQUA=ciano
PINK=rosa
TOMATO=tomate
ORANGE=laranja
GOLD=ouro
VIOLET=violeta
SKYBLUE=celeste
CHOCOLATE=chocolate
BROWN=marrom

# messages

LIBRELOGO=LibreLogo
ERROR=Erro (na linha %s)
ERR_ZERODIVISION=Divisão por zero.
ERR_NAME=Nome desconhecido: “%s”
ERR_ARGUMENTS=%s usa %s argumentos (%s definidos).
ERR_BLOCK=Erro (há espaços faltando ou a mais nos colchetes?)
ERR_KEY=Elemento desconhecido: %s
ERR_INDEX=Índice fora do intervalo.

ERR_STOP=Programa encerrado:
ERR_MAXRECURSION=profundidade máxima de recursão (%d) excedida.
ERR_MEMORY=memória insuficiente.
ERR_NOTAPROGRAM=Deseja executar este documento de texto?
