<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.20.0 -->
<interface domain="dba">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkMenu" id="menu">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <child>
      <object class="GtkMenuItem" id="width">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="querycolmenu|width">Column _Width...</property>
        <property name="use_underline">True</property>
      </object>
    </child>
    <child>
      <object class="GtkSeparatorMenuItem" id="menuitem1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="delete">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="querycolmenu|delete">_Delete</property>
        <property name="use_underline">True</property>
      </object>
    </child>
  </object>
</interface>
