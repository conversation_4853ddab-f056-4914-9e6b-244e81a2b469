<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.22.1 -->
<interface domain="dba">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkAdjustment" id="adjustment1">
    <property name="upper">65535</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkBox" id="MysqlNativeSettings">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="hexpand">True</property>
    <property name="vexpand">True</property>
    <property name="border_width">6</property>
    <property name="orientation">vertical</property>
    <property name="spacing">12</property>
    <child>
      <object class="GtkBox" id="box1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="spacing">12</property>
        <child>
          <object class="GtkLabel" id="dbnamelabel">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="halign">start</property>
            <property name="label" translatable="yes" context="mysqlnativesettings|dbnamelabel">_Database name:</property>
            <property name="use_underline">True</property>
            <property name="mnemonic_widget">dbname</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkEntry" id="dbname">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="hexpand">True</property>
            <property name="truncate-multiline">True</property>
            <property name="activates_default">True</property>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
      <packing>
        <property name="expand">False</property>
        <property name="fill">True</property>
        <property name="position">0</property>
      </packing>
    </child>
    <child>
      <!-- n-columns=1 n-rows=1 -->
      <object class="GtkGrid" id="grid4">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="row_spacing">6</property>
        <child>
          <object class="GtkRadioButton" id="hostport">
            <property name="label" translatable="yes" context="mysqlnativesettings|hostport">Se_rver/port</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">False</property>
            <property name="use_underline">True</property>
            <property name="xalign">0</property>
            <property name="active">True</property>
            <property name="draw_indicator">True</property>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkAlignment" id="alignment1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="left_padding">12</property>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid" id="grid1">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="row_spacing">6</property>
                <property name="column_spacing">12</property>
                <child>
                  <object class="GtkLabel" id="serverlabel">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="mysqlnativesettings|serverlabel">_Server:</property>
                    <property name="use_underline">True</property>
                    <property name="mnemonic_widget">server</property>
                    <property name="xalign">1</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="portlabel">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="mysqlnativesettings|portlabel">_Port:</property>
                    <property name="use_underline">True</property>
                    <property name="mnemonic_widget">port</property>
                    <property name="xalign">1</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkEntry" id="server">
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="hexpand">True</property>
                    <property name="truncate-multiline">True</property>
                    <property name="activates_default">True</property>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">0</property>
                    <property name="width">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="defaultport">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="label" translatable="yes" context="mysqlnativesettings|defaultport">Default: 3306</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">2</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkSpinButton" id="port">
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="hexpand">True</property>
                    <property name="activates_default">True</property>
                    <property name="adjustment">adjustment1</property>
                    <property name="truncate-multiline">True</property>
                    <property name="numeric">True</property>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">1</property>
          </packing>
        </child>
      </object>
      <packing>
        <property name="expand">False</property>
        <property name="fill">True</property>
        <property name="position">1</property>
      </packing>
    </child>
    <child>
      <!-- n-columns=1 n-rows=1 -->
      <object class="GtkGrid" id="grid2">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="row_spacing">6</property>
        <property name="column_spacing">12</property>
        <child>
          <object class="GtkRadioButton" id="socketlabel">
            <property name="label" translatable="yes" context="mysqlnativesettings|socketlabel">So_cket:</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">False</property>
            <property name="use_underline">True</property>
            <property name="xalign">0</property>
            <property name="draw_indicator">True</property>
            <property name="group">hostport</property>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkEntry" id="socket">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="hexpand">True</property>
            <property name="truncate-multiline">True</property>
            <property name="activates_default">True</property>
          </object>
          <packing>
            <property name="left_attach">1</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
      </object>
      <packing>
        <property name="expand">False</property>
        <property name="fill">True</property>
        <property name="position">3</property>
      </packing>
    </child>
    <child>
      <!-- n-columns=1 n-rows=1 -->
      <object class="GtkGrid" id="grid3">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="row_spacing">6</property>
        <property name="column_spacing">12</property>
        <child>
          <object class="GtkRadioButton" id="namedpipelabel">
            <property name="label" translatable="yes" context="mysqlnativesettings|namedpipelabel">Named p_ipe:</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">False</property>
            <property name="use_underline">True</property>
            <property name="xalign">0</property>
            <property name="draw_indicator">True</property>
            <property name="group">hostport</property>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkEntry" id="namedpipe">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="hexpand">True</property>
            <property name="truncate-multiline">True</property>
            <property name="activates_default">True</property>
          </object>
          <packing>
            <property name="left_attach">1</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
      </object>
      <packing>
        <property name="expand">False</property>
        <property name="fill">True</property>
        <property name="position">4</property>
      </packing>
    </child>
  </object>
</interface>
