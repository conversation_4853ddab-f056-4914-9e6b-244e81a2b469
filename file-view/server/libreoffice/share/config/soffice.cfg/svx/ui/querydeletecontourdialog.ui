<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.20.2 -->
<interface domain="svx">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkMessageDialog" id="QueryDeleteContourDialog">
    <property name="can_focus">False</property>
    <property name="title" translatable="yes" context="querydeletecontourdialog|QueryDeleteContourDialog">Delete the contour?</property>
    <property name="resizable">False</property>
    <property name="modal">True</property>
    <property name="type_hint">dialog</property>
    <property name="skip_taskbar_hint">True</property>
    <property name="message_type">question</property>
    <property name="buttons">yes-no</property>
    <property name="text" translatable="yes" context="querydeletecontourdialog|QueryDeleteContourDialog">Setting a new workspace will
cause the contour to be deleted.</property>
    <property name="secondary_text" translatable="yes" context="querydeletecontourdialog|QueryDeleteContourDialog">Are you sure you want to continue?</property>
    <child internal-child="vbox">
      <object class="GtkBox" id="messagedialog-vbox">
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <child internal-child="action_area">
          <object class="GtkButtonBox" id="messagedialog-action_area">
            <property name="can_focus">False</property>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="pack_type">end</property>
            <property name="position">0</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>
