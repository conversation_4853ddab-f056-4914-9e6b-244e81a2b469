<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.22.1 -->
<interface domain="svx">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkImage" id="image1">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="icon_name">svx/res/perspective_16.png</property>
  </object>
  <object class="GtkImage" id="image2">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="icon_name">svx/res/parallel_16.png</property>
  </object>
  <object class="GtkPopover" id="DirectionWindow">
    <property name="can_focus">False</property>
    <property name="no_show_all">True</property>
    <property name="border_width">4</property>
    <child>
      <object class="GtkBox" id="container">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">6</property>
        <child>
          <object class="GtkScrolledWindow" id="valuesetwin">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="hexpand">True</property>
            <property name="vexpand">True</property>
            <property name="hscrollbar_policy">never</property>
            <property name="vscrollbar_policy">never</property>
            <property name="shadow_type">in</property>
            <child>
              <object class="GtkViewport">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <child>
                  <object class="GtkDrawingArea" id="valueset">
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="events">GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_KEY_PRESS_MASK | GDK_KEY_RELEASE_MASK | GDK_STRUCTURE_MASK</property>
                    <property name="hexpand">True</property>
                    <property name="vexpand">True</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkRadioButton" id="perspective">
            <property name="label" translatable="yes" context="directionwindow|RID_SVXSTR_PERSPECTIVE">_Perspective</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">False</property>
            <property name="image">image1</property>
            <property name="use_underline">True</property>
            <property name="xalign">0</property>
            <property name="always_show_image">True</property>
            <property name="active">True</property>
            <property name="draw_indicator">True</property>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
        <child>
          <object class="GtkRadioButton" id="parallel">
            <property name="label" translatable="yes" context="dockingwindow|RID_SVXSTR_PARALLEL">P_arallel</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">False</property>
            <property name="image">image2</property>
            <property name="use_underline">True</property>
            <property name="xalign">0</property>
            <property name="always_show_image">True</property>
            <property name="draw_indicator">True</property>
            <property name="group">perspective</property>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">2</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>
