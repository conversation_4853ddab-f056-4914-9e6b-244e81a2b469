<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="cui">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkListStore" id="liststore1">
    <columns>
      <!-- column-name text -->
      <column type="gchararray"/>
      <!-- column-name id -->
      <column type="gchararray"/>
      <!-- column-name image -->
      <column type="GdkPixbuf"/>
    </columns>
  </object>
  <object class="GtkListStore" id="liststore2">
    <columns>
      <!-- column-name text -->
      <column type="gchararray"/>
      <!-- column-name id -->
      <column type="gchararray"/>
      <!-- column-name image -->
      <column type="GdkPixbuf"/>
    </columns>
  </object>
  <object class="GtkListStore" id="liststore3">
    <columns>
      <!-- column-name text -->
      <column type="gchararray"/>
      <!-- column-name id -->
      <column type="gchararray"/>
      <!-- column-name image -->
      <column type="GdkPixbuf"/>
    </columns>
  </object>
  <!-- n-columns=1 n-rows=1 -->
  <object class="GtkGrid" id="OptLanguagesPage">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="row_spacing">12</property>
    <child>
      <object class="GtkFrame" id="frame1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="label_xalign">0</property>
        <property name="shadow_type">none</property>
        <child>
          <object class="GtkAlignment" id="alignment1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="top_padding">5</property>
            <property name="left_padding">12</property>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid" id="grid1">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="row_spacing">6</property>
                <property name="column_spacing">12</property>
                <child>
                  <object class="GtkLabel" id="label4">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="optlanguagespage|label4">_User interface:</property>
                    <property name="use_underline">True</property>
                    <property name="mnemonic_widget">userinterface</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkComboBoxText" id="userinterface">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="userinterface-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|userinterface">Select the language used for the user interface, for example menus, dialogs, help files. You must have installed at least one additional language pack or a multi-language version of %PRODUCTNAME.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child type="label">
          <object class="GtkLabel" id="label1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="optlanguagespage|label1">Language Of</property>
            <attributes>
              <attribute name="weight" value="bold"/>
            </attributes>
          </object>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">0</property>
      </packing>
    </child>
    <child>
      <object class="GtkFrame" id="frame2">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="label_xalign">0</property>
        <property name="shadow_type">none</property>
        <child>
          <object class="GtkAlignment" id="alignment2">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="top_padding">6</property>
            <property name="left_padding">12</property>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid" id="grid2">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="row_spacing">6</property>
                <property name="column_spacing">12</property>
                <child>
                  <object class="GtkComboBox" id="westernlanguage">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="model">liststore1</property>
                    <property name="entry_text_column">0</property>
                    <property name="id_column">1</property>
                    <child>
                      <object class="GtkCellRendererText" id="cellrenderertext1"/>
                      <attributes>
                        <attribute name="text">0</attribute>
                      </attributes>
                    </child>
                    <child>
                      <object class="GtkCellRendererPixbuf" id="cellrenderertext2"/>
                      <attributes>
                        <attribute name="pixbuf">2</attribute>
                      </attributes>
                    </child>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="westernlanguage-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|westernlanguage">Specifies the language used for the spellcheck function in western alphabets.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkComboBox" id="asianlanguage">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="model">liststore2</property>
                    <property name="entry_text_column">0</property>
                    <property name="id_column">1</property>
                    <child>
                      <object class="GtkCellRendererText" id="cellrenderertext3"/>
                      <attributes>
                        <attribute name="text">0</attribute>
                      </attributes>
                    </child>
                    <child>
                      <object class="GtkCellRendererPixbuf" id="cellrenderertext4"/>
                      <attributes>
                        <attribute name="pixbuf">2</attribute>
                      </attributes>
                    </child>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="asianlanguage-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|asianlanguage">Specifies the language used for the spellcheck function in Asian alphabets.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkComboBox" id="complexlanguage">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="model">liststore3</property>
                    <property name="entry_text_column">0</property>
                    <property name="id_column">1</property>
                    <child>
                      <object class="GtkCellRendererText" id="cellrenderertext5"/>
                      <attributes>
                        <attribute name="text">0</attribute>
                      </attributes>
                    </child>
                    <child>
                      <object class="GtkCellRendererPixbuf" id="cellrenderertext6"/>
                      <attributes>
                        <attribute name="pixbuf">2</attribute>
                      </attributes>
                    </child>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="complexlanguage-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|complexlanguage">Specifies the language for the complex text layout spellcheck.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkCheckButton" id="currentdoc">
                    <property name="label" translatable="yes" context="optlanguagespage|currentdoc">For the current document only</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">False</property>
                    <property name="halign">start</property>
                    <property name="use_underline">True</property>
                    <property name="xalign">0</property>
                    <property name="draw_indicator">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="currentdoc-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|currentdoc">Specifies that the settings for default languages are valid only for the current document.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">3</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkCheckButton" id="ctlsupport">
                    <property name="label" translatable="yes" context="optlanguagespage|ctlsupport">Complex _text layout:</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">False</property>
                    <property name="use_underline">True</property>
                    <property name="xalign">0</property>
                    <property name="draw_indicator">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="ctlsupport-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|ctlsupport">Activates complex text layout support. You can now modify the settings corresponding to complex text layout in %PRODUCTNAME.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkCheckButton" id="asiansupport">
                    <property name="label" translatable="yes" context="optlanguagespage|asiansupport">Asian:</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">False</property>
                    <property name="use_underline">True</property>
                    <property name="xalign">0</property>
                    <property name="draw_indicator">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="asiansupport-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|asiansupport">Activates Asian languages support. You can now modify the corresponding Asian language settings in %PRODUCTNAME.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="western">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="optlanguagespage|western">Western:</property>
                    <property name="use_underline">True</property>
                    <property name="mnemonic_widget">westernlanguage</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <placeholder/>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child type="label">
          <object class="GtkLabel" id="label2">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="optlanguagespage|label2">Default Languages for Documents</property>
            <attributes>
              <attribute name="weight" value="bold"/>
            </attributes>
          </object>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">2</property>
      </packing>
    </child>
    <child>
      <object class="GtkFrame" id="frame3">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label_xalign">0</property>
        <property name="shadow_type">none</property>
        <child>
          <object class="GtkAlignment" id="alignment3">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="top_padding">6</property>
            <property name="left_padding">12</property>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid" id="grid3">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="row_spacing">6</property>
                <child>
                  <object class="GtkCheckButton" id="ignorelanguagechange">
                    <property name="label" translatable="yes" context="optlanguagespage|ignorelanguagechange">Ignore s_ystem input language</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">False</property>
                    <property name="use_underline">True</property>
                    <property name="xalign">0</property>
                    <property name="draw_indicator">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="ignorelanguagechange-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|ignorelanguagechange">Indicates whether changes to the system input language/keyboard will be ignored. If ignored, when new text is typed that text will follow the language of the document or current paragraph, not the current system language.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child type="label">
          <object class="GtkLabel" id="label3">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="optlanguagespage|label3">Enhanced Language Support</property>
            <attributes>
              <attribute name="weight" value="bold"/>
            </attributes>
          </object>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">3</property>
      </packing>
    </child>
    <child>
      <object class="GtkFrame" id="frame4">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="label_xalign">0</property>
        <property name="shadow_type">none</property>
        <child>
          <object class="GtkAlignment" id="alignment4">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="top_padding">5</property>
            <property name="left_padding">12</property>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid" id="grid4">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="row_spacing">6</property>
                <property name="column_spacing">12</property>
                <child>
                  <object class="GtkLabel" id="localesettingFT">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="optlanguagespage|localesettingFT">Locale setting:</property>
                    <property name="use_underline">True</property>
                    <property name="mnemonic_widget">localesetting</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="label6">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="optlanguagespage|label6">Decimal separator key:</property>
                    <property name="use_underline">True</property>
                    <property name="mnemonic_widget">decimalseparator</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="defaultcurrency">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="optlanguagespage|defaultcurrency">_Default currency:</property>
                    <property name="use_underline">True</property>
                    <property name="mnemonic_widget">currencylb</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="dataaccpatterns">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="optlanguagespage|dataaccpatterns">Date acceptance _patterns:</property>
                    <property name="use_underline">True</property>
                    <property name="mnemonic_widget">datepatterns</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">3</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkComboBoxText" id="localesetting">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="localesetting-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|localesetting">Specifies the locale setting of the country setting. This influences settings for numbering, currency and units of measure.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkComboBoxText" id="currencylb">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="currencylb-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|currencylb">Specifies the default currency that is used for the currency format and the currency fields.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkEntry" id="datepatterns">
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="halign">start</property>
                    <property name="valign">center</property>
                    <property name="hexpand">True</property>
                    <property name="truncate-multiline">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="datepatterns-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|datepatterns">Specifies the date acceptance patterns for the current locale. Calc spreadsheet and Writer table cell input needs to match locale dependent date acceptance patterns before it is recognized as a valid date.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">3</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkCheckButton" id="decimalseparator">
                    <property name="label" translatable="yes" context="optlanguagespage|decimalseparator">_Same as locale setting ( %1 )</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">False</property>
                    <property name="use_underline">True</property>
                    <property name="xalign">0</property>
                    <property name="draw_indicator">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="decimalseparator-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|decimalseparator">Specifies to use the decimal separator key that is set in your system when you press the respective key on the number pad.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child type="label">
          <object class="GtkLabel" id="label7">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="optlanguagespage|label7">Formats</property>
            <attributes>
              <attribute name="weight" value="bold"/>
            </attributes>
          </object>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">1</property>
      </packing>
    </child>
    <child internal-child="accessible">
      <object class="AtkObject" id="OptLanguagesPage-atkobject">
        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|OptLanguagesPage">Defines the default languages and some other locale settings for documents.</property>
      </object>
    </child>
  </object>
  <object class="GtkSizeGroup" id="sizegroup1">
    <widgets>
      <widget name="label4"/>
      <widget name="localesettingFT"/>
      <widget name="label6"/>
      <widget name="defaultcurrency"/>
      <widget name="dataaccpatterns"/>
      <widget name="ctlsupport"/>
      <widget name="asiansupport"/>
      <widget name="western"/>
    </widgets>
  </object>
  <object class="GtkSizeGroup" id="sizegroup2">
    <widgets>
      <widget name="userinterface"/>
      <widget name="localesetting"/>
      <widget name="currencylb"/>
      <widget name="datepatterns"/>
      <widget name="decimalseparator"/>
      <widget name="westernlanguage"/>
      <widget name="asianlanguage"/>
      <widget name="complexlanguage"/>
    </widgets>
  </object>
</interface>
