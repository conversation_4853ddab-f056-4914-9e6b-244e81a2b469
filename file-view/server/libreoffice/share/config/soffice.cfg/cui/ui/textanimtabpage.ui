<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="cui">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkAdjustment" id="adjustmentAMOUNT">
    <property name="upper">100</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustmentCOUNT">
    <property name="upper">100</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustmentDELAY">
    <property name="upper">30000</property>
    <property name="step-increment">50</property>
    <property name="page-increment">500</property>
  </object>
  <object class="GtkImage" id="imageDOWN">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="stock">gtk-go-down</property>
  </object>
  <object class="GtkImage" id="imageLEFT">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="stock">gtk-go-back</property>
  </object>
  <object class="GtkImage" id="imageRIGHT">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="stock">gtk-go-forward</property>
  </object>
  <object class="GtkImage" id="imageUP">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="stock">gtk-go-up</property>
  </object>
  <object class="GtkBox" id="TextAnimation">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="orientation">vertical</property>
    <property name="spacing">6</property>
    <child>
      <object class="GtkFrame">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label_xalign">0</property>
        <property name="shadow_type">none</property>
        <child>
          <object class="GtkAlignment">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="top_padding">6</property>
            <property name="left_padding">12</property>
            <child>
              <object class="GtkBox" id="box2">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="spacing">12</property>
                <child>
                  <object class="GtkComboBoxText" id="LB_EFFECT">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="valign">center</property>
                    <items>
                      <item id="0" translatable="yes" context="textanimtabpage|liststoreEFFECT">No Effect</item>
                      <item id="1" translatable="yes" context="textanimtabpage|liststoreEFFECT">Blink</item>
                      <item id="2" translatable="yes" context="textanimtabpage|liststoreEFFECT">Scroll Through</item>
                      <item id="3" translatable="yes" context="textanimtabpage|liststoreEFFECT">Scroll Back and Forth</item>
                      <item id="4" translatable="yes" context="textanimtabpage|liststoreEFFECT">Scroll In</item>
                    </items>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="LB_EFFECT-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|LB_EFFECT">Select the animation effect that you want to apply to the text in the selected drawing object. To remove an animation effect, select No Effect.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkBox" id="boxDIRECTION">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="spacing">12</property>
                    <child>
                      <object class="GtkLabel" id="FT_DIRECTION">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="label" translatable="yes" context="textanimtabpage|FT_DIRECTION">Direction:</property>
                        <property name="use_underline">True</property>
                        <property name="mnemonic_widget">grid1</property>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <!-- n-columns=1 n-rows=1 -->
                      <object class="GtkGrid" id="grid1">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <child>
                          <object class="GtkToggleButton" id="BTN_UP">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">True</property>
                            <property name="tooltip_text" translatable="yes" context="textanimtabpage|BTN_UP|tooltip_text">To top</property>
                            <property name="halign">center</property>
                            <property name="hexpand">True</property>
                            <property name="image">imageUP</property>
                            <property name="always_show_image">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="BTN_UP-atkobject">
                                <property name="AtkObject::accessible-name" translatable="yes" context="textanimtabpage|BTN_UP-atkobject">Up</property>
                                <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|BTN_UP">Scrolls text from bottom to top.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkToggleButton" id="BTN_RIGHT">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">True</property>
                            <property name="tooltip_text" translatable="yes" context="textanimtabpage|BTN_RIGHT|tooltip_text">To right</property>
                            <property name="halign">center</property>
                            <property name="hexpand">True</property>
                            <property name="image">imageRIGHT</property>
                            <property name="always_show_image">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="BTN_RIGHT-atkobject">
                                <property name="AtkObject::accessible-name" translatable="yes" context="textanimtabpage|BTN_RIGHT-atkobject">Right</property>
                                <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|BTN_RIGHT">Scrolls text from left to right.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">2</property>
                            <property name="top_attach">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkToggleButton" id="BTN_LEFT">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">True</property>
                            <property name="tooltip_text" translatable="yes" context="textanimtabpage|BTN_LEFT|tooltip_text">To left</property>
                            <property name="halign">center</property>
                            <property name="hexpand">True</property>
                            <property name="image">imageLEFT</property>
                            <property name="always_show_image">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="BTN_LEFT-atkobject">
                                <property name="AtkObject::accessible-name" translatable="yes" context="textanimtabpage|BTN_LEFT-atkobject">Left</property>
                                <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|BTN_LEFT">Scrolls text from right to left.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkToggleButton" id="BTN_DOWN">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">True</property>
                            <property name="tooltip_text" translatable="yes" context="textanimtabpage|BTN_DOWN|tooltip_text">To bottom</property>
                            <property name="halign">center</property>
                            <property name="hexpand">True</property>
                            <property name="image">imageDOWN</property>
                            <property name="always_show_image">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="BTN_DOWN-atkobject">
                                <property name="AtkObject::accessible-name" translatable="yes" context="textanimtabpage|BTN_DOWN-atkobject">Down</property>
                                <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|BTN_DOWN">Scrolls text from top to bottom.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">2</property>
                          </packing>
                        </child>
                        <child>
                          <placeholder/>
                        </child>
                        <child>
                          <placeholder/>
                        </child>
                        <child>
                          <placeholder/>
                        </child>
                        <child>
                          <placeholder/>
                        </child>
                        <child>
                          <placeholder/>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">1</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child type="label">
          <object class="GtkLabel" id="FT_EFFECTS">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="textanimtabpage|FT_EFFECTS">E_ffect</property>
            <property name="use_underline">True</property>
            <property name="mnemonic_widget">LB_EFFECT</property>
            <attributes>
              <attribute name="weight" value="bold"/>
            </attributes>
          </object>
        </child>
      </object>
      <packing>
        <property name="expand">False</property>
        <property name="fill">True</property>
        <property name="position">0</property>
      </packing>
    </child>
    <child>
      <object class="GtkFrame" id="FL_PROPERTIES">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label_xalign">0</property>
        <property name="shadow_type">none</property>
        <child>
          <object class="GtkAlignment" id="alignment2">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="top_padding">6</property>
            <property name="left_padding">12</property>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid" id="box4">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="row_spacing">6</property>
                <property name="column_spacing">12</property>
                <child>
                  <object class="GtkCheckButton" id="TSB_START_INSIDE">
                    <property name="label" translatable="yes" context="textanimtabpage|TSB_START_INSIDE">S_tart inside</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">False</property>
                    <property name="use_underline">True</property>
                    <property name="xalign">0</property>
                    <property name="inconsistent">True</property>
                    <property name="draw_indicator">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="TSB_START_INSIDE-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|TSB_START_INSIDE">Text is visible and inside the drawing object when the effect is applied.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                    <property name="width">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkCheckButton" id="TSB_STOP_INSIDE">
                    <property name="label" translatable="yes" context="textanimtabpage|TSB_STOP_INSIDE">Text _visible when exiting</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">False</property>
                    <property name="use_underline">True</property>
                    <property name="xalign">0</property>
                    <property name="inconsistent">True</property>
                    <property name="draw_indicator">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="TSB_STOP_INSIDE-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|TSB_STOP_INSIDE">Text remains visible after the effect is applied.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">1</property>
                    <property name="width">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="FT_COUNT">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textanimtabpage|FT_COUNT">Animation cycles:</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkBox" id="boxCOUNT">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="margin_left">12</property>
                    <property name="spacing">12</property>
                    <child>
                      <object class="GtkCheckButton" id="TSB_ENDLESS">
                        <property name="label" translatable="yes" context="textanimtabpage|TSB_ENDLESS">_Continuous</property>
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="receives_default">False</property>
                        <property name="use_underline">True</property>
                        <property name="xalign">0</property>
                        <property name="inconsistent">True</property>
                        <property name="draw_indicator">True</property>
                        <accessibility>
                          <relation type="label-for" target="NUM_FLD_COUNT"/>
                        </accessibility>
                        <child internal-child="accessible">
                          <object class="AtkObject" id="TSB_ENDLESS-atkobject">
                            <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|TSB_ENDLESS">Plays the animation effect continuously. To specify the number of times to play the effect, clear this checkbox, and enter a number in the Continuous box.</property>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkSpinButton" id="NUM_FLD_COUNT">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="halign">start</property>
                        <property name="activates_default">True</property>
                        <property name="truncate-multiline">True</property>
                        <property name="adjustment">adjustmentCOUNT</property>
                        <accessibility>
                          <relation type="labelled-by" target="TSB_ENDLESS"/>
                        </accessibility>
                        <child internal-child="accessible">
                          <object class="AtkObject" id="NUM_FLD_COUNT-atkobject">
                            <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|NUM_FLD_COUNT">Enter the number of times that you want the animation effect to repeat.</property>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="FT_AMOUNT">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textanimtabpage|FT_AMOUNT">Increment:</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">3</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkBox" id="boxAMOUNT">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="margin_left">12</property>
                    <property name="spacing">12</property>
                    <child>
                      <object class="GtkCheckButton" id="TSB_PIXEL">
                        <property name="label" translatable="yes" context="textanimtabpage|TSB_PIXEL">_Pixels</property>
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="receives_default">False</property>
                        <property name="use_underline">True</property>
                        <property name="xalign">0</property>
                        <property name="inconsistent">True</property>
                        <property name="draw_indicator">True</property>
                        <accessibility>
                          <relation type="label-for" target="MTR_FLD_AMOUNT"/>
                        </accessibility>
                        <child internal-child="accessible">
                          <object class="AtkObject" id="TSB_PIXEL-atkobject">
                            <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|TSB_PIXEL">Measures increment value in pixels.</property>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkSpinButton" id="MTR_FLD_AMOUNT">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="halign">start</property>
                        <property name="activates_default">True</property>
                        <property name="truncate-multiline">True</property>
                        <property name="adjustment">adjustmentAMOUNT</property>
                        <accessibility>
                          <relation type="labelled-by" target="TSB_PIXEL"/>
                        </accessibility>
                        <child internal-child="accessible">
                          <object class="AtkObject" id="MTR_FLD_AMOUNT-atkobject">
                            <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|MTR_FLD_AMOUNT">Enter the number of increments by which to scroll the text.</property>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">3</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="FT_DELAY">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textanimtabpage|FT_DELAY">Delay:</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">4</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkBox" id="boxDELAY">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="halign">start</property>
                    <property name="margin_left">12</property>
                    <property name="spacing">12</property>
                    <child>
                      <object class="GtkCheckButton" id="TSB_AUTO">
                        <property name="label" translatable="yes" context="textanimtabpage|TSB_AUTO">_Automatic</property>
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="receives_default">False</property>
                        <property name="use_underline">True</property>
                        <property name="xalign">0</property>
                        <property name="inconsistent">True</property>
                        <property name="draw_indicator">True</property>
                        <accessibility>
                          <relation type="label-for" target="MTR_FLD_DELAY"/>
                        </accessibility>
                        <child internal-child="accessible">
                          <object class="AtkObject" id="TSB_AUTO-atkobject">
                            <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|TSB_AUTO">%PRODUCTNAME automatically determines the amount of time to wait before repeating the effect. To manually assign the delay period, clear this checkbox, and then enter a value in the Automatic box.</property>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkSpinButton" id="MTR_FLD_DELAY">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="halign">start</property>
                        <property name="activates_default">True</property>
                        <property name="truncate-multiline">True</property>
                        <property name="adjustment">adjustmentDELAY</property>
                        <accessibility>
                          <relation type="labelled-by" target="TSB_AUTO"/>
                        </accessibility>
                        <child internal-child="accessible">
                          <object class="AtkObject" id="MTR_FLD_DELAY-atkobject">
                            <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|MTR_FLD_DELAY">Enter the amount of time to wait before repeating the effect.</property>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">4</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child type="label">
          <object class="GtkLabel" id="label2">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="textanimtabpage|label2">Properties</property>
            <property name="xalign">0</property>
            <attributes>
              <attribute name="weight" value="bold"/>
            </attributes>
          </object>
        </child>
      </object>
      <packing>
        <property name="expand">False</property>
        <property name="fill">True</property>
        <property name="position">1</property>
      </packing>
    </child>
    <child internal-child="accessible">
      <object class="AtkObject" id="TextAnimation-atkobject">
        <property name="AtkObject::accessible-description" translatable="yes" context="textanimtabpage|extended_tip|TextAnimation">Adds an animation effect to the text in the selected drawing object.</property>
      </object>
    </child>
  </object>
  <object class="GtkSizeGroup">
    <widgets>
      <widget name="NUM_FLD_COUNT"/>
      <widget name="MTR_FLD_AMOUNT"/>
      <widget name="MTR_FLD_DELAY"/>
    </widgets>
  </object>
  <object class="GtkSizeGroup" id="sizegroup1">
    <widgets>
      <widget name="TSB_ENDLESS"/>
      <widget name="TSB_PIXEL"/>
      <widget name="TSB_AUTO"/>
    </widgets>
  </object>
</interface>
