<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.22.1 -->
<interface domain="sc">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkDialog" id="TextImportOptionsDialog">
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="title" translatable="yes" context="textimportoptions|TextImportOptionsDialog">Import Options</property>
    <property name="modal">True</property>
    <property name="default_width">0</property>
    <property name="default_height">0</property>
    <property name="type_hint">dialog</property>
    <child>
      <placeholder/>
    </child>
    <child internal-child="vbox">
      <object class="GtkBox" id="dialog-vbox1">
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <child internal-child="action_area">
          <object class="GtkButtonBox" id="dialog-action_area1">
            <property name="can_focus">False</property>
            <property name="layout_style">end</property>
            <child>
              <object class="GtkButton" id="ok">
                <property name="label">gtk-ok</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="can_default">True</property>
                <property name="has_default">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="cancel">
                <property name="label">gtk-cancel</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="help">
                <property name="label">gtk-help</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">2</property>
                <property name="secondary">True</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="pack_type">end</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkBox" id="box1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="orientation">vertical</property>
            <property name="spacing">12</property>
            <child>
              <object class="GtkFrame" id="frame2">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="label_xalign">0</property>
                <property name="shadow_type">none</property>
                <child>
                  <object class="GtkAlignment" id="alignment2">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="top_padding">6</property>
                    <property name="left_padding">12</property>
                    <child>
                      <!-- n-columns=1 n-rows=1 -->
                      <object class="GtkGrid" id="grid1">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="row_spacing">6</property>
                        <property name="column_spacing">6</property>
                        <child>
                          <object class="GtkRadioButton" id="custom">
                            <property name="label" translatable="yes" context="textimportoptions|custom">Custom:</property>
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">False</property>
                            <property name="use_underline">True</property>
                            <property name="xalign">0</property>
                            <property name="draw_indicator">True</property>
                            <property name="group">automatic</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkRadioButton" id="automatic">
                            <property name="label" translatable="yes" context="textimportoptions|automatic">Automatic</property>
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">False</property>
                            <property name="use_underline">True</property>
                            <property name="xalign">0</property>
                            <property name="active">True</property>
                            <property name="draw_indicator">True</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">0</property>
                            <property name="width">2</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkComboBoxText" id="lang">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="hexpand">True</property>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">1</property>
                          </packing>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel" id="label2">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textimportoptions|label2">Select the Language to Use for Import</property>
                    <attributes>
                      <attribute name="weight" value="bold"/>
                    </attributes>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkFrame" id="frame3">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="label_xalign">0</property>
                <property name="shadow_type">none</property>
                <child>
                  <object class="GtkAlignment" id="alignment3">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="top_padding">6</property>
                    <property name="left_padding">12</property>
                    <child>
                      <object class="GtkCheckButton" id="convertdata">
                        <property name="label" translatable="yes" context="textimportoptions|convertdata">Detect special numbers (such as dates)</property>
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="receives_default">False</property>
                        <property name="use_underline">True</property>
                        <property name="xalign">0</property>
                        <property name="draw_indicator">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel" id="label3">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textimportoptions|label3">Options</property>
                    <attributes>
                      <attribute name="weight" value="bold"/>
                    </attributes>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">True</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
    </child>
    <action-widgets>
      <action-widget response="-5">ok</action-widget>
      <action-widget response="-6">cancel</action-widget>
      <action-widget response="-11">help</action-widget>
    </action-widgets>
  </object>
</interface>
