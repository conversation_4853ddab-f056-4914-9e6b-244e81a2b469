<?xml version="1.0" encoding="UTF-8"?>
<!--
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
-->
<menu:menupopup xmlns:menu="http://openoffice.org/2001/menu">
  <menu:menuitem menu:id=".uno:Cut"/>
  <menu:menuitem menu:id=".uno:Copy"/>
  <menu:menuitem menu:id=".uno:Paste"/>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:TransformDialog"/>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:AnchorMenu"/>
  <menu:menu menu:id=".uno:WrapMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:WrapOff"/>
      <menu:menuitem menu:id=".uno:WrapOn"/>
      <menu:menuitem menu:id=".uno:WrapIdeal"/>
      <menu:menuitem menu:id=".uno:WrapLeft"/>
      <menu:menuitem menu:id=".uno:WrapRight"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:WrapThrough"/>
      <menu:menuitem menu:id=".uno:WrapThroughTransparencyToggle"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:WrapContour"/>
      <menu:menuitem menu:id=".uno:ContourDialog"/>
      <menu:menuitem menu:id=".uno:WrapAnchorOnly"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:TextWrap"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:ObjectAlign">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:ObjectAlignLeft"/>
      <menu:menuitem menu:id=".uno:AlignCenter"/>
      <menu:menuitem menu:id=".uno:ObjectAlignRight"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:AlignUp"/>
      <menu:menuitem menu:id=".uno:AlignMiddle"/>
      <menu:menuitem menu:id=".uno:AlignDown"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:ArrangeFrameMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:BringToFront"/>
      <menu:menuitem menu:id=".uno:ObjectForwardOne"/>
      <menu:menuitem menu:id=".uno:ObjectBackOne"/>
      <menu:menuitem menu:id=".uno:SendToBack"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:NameGroup"/>
  <menu:menuitem menu:id=".uno:ObjectTitleDescription"/>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:InsertCaptionDialog"/>
</menu:menupopup>
