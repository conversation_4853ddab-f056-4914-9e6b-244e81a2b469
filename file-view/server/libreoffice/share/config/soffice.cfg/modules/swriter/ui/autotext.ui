<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="sw">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkMenu" id="editmenu">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <child>
      <object class="GtkMenuItem" id="new">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|new">_New</property>
        <property name="use_underline">True</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="new-atkobject">
            <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|new">Creates a new AutoText category using the name that you entered in the Name box.</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="newtext">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|newtext">New (text only)</property>
        <property name="use_underline">True</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="newtext-atkobject">
            <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|newtext">Creates a new AutoText entry only from the text in the selection that you made in the current document. Graphics, tables and other objects are not included. You must first enter a name before you see this command.</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="copy">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|copy">_Copy</property>
        <property name="use_underline">True</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="copy-atkobject">
            <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|copy">Copies the selected AutoText to the clipboard.</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="replace">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|replace">Replace</property>
        <property name="use_underline">True</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="replace-atkobject">
            <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|replace">Replaces the contents of the selected AutoText entry with the selection that was made in the current document.</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="replacetext">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|replacetext">Rep_lace (text only)</property>
        <property name="use_underline">True</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="rename">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|rename">Rename...</property>
        <property name="use_underline">True</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="rename-atkobject">
            <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|rename">Changes the name of the selected AutoText category to the name that you enter in the Name box.</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="delete">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|delete">_Delete</property>
        <property name="use_underline">True</property>
      </object>
    </child>
    <child>
      <object class="GtkSeparatorMenuItem" id="menuitem2">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="edit">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|edit">_Edit</property>
        <property name="use_underline">True</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="edit-atkobject">
            <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|edit">Opens the selected AutoText entry for editing in a separate document. Make the changes that you want, choose File - Save AutoText, and then choose File - Close.</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkSeparatorMenuItem" id="menuitem1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="macro">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|macro">_Macro...</property>
        <property name="use_underline">True</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="macro-atkobject">
            <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|macro">Opens the Assign Macro dialog, where you attach a macro to the selected AutoText entry.</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkSeparatorMenuItem" id="menuitem4">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="import">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="autotext|import">_Import...</property>
        <property name="use_underline">True</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="import-atkobject">
            <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|import">Opens a dialog where you can select the MS 97/2000/XP Word document or template, containing the AutoText entries that you want to import.</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkTreeStore" id="liststore1">
    <columns>
      <!-- column-name text -->
      <column type="gchararray"/>
      <!-- column-name id -->
      <column type="gchararray"/>
    </columns>
  </object>
  <object class="GtkDialog" id="AutoTextDialog">
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="title" translatable="yes" context="autotext|AutoTextDialog">AutoText</property>
    <property name="modal">True</property>
    <property name="default_width">0</property>
    <property name="default_height">0</property>
    <property name="type_hint">dialog</property>
    <child internal-child="vbox">
      <object class="GtkBox" id="dialog-vbox1">
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <child internal-child="action_area">
          <object class="GtkButtonBox" id="dialog-action_area1">
            <property name="can_focus">False</property>
            <property name="layout_style">end</property>
            <child>
              <object class="GtkMenuButton" id="autotext">
                <property name="label" translatable="yes" context="autotext|autotext">AutoTe_xt</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_underline">True</property>
                <property name="draw_indicator">True</property>
                <property name="popup">editmenu</property>
                <child>
                  <placeholder/>
                </child>
                <child internal-child="accessible">
                  <object class="AtkObject" id="autotext-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|autotext">Click to display additional AutoText commands, for example, to create a new AutoText entry from a text selection in the current document.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="categories">
                <property name="label" translatable="yes" context="autotext|categories">Cat_egories...</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_underline">True</property>
                <child internal-child="accessible">
                  <object class="AtkObject" id="categories-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|categories">Adds, renames, or deletes AutoText categories.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="path">
                <property name="label" translatable="yes" context="autotext|path">_Path...</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_underline">True</property>
                <child internal-child="accessible">
                  <object class="AtkObject" id="path-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|path">Opens the Edit Paths dialog, where you can select the directory to store AutoText.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">2</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="close">
                <property name="label">gtk-close</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">3</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="ok">
                <property name="label" translatable="yes" context="autotext|insert">_Insert</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="can_default">True</property>
                <property name="has_default">True</property>
                <property name="receives_default">True</property>
                <property name="use_underline">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">4</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="help">
                <property name="label">gtk-help</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">5</property>
                <property name="secondary">True</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="pack_type">end</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <!-- n-columns=1 n-rows=1 -->
          <object class="GtkGrid" id="grid1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="vexpand">True</property>
            <property name="row_spacing">12</property>
            <property name="column_spacing">18</property>
            <child>
              <object class="GtkFrame" id="frame1">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="label_xalign">0</property>
                <property name="shadow_type">none</property>
                <child>
                  <object class="GtkAlignment" id="alignment1">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="top_padding">6</property>
                    <property name="left_padding">12</property>
                    <child>
                      <!-- n-columns=1 n-rows=1 -->
                      <object class="GtkGrid" id="grid4">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="column_spacing">18</property>
                        <child>
                          <object class="GtkCheckButton" id="relfile">
                            <property name="label" translatable="yes" context="autotext|relfile">_File system</property>
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">False</property>
                            <property name="use_underline">True</property>
                            <property name="xalign">0</property>
                            <property name="draw_indicator">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="relfile-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|relfile">Links to AutoText directories on your computer are relative.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkCheckButton" id="relnet">
                            <property name="label" translatable="yes" context="autotext|relnet">Inter_net</property>
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">False</property>
                            <property name="use_underline">True</property>
                            <property name="xalign">0</property>
                            <property name="draw_indicator">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="relnet-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|relnet">Links to files on the Internet are relative.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel" id="label1">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="autotext|label1">Save Links Relative To</property>
                    <attributes>
                      <attribute name="weight" value="bold"/>
                    </attributes>
                  </object>
                </child>
              </object>
              <packing>
                <property name="left_attach">0</property>
                <property name="top_attach">1</property>
                <property name="width">2</property>
              </packing>
            </child>
            <child>
              <object class="GtkAlignment" id="alignment2">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="margin_left">12</property>
                <child>
                  <!-- n-columns=1 n-rows=1 -->
                  <object class="GtkGrid" id="grid2">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="hexpand">True</property>
                    <property name="vexpand">True</property>
                    <property name="row_spacing">6</property>
                    <property name="column_spacing">12</property>
                    <child>
                      <object class="GtkCheckButton" id="inserttip">
                        <property name="label" translatable="yes" context="autotext|inserttip">_Display remainder of name as suggestion while typing</property>
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="receives_default">False</property>
                        <property name="use_underline">True</property>
                        <property name="xalign">0</property>
                        <property name="draw_indicator">True</property>
                        <accessibility>
                          <relation type="label-for" target="category"/>
                        </accessibility>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">0</property>
                      </packing>
                    </child>
                    <child>
                      <!-- n-columns=1 n-rows=1 -->
                      <object class="GtkGrid" id="grid3">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="column_spacing">12</property>
                        <child>
                          <object class="GtkLabel" id="nameft">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="autotext|nameft">Name:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">name</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkLabel" id="shortnameft">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="autotext|shortnameft">Shortcut:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">shortname</property>
                          </object>
                          <packing>
                            <property name="left_attach">2</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkEntry" id="name">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="hexpand">True</property>
                            <property name="max_length">60</property>
                            <property name="activates_default">True</property>
                            <property name="truncate-multiline">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="name-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|name">Displays the name of the selected AutoText category. To change the name of the category, type a new name, and then click Rename. To create a new category, type a name, and then click New.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkEntry" id="shortname">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="halign">end</property>
                            <property name="max_length">30</property>
                            <property name="activates_default">True</property>
                            <property name="width_chars">6</property>
                            <property name="truncate-multiline">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="shortname-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|shortname">Displays the shortcut for the selected AutoText entry. If you are creating a new AutoText entry, type the shortcut that you want to use for the entry.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">3</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">1</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkScrolledWindow">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="hexpand">True</property>
                        <property name="vexpand">True</property>
                        <property name="shadow_type">in</property>
                        <child>
                          <object class="GtkTreeView" id="category">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <property name="model">liststore1</property>
                            <property name="headers_visible">False</property>
                            <property name="headers_clickable">False</property>
                            <property name="search_column">0</property>
                            <child internal-child="selection">
                              <object class="GtkTreeSelection" id="treeview-selection1"/>
                            </child>
                            <child>
                              <object class="GtkTreeViewColumn" id="treeviewcolumn1">
                                <child>
                                  <object class="GtkCellRendererText" id="cellrenderertext1"/>
                                  <attributes>
                                    <attribute name="text">0</attribute>
                                  </attributes>
                                </child>
                              </object>
                            </child>
                            <accessibility>
                              <relation type="labelled-by" target="inserttip"/>
                            </accessibility>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="category-atkobject">
                                <property name="AtkObject::accessible-name" translatable="yes" context="autotext|category-atkobject">Category</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">2</property>
                      </packing>
                    </child>
                  </object>
                </child>
              </object>
              <packing>
                <property name="left_attach">0</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid" id="grid5">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <child>
                  <object class="GtkDrawingArea" id="example">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="events">GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_STRUCTURE_MASK</property>
                    <property name="no_show_all">True</property>
                    <property name="hexpand">True</property>
                    <property name="vexpand">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="example-atkobject">
                        <property name="AtkObject::accessible-name" translatable="yes" context="autotext|example-atkobject">Preview</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="left_attach">1</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
    </child>
    <action-widgets>
      <action-widget response="103">categories</action-widget>
      <action-widget response="104">path</action-widget>
      <action-widget response="-7">close</action-widget>
      <action-widget response="-5">ok</action-widget>
      <action-widget response="-11">help</action-widget>
    </action-widgets>
    <child type="titlebar">
      <placeholder/>
    </child>
    <child internal-child="accessible">
      <object class="AtkObject" id="AutoTextDialog-atkobject">
        <property name="AtkObject::accessible-description" translatable="yes" context="autotext|extended_tip|AutoTextDialog">Creates, edits, or inserts AutoText. You can store formatted text, text with graphics, tables, and fields as AutoText. To quickly insert AutoText, type the shortcut for the AutoText in your document, and then press F3.</property>
      </object>
    </child>
  </object>
</interface>
