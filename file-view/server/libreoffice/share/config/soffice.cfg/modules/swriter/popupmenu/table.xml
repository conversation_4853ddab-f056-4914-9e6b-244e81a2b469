<?xml version="1.0" encoding="UTF-8"?>
<!--
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
-->
<menu:menupopup xmlns:menu="http://openoffice.org/2001/menu">
  <menu:menuitem menu:id=".uno:Cut"/>
  <menu:menuitem menu:id=".uno:Copy"/>
  <menu:menuitem menu:id=".uno:Paste"/>
  <menu:menu menu:id=".uno:PasteSpecialMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:PasteNestedTable"/>
      <menu:menuitem menu:id=".uno:PasteRowsBefore"/>
      <menu:menuitem menu:id=".uno:PasteColumnsBefore"/>
      <menu:menuitem menu:id=".uno:PasteUnformatted"/>
      <menu:menuitem menu:id=".uno:PasteSpecial"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:UpdateCurIndex"/>
  <menu:menuitem menu:id=".uno:EditCurIndex"/>
  <menu:menuitem menu:id=".uno:RemoveTableOf"/>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:UnsetCellsReadOnly"/>
  <menu:menuitem menu:id=".uno:MergeCells"/>
  <menu:menuitem menu:id=".uno:SplitCell"/>
  <menu:menu menu:id=".uno:TableInsertMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:InsertRowsBefore"/>
      <menu:menuitem menu:id=".uno:InsertRowsAfter"/>
      <menu:menuitem menu:id=".uno:InsertRowDialog"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:InsertColumnsBefore"/>
      <menu:menuitem menu:id=".uno:InsertColumnsAfter"/>
      <menu:menuitem menu:id=".uno:InsertColumnDialog"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:TableDeleteMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:DeleteRows"/>
      <menu:menuitem menu:id=".uno:DeleteColumns"/>
      <menu:menuitem menu:id=".uno:DeleteTable"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:TableAutoFitMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:SetRowHeight"/>
      <menu:menuitem menu:id=".uno:SetMinimalRowHeight"/>
      <menu:menuitem menu:id=".uno:SetOptimalRowHeight"/>
      <menu:menuitem menu:id=".uno:DistributeRows"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:SetColumnWidth"/>
      <menu:menuitem menu:id=".uno:SetMinimalColumnWidth"/>
      <menu:menuitem menu:id=".uno:SetOptimalColumnWidth"/>
      <menu:menuitem menu:id=".uno:DistributeColumns"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:StyleMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:HeadingRowsRepeat"/>
      <menu:menuitem menu:id=".uno:RowSplit"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:StyleApply?Style:string=Default Style&amp;FamilyName:string=TableStyles" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:StyleApply?Style:string=Academic&amp;FamilyName:string=TableStyles" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:StyleApply?Style:string=Elegant&amp;FamilyName:string=TableStyles" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:StyleApply?Style:string=Financial&amp;FamilyName:string=TableStyles" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:StyleApply?Style:string=Box List Blue&amp;FamilyName:string=TableStyles" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:StyleApply?Style:string=Box List Green&amp;FamilyName:string=TableStyles" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:StyleApply?Style:string=Box List Red&amp;FamilyName:string=TableStyles" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:StyleApply?Style:string=Box List Yellow&amp;FamilyName:string=TableStyles" menu:style="radio"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:AutoFormat"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:FormatPaintbrush"/>
  <menu:menuitem menu:id=".uno:ResetAttributes"/>
  <menu:menu menu:id=".uno:CharacterMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:FontDialog"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:DefaultCharStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:EmphasisCharStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:StrongEmphasisCharStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:QuoteCharStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:SourceCharStyle" menu:style="radio"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:ParagraphMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:ParagraphDialog"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:TextBodyParaStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:Heading1ParaStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:Heading2ParaStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:Heading3ParaStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:QuotationsParStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:PreformattedParaStyle" menu:style="radio"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:EditStyle"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:NumberingMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:OutlineBullet"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:BulletListStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:NumberListStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:AlphaListStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:AlphaLowListStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:RomanListStyle" menu:style="radio"/>
      <menu:menuitem menu:id=".uno:RomanLowListStyle" menu:style="radio"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:IncrementLevel"/>
      <menu:menuitem menu:id=".uno:DecrementLevel"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:NumberingStart"/>
      <menu:menuitem menu:id=".uno:ContinueNumbering"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:RemoveBullets"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:InsertAnnotation"/>
  <menu:menuitem menu:id=".uno:FieldDialog"/>
  <menu:menuitem menu:id=".uno:AcceptTrackedChange"/>
  <menu:menuitem menu:id=".uno:RejectTrackedChange"/>
  <menu:menuitem menu:id=".uno:NextTrackedChange"/>
  <menu:menuitem menu:id=".uno:PreviousTrackedChange"/>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:OpenHyperlinkOnCursor"/>
  <menu:menuitem menu:id=".uno:EditHyperlink"/>
  <menu:menuitem menu:id=".uno:CopyHyperlinkLocation"/>
  <menu:menuitem menu:id=".uno:RemoveHyperlink"/>
  <menu:menuitem menu:id=".uno:OpenSmartTagMenuOnCursor"/>
  <menu:menuitem menu:id=".uno:ThesaurusFromContext"/>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:InsertCaptionDialog"/>
  <menu:menuitem menu:id=".uno:TableDialog"/>
</menu:menupopup>
