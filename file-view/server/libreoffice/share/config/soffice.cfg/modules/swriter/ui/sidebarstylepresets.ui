<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.22.1 -->
<interface domain="sw">
  <requires lib="gtk+" version="3.20"/>
  <!-- n-columns=1 n-rows=1 -->
  <object class="GtkGrid" id="StylePresetsPanel">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="row_homogeneous">True</property>
    <property name="column_homogeneous">True</property>
    <child>
      <object class="GtkAlignment" id="alignment1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <child>
          <!-- n-columns=1 n-rows=1 -->
          <object class="GtkGrid" id="grid1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="row_spacing">6</property>
            <property name="column_spacing">6</property>
            <child>
              <object class="GtkScrolledWindow" id="valuesetwin">
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <property name="hscrollbar_policy">never</property>
                <property name="vscrollbar_policy">never</property>
                <property name="shadow_type">in</property>
                <child>
                  <object class="GtkViewport">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <child>
                      <object class="GtkDrawingArea" id="valueset">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="events">GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_KEY_PRESS_MASK | GDK_KEY_RELEASE_MASK | GDK_STRUCTURE_MASK</property>
                        <property name="hexpand">True</property>
                        <property name="vexpand">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
              <packing>
                <property name="left_attach">0</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
          </object>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">0</property>
      </packing>
    </child>
  </object>
</interface>
