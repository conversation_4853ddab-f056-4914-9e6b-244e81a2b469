<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="sd">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkFrame" id="SdViewPage">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="hexpand">True</property>
    <property name="border_width">6</property>
    <property name="label_xalign">0</property>
    <property name="shadow_type">none</property>
    <child>
      <object class="GtkAlignment" id="alignment1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="top_padding">6</property>
        <property name="left_padding">12</property>
        <child>
          <object class="GtkBox" id="box1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="orientation">vertical</property>
            <property name="spacing">6</property>
            <child>
              <object class="GtkCheckButton" id="ruler">
                <property name="label" translatable="yes" context="sdviewpage|ruler">_Rulers visible</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">False</property>
                <property name="use_underline">True</property>
                <property name="xalign">0</property>
                <property name="draw_indicator">True</property>
                <child internal-child="accessible">
                  <object class="AtkObject" id="ruler-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|ruler">Specifies whether to display the rulers at the top and to the left of the work area.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkCheckButton" id="dragstripes">
                <property name="label" translatable="yes" context="sdviewpage|dragstripes">_Helplines while moving</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">False</property>
                <property name="use_underline">True</property>
                <property name="xalign">0</property>
                <property name="draw_indicator">True</property>
                <child internal-child="accessible">
                  <object class="AtkObject" id="dragstripes-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|dragstripes">Specifies whether to display guides when moving an object.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
            <child>
              <object class="GtkCheckButton" id="handlesbezier">
                <property name="label" translatable="yes" context="sdviewpage|handlesbezier">_All control points in Bézier editor</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">False</property>
                <property name="use_underline">True</property>
                <property name="xalign">0</property>
                <property name="draw_indicator">True</property>
                <child internal-child="accessible">
                  <object class="AtkObject" id="handlesbezier-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|handlesbezier">Displays the control points of all Bézier points if you have previously selected a Bézier curve. If the All control points in Bézier editor option is not marked, only the control points of the selected Bézier points will be visible.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">2</property>
              </packing>
            </child>
            <child>
              <object class="GtkCheckButton" id="moveoutline">
                <property name="label" translatable="yes" context="sdviewpage|moveoutline">_Contour of each individual object</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">False</property>
                <property name="use_underline">True</property>
                <property name="xalign">0</property>
                <property name="draw_indicator">True</property>
                <child internal-child="accessible">
                  <object class="AtkObject" id="moveoutline-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|moveoutline">%PRODUCTNAME displays the contour line of each individual object when moving this object.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">3</property>
              </packing>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child type="label">
      <object class="GtkLabel" id="label1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="sdviewpage|label1">Display</property>
        <attributes>
          <attribute name="weight" value="bold"/>
        </attributes>
      </object>
    </child>
    <child internal-child="accessible">
      <object class="AtkObject" id="SdViewPage-atkobject">
        <property name="AtkObject::accessible-description" translatable="yes" context="extended_tip|SdViewPage">Specifies the available display modes.</property>
      </object>
    </child>
  </object>
</interface>
