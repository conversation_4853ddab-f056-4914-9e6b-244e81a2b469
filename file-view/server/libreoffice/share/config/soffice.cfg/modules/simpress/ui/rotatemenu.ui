<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.20.0 -->
<interface domain="sd">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkMenu" id="menu">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <child>
      <object class="GtkMenuItem" id="90">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="rotatemenu|90">Quarter Spin</property>
        <property name="use_underline">True</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="180">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="rotatemenu|180">Half Spin</property>
        <property name="use_underline">True</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="360">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="rotatemenu|360">Full Spin</property>
        <property name="use_underline">True</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="720">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="rotatemenu|720">Two Spins</property>
        <property name="use_underline">True</property>
      </object>
    </child>
    <child>
      <object class="GtkSeparatorMenuItem" id="menuitem1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="clockwise">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="rotatemenu|clockwise">Clockwise</property>
        <property name="use_underline">True</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuItem" id="counterclock">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="rotatemenu|counterclock">Counter-clockwise</property>
        <property name="use_underline">True</property>
      </object>
    </child>
  </object>
</interface>
