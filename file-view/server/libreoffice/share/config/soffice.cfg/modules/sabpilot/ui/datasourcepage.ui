<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="pcr">
  <requires lib="gtk+" version="3.20"/>
  <!-- n-columns=1 n-rows=1 -->
  <object class="GtkGrid" id="DataSourcePage">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="row_spacing">12</property>
    <child>
      <object class="GtkLabel" id="label2">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label" translatable="yes" context="datasourcepage|label2">That was all the information necessary to integrate your address data into %PRODUCTNAME.

Now, just enter the name under which you want to register the data source in %PRODUCTNAME.</property>
        <property name="wrap">True</property>
        <property name="width_chars">70</property>
        <property name="max_width_chars">70</property>
        <property name="xalign">0</property>
        <property name="yalign">0</property>
        <child internal-child="accessible">
          <object class="AtkObject" id="label2-atkobject">
            <property name="AtkObject::accessible-role" translatable="no">static</property>
          </object>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">0</property>
      </packing>
    </child>
    <child>
      <!-- n-columns=1 n-rows=1 -->
      <object class="GtkGrid" id="grid3">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="row_spacing">6</property>
        <child>
          <object class="GtkCheckButton" id="embed">
            <property name="label" translatable="yes" context="datasourcepage|embed">Embed this address book definition into the current document.</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">False</property>
            <property name="use_underline">True</property>
            <property name="xalign">0</property>
            <property name="draw_indicator">True</property>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
        <child>
          <!-- n-columns=1 n-rows=1 -->
          <object class="GtkGrid" id="grid4">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="column_spacing">12</property>
            <child>
              <object class="GtkLabel" id="locationft">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="label" translatable="yes" context="datasourcepage|locationft">Location</property>
                <property name="use_underline">True</property>
                <property name="mnemonic_widget">location</property>
                <property name="xalign">0</property>
              </object>
              <packing>
                <property name="left_attach">0</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="browse">
                <property name="label" translatable="yes" context="datasourcepage|browse">Browse...</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_underline">True</property>
                <child internal-child="accessible">
                  <object class="AtkObject" id="browse-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="datasourcepage|extended_tip|browse">Specifies the location using a file dialog.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="left_attach">2</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkComboBoxText" id="location">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="has_entry">True</property>
                <child internal-child="entry">
                  <object class="GtkEntry" id="comboboxtext-entry">
                    <property name="can_focus">True</property>
                    <property name="truncate-multiline">True</property>
                    <property name="activates_default">True</property>
                  </object>
                </child>
                <child internal-child="accessible">
                  <object class="AtkObject" id="location-atkobject">
                    <property name="AtkObject::accessible-description" translatable="yes" context="datasourcepage|extended_tip|location">Specifies the location of the database file.</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="left_attach">1</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">1</property>
          </packing>
        </child>
        <child>
          <object class="GtkCheckButton" id="available">
            <property name="label" translatable="yes" context="datasourcepage|available">Make this address book available to all modules in %PRODUCTNAME.</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">False</property>
            <property name="use_underline">True</property>
            <property name="xalign">0</property>
            <property name="draw_indicator">True</property>
            <child internal-child="accessible">
              <object class="AtkObject" id="available-atkobject">
                <property name="AtkObject::accessible-description" translatable="yes" context="datasourcepage|extended_tip|available">Registers the newly created database file in %PRODUCTNAME. The database will then be listed in the Data sources pane (Ctrl+Shift+F4). If this check box is cleared, the database will be available only by opening the database file.</property>
              </object>
            </child>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">2</property>
          </packing>
        </child>
        <child>
          <object class="GtkAlignment" id="alignment1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="left_padding">12</property>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid" id="grid5">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="row_spacing">6</property>
                <child>
                  <!-- n-columns=1 n-rows=1 -->
                  <object class="GtkGrid" id="grid6">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="hexpand">True</property>
                    <property name="column_spacing">12</property>
                    <child>
                      <object class="GtkLabel" id="nameft">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="label" translatable="yes" context="datasourcepage|nameft">Address book name</property>
                        <property name="use_underline">True</property>
                        <property name="mnemonic_widget">name</property>
                        <property name="xalign">0</property>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkEntry" id="name">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="hexpand">True</property>
                        <property name="activates_default">True</property>
                        <property name="truncate-multiline">True</property>
                        <child internal-child="accessible">
                          <object class="AtkObject" id="name-atkobject">
                            <property name="AtkObject::accessible-description" translatable="yes" context="datasourcepage|extended_tip|name">Specifies the data source name.</property>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">0</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="warning">
                    <property name="can_focus">False</property>
                    <property name="no_show_all">True</property>
                    <property name="label" translatable="yes" context="datasourcepage|warning">Another data source already has this name. As data sources have to have globally unique names, you need to choose another one.</property>
                    <property name="wrap">True</property>
                    <property name="width_chars">70</property>
                    <property name="max_width_chars">70</property>
                    <property name="xalign">0</property>
                    <property name="yalign">0</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="warning-atkobject">
                        <property name="AtkObject::accessible-role" translatable="no">static</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">3</property>
          </packing>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">1</property>
      </packing>
    </child>
    <child internal-child="accessible">
      <object class="AtkObject" id="DataSourcePage-atkobject">
        <property name="AtkObject::accessible-description" translatable="yes" context="datasourcepage|extended_tip|DataSourcePage">Specifies a location for the address book file and a name under which the data source will be listed in the data source explorer.</property>
      </object>
    </child>
  </object>
</interface>
