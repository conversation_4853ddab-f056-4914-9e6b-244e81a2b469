<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="svt">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkPopover" id="date_popup_window">
    <property name="can_focus">False</property>
    <property name="no_show_all">True</property>
    <property name="border_width">4</property>
    <child>
      <object class="GtkBox" id="box1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">6</property>
        <child>
          <object class="GtkCalendar" id="date">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="year">2019</property>
            <property name="month">1</property>
            <property name="day">14</property>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkBox" id="extras">
            <property name="can_focus">False</property>
            <property name="no_show_all">True</property>
            <property name="orientation">vertical</property>
            <property name="spacing">6</property>
            <child>
              <object class="GtkSeparator">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkButtonBox" id="buttonbox">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="spacing">6</property>
                <property name="layout_style">spread</property>
                <child>
                  <object class="GtkButton" id="today">
                    <property name="label" context="calendar|STR_SVT_CALENDAR_TODAY">Today</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="can_default">True</property>
                    <property name="receives_default">True</property>
                    <property name="use_underline">True</property>
                  </object>
                  <packing>
                    <property name="expand">True</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="none">
                    <property name="label" context="calendar|STR_SVT_CALENDAR_NONE">None</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">True</property>
                  </object>
                  <packing>
                    <property name="expand">True</property>
                    <property name="fill">True</property>
                    <property name="position">1</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>
