<?xml version="1.0" encoding="UTF-8"?>
<!--
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
 * This file incorporates work covered by the following license notice:
 *
 *   Licensed to the Apache Software Foundation (ASF) under one or more
 *   contributor license agreements. See the NOTICE file distributed
 *   with this work for additional information regarding copyright
 *   ownership. The ASF licenses this file to you under the Apache
 *   License, Version 2.0 (the "License"); you may not use this file
 *   except in compliance with the License. You may obtain a copy of
 *   the License at http://www.apache.org/licenses/LICENSE-2.0 .
-->

<!ENTITY % boolean		"(true|false)">
<!ENTITY % numeric		"CDATA">
<!ENTITY % alignment	"(left|center|right)">
<!ENTITY % style		"(in|out|flat)">

<!ELEMENT statusbar:statusbar (statusbar:statusbaritem*)>
<!ATTLIST statusbar:statusbar
	xmlns:statusbar CDATA #FIXED "http://openoffice.org/2001/statusbar"
	xmlns:xlink CDATA #FIXED "http://www.w3.org/1999/xlink"
>
<!ELEMENT statusbar:statusbaritem EMPTY>
<!ATTLIST statusbar:statusbaritem
	xlink:href CDATA #REQUIRED
	statusbar:align %alignment; "center"
	statusbar:style %style; "in"
	statusbar:autosize %boolean; "false"
	statusbar:mandatory %boolean; "true"
	statusbar:ownerdraw %boolean; "false"
	statusbar:width %numeric; "0"
	statusbar:offset %numeric; "5"
>
