#BaseNLPSolver
NLPSolverCommon.Properties.AssumeNonNegative=<PERSON><PERSON><PERSON> variáveis não negativas

#BaseEvolutionarySolver
NLPSolverCommon.Properties.SwarmSize=Tamanho da colônia
NLPSolverCommon.Properties.LibrarySize=Tamanho da biblioteca
NLPSolverCommon.Properties.LearningCycles=Ciclos de aprendizagem
NLPSolverCommon.Properties.GuessVariableRange=Estimativa dos limites das variáveis
NLPSolverCommon.Properties.VariableRangeThreshold=Piso dos limites das variáveis (ao estimar)
NLPSolverCommon.Properties.UseACRComparator=Utilizar comparador ACR (no lugar de BCH)
NLPSolverCommon.Properties.UseRandomStartingPoint=Utilizar ponto inicial aleatório
NLPSolverCommon.Properties.StagnationLimit=Limite de estagnação
NLPSolverCommon.Properties.Tolerance=Tolerância de estagnação
NLPSolverCommon.Properties.EnhancedSolverStatus=Mostrar detalhes do solver

#DEPS
NLPSolverCommon.Properties.AgentSwitchRate=Taxa de alternância do agente (probabilidade DE)
NLPSolverCommon.Properties.DEFactorMin=DE: Fator de escala mínimo  (0-1,2)
NLPSolverCommon.Properties.DEFactorMax=DE: Fator de escala máximo (0-1,2)
NLPSolverCommon.Properties.DECR=DE: Probabilidade de cruzamento (0 - 1)
NLPSolverCommon.Properties.PSC1=PS: Constante cognitiva
NLPSolverCommon.Properties.PSC2=PS: Constante social
NLPSolverCommon.Properties.PSWeight=PS: Coeficiente de constrição
NLPSolverCommon.Properties.PSCL=PS: Probabilidade de mutação (0 - 0,005)
