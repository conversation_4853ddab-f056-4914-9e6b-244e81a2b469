#BaseNLPSolver
NLPSolverCommon.Properties.AssumeNonNegative=नॉन-नेगेटिव्ह वेरियेबल्स् गृहीत धरा

#BaseEvolutionarySolver
NLPSolverCommon.Properties.SwarmSize=स्वॅर्मचे आकार
NLPSolverCommon.Properties.LibrarySize=लाइब्ररिचा आकार
NLPSolverCommon.Properties.LearningCycles=लर्णिंग साइकल
NLPSolverCommon.Properties.GuessVariableRange=वेरियेबल बाउंडस् ओळखणे
NLPSolverCommon.Properties.VariableRangeThreshold=वेरियेबल बाउंडस् थ्रेशहोल्ड (अंदाज घेतेवेळी)
NLPSolverCommon.Properties.UseACRComparator=ACR कॅम्पॅरटरचा वापर करा (BCH ऐवजी)
NLPSolverCommon.Properties.UseRandomStartingPoint=विनाक्रम प्रारंभ पाइंटचा वापर करा
NLPSolverCommon.Properties.StagnationLimit=स्टॅगनेशन मर्यादा
NLPSolverCommon.Properties.Tolerance=स्टॅगनेशन टॉलरेंस
NLPSolverCommon.Properties.EnhancedSolverStatus=सुधारीत सॉल्वहर स्थिती

#DEPS
NLPSolverCommon.Properties.AgentSwitchRate=एजंट स्विच् रेट (DE प्रोबॅबिलिटि)
NLPSolverCommon.Properties.DEFactorMin=DE: Min Scaling Factor (0-1.2)
NLPSolverCommon.Properties.DEFactorMax=DE: Max Scaling Factor (0-1.2)
NLPSolverCommon.Properties.DECR=DE: क्रॉसओव्हर प्रोबॅबिलिटि (0-1)
NLPSolverCommon.Properties.PSC1=PS: कॉगनिटिव्ह काँस्टंट
NLPSolverCommon.Properties.PSC2=PS: सोशअल काँस्टंट
NLPSolverCommon.Properties.PSWeight=PS: काँस्ट्रिक्शन कोइफिशिअंट
NLPSolverCommon.Properties.PSCL=PS: म्युटेशन प्रोबॅबिलिटि (0-0.005)
