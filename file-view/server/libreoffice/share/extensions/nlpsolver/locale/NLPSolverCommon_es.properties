#BaseNLPSolver
NLPSolverCommon.Properties.AssumeNonNegative=Asumir variables no negativas

#BaseEvolutionarySolver
NLPSolverCommon.Properties.SwarmSize=Tamaño del cúmulo
NLPSolverCommon.Properties.LibrarySize=Tamaño de la biblioteca
NLPSolverCommon.Properties.LearningCycles=Ciclos de aprendizaje
NLPSolverCommon.Properties.GuessVariableRange=Estimar los límites de las variables
NLPSolverCommon.Properties.VariableRangeThreshold=Umbral de los límites de las variables (al estimar)
NLPSolverCommon.Properties.UseACRComparator=Utilizar el comparador ACR (en lugar del BCH)
NLPSolverCommon.Properties.UseRandomStartingPoint=Utilizar un punto de inicio aleatorio
NLPSolverCommon.Properties.StagnationLimit=Límite de estancamiento
NLPSolverCommon.Properties.Tolerance=Tolerancia de estancamiento
NLPSolverCommon.Properties.EnhancedSolverStatus=Mostrar estado detallado de Solver

#DEPS
NLPSolverCommon.Properties.AgentSwitchRate=Tasa de cambio del agente (probabilidad DE)
NLPSolverCommon.Properties.DEFactorMin=DE: Factor de escala mínimo (0-1,2)
NLPSolverCommon.Properties.DEFactorMax=DE: Factor de escala máximo (0-1,2)
NLPSolverCommon.Properties.DECR=DE: Probabilidad de cruce (0-1)
NLPSolverCommon.Properties.PSC1=PS: Constante cognitiva
NLPSolverCommon.Properties.PSC2=PS: Constante social
NLPSolverCommon.Properties.PSWeight=PS: Coeficiente de constricción
NLPSolverCommon.Properties.PSCL=PS: Probabilidad de mutación (0-0.005)
