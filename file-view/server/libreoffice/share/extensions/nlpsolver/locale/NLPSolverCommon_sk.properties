#BaseNLPSolver
NLPSolverCommon.Properties.AssumeNonNegative=Predpokladať nezáporné premenné

#BaseEvolutionarySolver
NLPSolverCommon.Properties.SwarmSize=Veľkosť populácie
NLPSolverCommon.Properties.LibrarySize=Veľkosť knižnice
NLPSolverCommon.Properties.LearningCycles=Cykly učenia
NLPSolverCommon.Properties.GuessVariableRange=Odhad hraničných hodnôt premenných
NLPSolverCommon.Properties.VariableRangeThreshold=Najnižšia navrhovaná hodnota premennej
NLPSolverCommon.Properties.UseACRComparator=Použiť ACR porovnávanie (namiesto BCH)
NLPSolverCommon.Properties.UseRandomStartingPoint=Použiť náhodne vybraný štartovací bod
NLPSolverCommon.Properties.StagnationLimit=Hranica stagnácie
NLPSolverCommon.Properties.Tolerance=Tolerancia stagnácie
NLPSolverCommon.Properties.EnhancedSolverStatus=Ukázať rozšírený status riešiča

#DEPS
NLPSolverCommon.Properties.AgentSwitchRate=Rýchlosť prepínania agenta (pravdepodobnosť DE)
NLPSolverCommon.Properties.DEFactorMin=DE: Minimálna mierka (0-1,2)
NLPSolverCommon.Properties.DEFactorMax=DE: Maximálna mierka (0-1,2)
NLPSolverCommon.Properties.DECR=DE: pravdepodobnosť kríženia (0-1)
NLPSolverCommon.Properties.PSC1=PS: kognitívna konštanta
NLPSolverCommon.Properties.PSC2=PS: sociálna konštanta
NLPSolverCommon.Properties.PSWeight=PS: koeficient zmršťovania
NLPSolverCommon.Properties.PSCL=PS: pravdepodobnosť mutácie (0-0,005)
