#BaseNLPSolver
NLPSolverCommon.Properties.AssumeNonNegative=افترض أن المتغيرات غير سالبة

#BaseEvolutionarySolver
NLPSolverCommon.Properties.SwarmSize=حجم السرب
NLPSolverCommon.Properties.LibrarySize=حجم المكتبة
NLPSolverCommon.Properties.LearningCycles=دورة التعلم
NLPSolverCommon.Properties.GuessVariableRange=تخمين حدود المتغير
NLPSolverCommon.Properties.VariableRangeThreshold=عتبة حدود المتغير (عند التخمين)
NLPSolverCommon.Properties.UseACRComparator=استخدم مقارن ACR (بدلًا من BCH)
NLPSolverCommon.Properties.UseRandomStartingPoint=استخدام نقطة بداية عشوائيًا
NLPSolverCommon.Properties.StagnationLimit=حدّ الركود
NLPSolverCommon.Properties.Tolerance=تحمّل الركود
NLPSolverCommon.Properties.EnhancedSolverStatus=إظهار حالة الحلّال المحسّنة

#DEPS
NLPSolverCommon.Properties.AgentSwitchRate=معدّل تحويل العامل (احتمالية DE)
NLPSolverCommon.Properties.DEFactorMin=DE: Min Scaling Factor (0-1.2)
NLPSolverCommon.Properties.DEFactorMax=DE: Max Scaling Factor (0-1.2)
NLPSolverCommon.Properties.DECR=DE: احتمال العبور (0-1)
NLPSolverCommon.Properties.PSC1=PS: ثابت الإدراك
NLPSolverCommon.Properties.PSC2=PS: ثابت اجتماعي
NLPSolverCommon.Properties.PSWeight=PS: معامل الانقباض
NLPSolverCommon.Properties.PSCL=PS: احتمال الطفرة (0-0.005)
