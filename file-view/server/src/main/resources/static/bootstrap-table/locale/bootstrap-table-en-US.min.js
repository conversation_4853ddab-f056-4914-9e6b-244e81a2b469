/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.16.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&t.hasOwnProperty("default")?t.default:t;var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t,n){return t(n={exports:{}},n.exports),n.exports}var e=function(t){return t&&t.Math==Math&&t},o=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof n&&n)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),c={}.propertyIsEnumerable,f=Object.getOwnPropertyDescriptor,a={f:f&&!c.call({1:2},1)?function(t){var n=f(this,t);return!!n&&n.enumerable}:c},l=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},s={}.toString,p=function(t){return s.call(t).slice(8,-1)},g="".split,d=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?g.call(t,""):Object(t)}:Object,y=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},h=function(t){return d(y(t))},m=function(t){return"object"==typeof t?null!==t:"function"==typeof t},v=function(t,n){if(!m(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!m(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!m(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!m(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},b={}.hasOwnProperty,w=function(t,n){return b.call(t,n)},S=o.document,O=m(S)&&m(S.createElement),T=!u&&!i((function(){return 7!=Object.defineProperty((t="div",O?S.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),j=Object.getOwnPropertyDescriptor,P={f:u?j:function(t,n){if(t=h(t),n=v(n,!0),T)try{return j(t,n)}catch(t){}if(w(t,n))return l(!a.f.call(t,n),t[n])}},x=function(t){if(!m(t))throw TypeError(String(t)+" is not an object");return t},A=Object.defineProperty,E={f:u?A:function(t,n,r){if(x(t),n=v(n,!0),x(r),T)try{return A(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},M=u?function(t,n,r){return E.f(t,n,l(1,r))}:function(t,n,r){return t[n]=r,t},C=function(t,n){try{M(o,t,n)}catch(r){o[t]=n}return n},R=o["__core-js_shared__"]||C("__core-js_shared__",{}),_=Function.toString;"function"!=typeof R.inspectSource&&(R.inspectSource=function(t){return _.call(t)});var N,F,L,k,I=R.inspectSource,D=o.WeakMap,q="function"==typeof D&&/native code/.test(I(D)),z=r((function(t){(t.exports=function(t,n){return R[t]||(R[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),G=0,H=Math.random(),U=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++G+H).toString(36)},B=z("keys"),W={},J=o.WeakMap;if(q){var K=new J,Q=K.get,V=K.has,Y=K.set;N=function(t,n){return Y.call(K,t,n),n},F=function(t){return Q.call(K,t)||{}},L=function(t){return V.call(K,t)}}else{var X=B[k="state"]||(B[k]=U(k));W[X]=!0,N=function(t,n){return M(t,X,n),n},F=function(t){return w(t,X)?t[X]:{}},L=function(t){return w(t,X)}}var Z,$,tt={set:N,get:F,has:L,enforce:function(t){return L(t)?F(t):N(t,{})},getterFor:function(t){return function(n){var r;if(!m(n)||(r=F(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},nt=r((function(t){var n=tt.get,r=tt.enforce,e=String(String).split("String");(t.exports=function(t,n,i,u){var c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,a=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof n||w(i,"name")||M(i,"name",n),r(i).source=e.join("string"==typeof n?n:"")),t!==o?(c?!a&&t[n]&&(f=!0):delete t[n],f?t[n]=i:M(t,n,i)):f?t[n]=i:C(n,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||I(this)}))})),rt=o,et=function(t){return"function"==typeof t?t:void 0},ot=function(t,n){return arguments.length<2?et(rt[t])||et(o[t]):rt[t]&&rt[t][n]||o[t]&&o[t][n]},it=Math.ceil,ut=Math.floor,ct=function(t){return isNaN(t=+t)?0:(t>0?ut:it)(t)},ft=Math.min,at=function(t){return t>0?ft(ct(t),9007199254740991):0},lt=Math.max,st=Math.min,pt=function(t){return function(n,r,e){var o,i=h(n),u=at(i.length),c=function(t,n){var r=ct(t);return r<0?lt(r+n,0):st(r,n)}(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},gt={includes:pt(!0),indexOf:pt(!1)}.indexOf,dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),yt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=h(t),o=0,i=[];for(r in e)!w(W,r)&&w(e,r)&&i.push(r);for(;n.length>o;)w(e,r=n[o++])&&(~gt(i,r)||i.push(r));return i}(t,dt)}},ht={f:Object.getOwnPropertySymbols},mt=ot("Reflect","ownKeys")||function(t){var n=yt.f(x(t)),r=ht.f;return r?n.concat(r(t)):n},vt=function(t,n){for(var r=mt(n),e=E.f,o=P.f,i=0;i<r.length;i++){var u=r[i];w(t,u)||e(t,u,o(n,u))}},bt=/#|\.prototype\./,wt=function(t,n){var r=Ot[St(t)];return r==jt||r!=Tt&&("function"==typeof n?i(n):!!n)},St=wt.normalize=function(t){return String(t).replace(bt,".").toLowerCase()},Ot=wt.data={},Tt=wt.NATIVE="N",jt=wt.POLYFILL="P",Pt=wt,xt=P.f,At=Array.isArray||function(t){return"Array"==p(t)},Et=function(t){return Object(y(t))},Mt=function(t,n,r){var e=v(n);e in t?E.f(t,e,l(0,r)):t[e]=r},Ct=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),Rt=Ct&&!Symbol.sham&&"symbol"==typeof Symbol(),_t=z("wks"),Nt=o.Symbol,Ft=Rt?Nt:U,Lt=function(t){return w(_t,t)||(Ct&&w(Nt,t)?_t[t]=Nt[t]:_t[t]=Ft("Symbol."+t)),_t[t]},kt=Lt("species"),It=function(t,n){var r;return At(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!At(r.prototype)?m(r)&&null===(r=r[kt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Dt=ot("navigator","userAgent")||"",qt=o.process,zt=qt&&qt.versions,Gt=zt&&zt.v8;Gt?$=(Z=Gt.split("."))[0]+Z[1]:Dt&&(!(Z=Dt.match(/Edge\/(\d+)/))||Z[1]>=74)&&(Z=Dt.match(/Chrome\/(\d+)/))&&($=Z[1]);var Ht,Ut=$&&+$,Bt=Lt("species"),Wt=Lt("isConcatSpreadable"),Jt=Ut>=51||!i((function(){var t=[];return t[Wt]=!1,t.concat()[0]!==t})),Kt=(Ht="concat",Ut>=51||!i((function(){var t=[];return(t.constructor={})[Bt]=function(){return{foo:1}},1!==t[Ht](Boolean).foo}))),Qt=function(t){if(!m(t))return!1;var n=t[Wt];return void 0!==n?!!n:At(t)};!function(t,n){var r,e,i,u,c,f=t.target,a=t.global,l=t.stat;if(r=a?o:l?o[f]||C(f,{}):(o[f]||{}).prototype)for(e in n){if(u=n[e],i=t.noTargetGet?(c=xt(r,e))&&c.value:r[e],!Pt(a?e:f+(l?".":"#")+e,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;vt(u,i)}(t.sham||i&&i.sham)&&M(u,"sham",!0),nt(r,e,u,t)}}({target:"Array",proto:!0,forced:!Jt||!Kt},{concat:function(t){var n,r,e,o,i,u=Et(this),c=It(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(i=-1===n?u:arguments[n],Qt(i)){if(f+(o=at(i.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(r=0;r<o;r++,f++)r in i&&Mt(c,f,i[r])}else{if(f>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Mt(c,f++,i)}return c.length=f,c}}),t.fn.bootstrapTable.locales["en-US"]={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Showing ".concat(t," to ").concat(n," of ").concat(r," rows (filtered from ").concat(e," total rows)"):"Showing ".concat(t," to ").concat(n," of ").concat(r," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["en-US"])}));
