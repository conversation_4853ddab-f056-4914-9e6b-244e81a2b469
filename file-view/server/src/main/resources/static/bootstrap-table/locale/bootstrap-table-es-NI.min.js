/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.16.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&t.hasOwnProperty("default")?t.default:t;var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t,n){return t(n={exports:{}},n.exports),n.exports}var e=function(t){return t&&t.Math==Math&&t},o=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof n&&n)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),a={}.propertyIsEnumerable,c=Object.getOwnPropertyDescriptor,f={f:c&&!a.call({1:2},1)?function(t){var n=c(this,t);return!!n&&n.enumerable}:a},l=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},s={}.toString,p=function(t){return s.call(t).slice(8,-1)},g="".split,d=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?g.call(t,""):Object(t)}:Object,y=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},m=function(t){return d(y(t))},h=function(t){return"object"==typeof t?null!==t:"function"==typeof t},v=function(t,n){if(!h(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!h(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!h(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!h(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},b={}.hasOwnProperty,w=function(t,n){return b.call(t,n)},S=o.document,O=h(S)&&h(S.createElement),T=!u&&!i((function(){return 7!=Object.defineProperty((t="div",O?S.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),j=Object.getOwnPropertyDescriptor,P={f:u?j:function(t,n){if(t=m(t),n=v(n,!0),T)try{return j(t,n)}catch(t){}if(w(t,n))return l(!f.f.call(t,n),t[n])}},x=function(t){if(!h(t))throw TypeError(String(t)+" is not an object");return t},A=Object.defineProperty,E={f:u?A:function(t,n,r){if(x(t),n=v(n,!0),x(r),T)try{return A(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},M=u?function(t,n,r){return E.f(t,n,l(1,r))}:function(t,n,r){return t[n]=r,t},C=function(t,n){try{M(o,t,n)}catch(r){o[t]=n}return n},R=o["__core-js_shared__"]||C("__core-js_shared__",{}),_=Function.toString;"function"!=typeof R.inspectSource&&(R.inspectSource=function(t){return _.call(t)});var N,I,F,L,k=R.inspectSource,q=o.WeakMap,D="function"==typeof q&&/native code/.test(k(q)),z=r((function(t){(t.exports=function(t,n){return R[t]||(R[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),B=0,G=Math.random(),H=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++B+G).toString(36)},W=z("keys"),J={},K=o.WeakMap;if(D){var Q=new K,U=Q.get,V=Q.has,Y=Q.set;N=function(t,n){return Y.call(Q,t,n),n},I=function(t){return U.call(Q,t)||{}},F=function(t){return V.call(Q,t)}}else{var X=W[L="state"]||(W[L]=H(L));J[X]=!0,N=function(t,n){return M(t,X,n),n},I=function(t){return w(t,X)?t[X]:{}},F=function(t){return w(t,X)}}var Z,$,tt={set:N,get:I,has:F,enforce:function(t){return F(t)?I(t):N(t,{})},getterFor:function(t){return function(n){var r;if(!h(n)||(r=I(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},nt=r((function(t){var n=tt.get,r=tt.enforce,e=String(String).split("String");(t.exports=function(t,n,i,u){var a=!!u&&!!u.unsafe,c=!!u&&!!u.enumerable,f=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof n||w(i,"name")||M(i,"name",n),r(i).source=e.join("string"==typeof n?n:"")),t!==o?(a?!f&&t[n]&&(c=!0):delete t[n],c?t[n]=i:M(t,n,i)):c?t[n]=i:C(n,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||k(this)}))})),rt=o,et=function(t){return"function"==typeof t?t:void 0},ot=function(t,n){return arguments.length<2?et(rt[t])||et(o[t]):rt[t]&&rt[t][n]||o[t]&&o[t][n]},it=Math.ceil,ut=Math.floor,at=function(t){return isNaN(t=+t)?0:(t>0?ut:it)(t)},ct=Math.min,ft=function(t){return t>0?ct(at(t),9007199254740991):0},lt=Math.max,st=Math.min,pt=function(t){return function(n,r,e){var o,i=m(n),u=ft(i.length),a=function(t,n){var r=at(t);return r<0?lt(r+n,0):st(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},gt={includes:pt(!0),indexOf:pt(!1)}.indexOf,dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),yt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=m(t),o=0,i=[];for(r in e)!w(J,r)&&w(e,r)&&i.push(r);for(;n.length>o;)w(e,r=n[o++])&&(~gt(i,r)||i.push(r));return i}(t,dt)}},mt={f:Object.getOwnPropertySymbols},ht=ot("Reflect","ownKeys")||function(t){var n=yt.f(x(t)),r=mt.f;return r?n.concat(r(t)):n},vt=function(t,n){for(var r=ht(n),e=E.f,o=P.f,i=0;i<r.length;i++){var u=r[i];w(t,u)||e(t,u,o(n,u))}},bt=/#|\.prototype\./,wt=function(t,n){var r=Ot[St(t)];return r==jt||r!=Tt&&("function"==typeof n?i(n):!!n)},St=wt.normalize=function(t){return String(t).replace(bt,".").toLowerCase()},Ot=wt.data={},Tt=wt.NATIVE="N",jt=wt.POLYFILL="P",Pt=wt,xt=P.f,At=Array.isArray||function(t){return"Array"==p(t)},Et=function(t){return Object(y(t))},Mt=function(t,n,r){var e=v(n);e in t?E.f(t,e,l(0,r)):t[e]=r},Ct=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),Rt=Ct&&!Symbol.sham&&"symbol"==typeof Symbol(),_t=z("wks"),Nt=o.Symbol,It=Rt?Nt:H,Ft=function(t){return w(_t,t)||(Ct&&w(Nt,t)?_t[t]=Nt[t]:_t[t]=It("Symbol."+t)),_t[t]},Lt=Ft("species"),kt=function(t,n){var r;return At(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!At(r.prototype)?h(r)&&null===(r=r[Lt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},qt=ot("navigator","userAgent")||"",Dt=o.process,zt=Dt&&Dt.versions,Bt=zt&&zt.v8;Bt?$=(Z=Bt.split("."))[0]+Z[1]:qt&&(!(Z=qt.match(/Edge\/(\d+)/))||Z[1]>=74)&&(Z=qt.match(/Chrome\/(\d+)/))&&($=Z[1]);var Gt,Ht=$&&+$,Wt=Ft("species"),Jt=Ft("isConcatSpreadable"),Kt=Ht>=51||!i((function(){var t=[];return t[Jt]=!1,t.concat()[0]!==t})),Qt=(Gt="concat",Ht>=51||!i((function(){var t=[];return(t.constructor={})[Wt]=function(){return{foo:1}},1!==t[Gt](Boolean).foo}))),Ut=function(t){if(!h(t))return!1;var n=t[Jt];return void 0!==n?!!n:At(t)};!function(t,n){var r,e,i,u,a,c=t.target,f=t.global,l=t.stat;if(r=f?o:l?o[c]||C(c,{}):(o[c]||{}).prototype)for(e in n){if(u=n[e],i=t.noTargetGet?(a=xt(r,e))&&a.value:r[e],!Pt(f?e:c+(l?".":"#")+e,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;vt(u,i)}(t.sham||i&&i.sham)&&M(u,"sham",!0),nt(r,e,u,t)}}({target:"Array",proto:!0,forced:!Kt||!Qt},{concat:function(t){var n,r,e,o,i,u=Et(this),a=kt(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(i=-1===n?u:arguments[n],Ut(i)){if(c+(o=ft(i.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(r=0;r<o;r++,c++)r in i&&Mt(a,c,i[r])}else{if(c>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Mt(a,c++,i)}return a.length=c,a}}),t.fn.bootstrapTable.locales["es-NI"]={formatLoadingMessage:function(){return"Cargando, por favor espere"},formatRecordsPerPage:function(t){return"".concat(t," registros por página")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Mostrando de ".concat(t," a ").concat(n," registros de ").concat(r," registros en total (filtered from ").concat(e," total rows)"):"Mostrando de ".concat(t," a ").concat(n," registros de ").concat(r," registros en total")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refrescar"},formatToggle:function(){return"Alternar"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Todo"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["es-NI"])}));
