/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.16.0
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&t.hasOwnProperty("default")?t.default:t;var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t,e){return t(e={exports:{}},e.exports),e.exports}var r=function(t){return t&&t.Math==Math&&t},o=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),a={}.propertyIsEnumerable,c=Object.getOwnPropertyDescriptor,f={f:c&&!a.call({1:2},1)?function(t){var e=c(this,t);return!!e&&e.enumerable}:a},s=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},l={}.toString,p=function(t){return l.call(t).slice(8,-1)},d="".split,y=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?d.call(t,""):Object(t)}:Object,h=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},g=function(t){return y(h(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},v=function(t,e){if(!b(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!b(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!b(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!b(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")},w={}.hasOwnProperty,m=function(t,e){return w.call(t,e)},O=o.document,S=b(O)&&b(O.createElement),x=!u&&!i((function(){return 7!=Object.defineProperty((t="div",S?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),j=Object.getOwnPropertyDescriptor,D={f:u?j:function(t,e){if(t=g(t),e=v(e,!0),x)try{return j(t,e)}catch(t){}if(m(t,e))return s(!f.f.call(t,e),t[e])}},T=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},R=Object.defineProperty,P={f:u?R:function(t,e,n){if(T(t),e=v(e,!0),T(n),x)try{return R(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},_=u?function(t,e,n){return P.f(t,e,s(1,n))}:function(t,e,n){return t[e]=n,t},E=function(t,e){try{_(o,t,e)}catch(n){o[t]=e}return e},A=o["__core-js_shared__"]||E("__core-js_shared__",{}),M=Function.toString;"function"!=typeof A.inspectSource&&(A.inspectSource=function(t){return M.call(t)});var k,C,I,B,F=A.inspectSource,N=o.WeakMap,L="function"==typeof N&&/native code/.test(F(N)),q=n((function(t){(t.exports=function(t,e){return A[t]||(A[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),$=0,z=Math.random(),H=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++$+z).toString(36)},G=q("keys"),V={},W=o.WeakMap;if(L){var K=new W,Q=K.get,Y=K.has,J=K.set;k=function(t,e){return J.call(K,t,e),e},C=function(t){return Q.call(K,t)||{}},I=function(t){return Y.call(K,t)}}else{var U=G[B="state"]||(G[B]=H(B));V[U]=!0,k=function(t,e){return _(t,U,e),e},C=function(t){return m(t,U)?t[U]:{}},I=function(t){return m(t,U)}}var X,Z,tt={set:k,get:C,has:I,enforce:function(t){return I(t)?C(t):k(t,{})},getterFor:function(t){return function(e){var n;if(!b(e)||(n=C(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return n}}},et=n((function(t){var e=tt.get,n=tt.enforce,r=String(String).split("String");(t.exports=function(t,e,i,u){var a=!!u&&!!u.unsafe,c=!!u&&!!u.enumerable,f=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof e||m(i,"name")||_(i,"name",e),n(i).source=r.join("string"==typeof e?e:"")),t!==o?(a?!f&&t[e]&&(c=!0):delete t[e],c?t[e]=i:_(t,e,i)):c?t[e]=i:E(e,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||F(this)}))})),nt=o,rt=function(t){return"function"==typeof t?t:void 0},ot=function(t,e){return arguments.length<2?rt(nt[t])||rt(o[t]):nt[t]&&nt[t][e]||o[t]&&o[t][e]},it=Math.ceil,ut=Math.floor,at=function(t){return isNaN(t=+t)?0:(t>0?ut:it)(t)},ct=Math.min,ft=function(t){return t>0?ct(at(t),9007199254740991):0},st=Math.max,lt=Math.min,pt=function(t,e){var n=at(t);return n<0?st(n+e,0):lt(n,e)},dt=function(t){return function(e,n,r){var o,i=g(e),u=ft(i.length),a=pt(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},yt={includes:dt(!0),indexOf:dt(!1)},ht=yt.indexOf,gt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return function(t,e){var n,r=g(t),o=0,i=[];for(n in r)!m(V,n)&&m(r,n)&&i.push(n);for(;e.length>o;)m(r,n=e[o++])&&(~ht(i,n)||i.push(n));return i}(t,gt)}},vt={f:Object.getOwnPropertySymbols},wt=ot("Reflect","ownKeys")||function(t){var e=bt.f(T(t)),n=vt.f;return n?e.concat(n(t)):e},mt=function(t,e){for(var n=wt(e),r=P.f,o=D.f,i=0;i<n.length;i++){var u=n[i];m(t,u)||r(t,u,o(e,u))}},Ot=/#|\.prototype\./,St=function(t,e){var n=jt[xt(t)];return n==Tt||n!=Dt&&("function"==typeof e?i(e):!!e)},xt=St.normalize=function(t){return String(t).replace(Ot,".").toLowerCase()},jt=St.data={},Dt=St.NATIVE="N",Tt=St.POLYFILL="P",Rt=St,Pt=D.f,_t=function(t,e){var n,r,i,u,a,c=t.target,f=t.global,s=t.stat;if(n=f?o:s?o[c]||E(c,{}):(o[c]||{}).prototype)for(r in e){if(u=e[r],i=t.noTargetGet?(a=Pt(n,r))&&a.value:n[r],!Rt(f?r:c+(s?".":"#")+r,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;mt(u,i)}(t.sham||i&&i.sham)&&_(u,"sham",!0),et(n,r,u,t)}},Et=Array.isArray||function(t){return"Array"==p(t)},At=function(t){return Object(h(t))},Mt=function(t,e,n){var r=v(e);r in t?P.f(t,r,s(0,n)):t[r]=n},kt=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),Ct=kt&&!Symbol.sham&&"symbol"==typeof Symbol(),It=q("wks"),Bt=o.Symbol,Ft=Ct?Bt:H,Nt=function(t){return m(It,t)||(kt&&m(Bt,t)?It[t]=Bt[t]:It[t]=Ft("Symbol."+t)),It[t]},Lt=Nt("species"),qt=function(t,e){var n;return Et(t)&&("function"!=typeof(n=t.constructor)||n!==Array&&!Et(n.prototype)?b(n)&&null===(n=n[Lt])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===e?0:e)},$t=ot("navigator","userAgent")||"",zt=o.process,Ht=zt&&zt.versions,Gt=Ht&&Ht.v8;Gt?Z=(X=Gt.split("."))[0]+X[1]:$t&&(!(X=$t.match(/Edge\/(\d+)/))||X[1]>=74)&&(X=$t.match(/Chrome\/(\d+)/))&&(Z=X[1]);var Vt=Z&&+Z,Wt=Nt("species"),Kt=function(t){return Vt>=51||!i((function(){var e=[];return(e.constructor={})[Wt]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Qt=Nt("isConcatSpreadable"),Yt=Vt>=51||!i((function(){var t=[];return t[Qt]=!1,t.concat()[0]!==t})),Jt=Kt("concat"),Ut=function(t){if(!b(t))return!1;var e=t[Qt];return void 0!==e?!!e:Et(t)};_t({target:"Array",proto:!0,forced:!Yt||!Jt},{concat:function(t){var e,n,r,o,i,u=At(this),a=qt(u,0),c=0;for(e=-1,r=arguments.length;e<r;e++)if(i=-1===e?u:arguments[e],Ut(i)){if(c+(o=ft(i.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<o;n++,c++)n in i&&Mt(a,c,i[n])}else{if(c>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Mt(a,c++,i)}return a.length=c,a}});var Xt,Zt,te=yt.indexOf,ee=[].indexOf,ne=!!ee&&1/[1].indexOf(1,-0)<0,re=!(Zt=[]["indexOf"])||!i((function(){Zt.call(null,Xt||function(){throw 1},1)}));_t({target:"Array",proto:!0,forced:ne||re},{indexOf:function(t){return ne?ee.apply(this,arguments)||0:te(this,t,arguments.length>1?arguments[1]:void 0)}});var oe=Math.max,ie=Math.min;function ue(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function ae(t){return(ae=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ce(t,e){return(ce=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function fe(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function se(t,e,n){return(se="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=ae(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(n):o.value}})(t,e,n||t)}_t({target:"Array",proto:!0,forced:!Kt("splice")},{splice:function(t,e){var n,r,o,i,u,a,c=At(this),f=ft(c.length),s=pt(t,f),l=arguments.length;if(0===l?n=r=0:1===l?(n=0,r=f-s):(n=l-2,r=ie(oe(at(e),0),f-s)),f+n-r>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(o=qt(c,r),i=0;i<r;i++)(u=s+i)in c&&Mt(o,i,c[u]);if(o.length=r,n<r){for(i=s;i<f-r;i++)a=i+n,(u=i+r)in c?c[a]=c[u]:delete c[a];for(i=f;i>f-r+n;i--)delete c[i-1]}else if(n>r)for(i=f-r;i>s;i--)a=i+n-1,(u=i+r-1)in c?c[a]=c[u]:delete c[a];for(i=0;i<n;i++)c[i+s]=arguments[i+2];return c.length=f-r+n,o}});var le=function(t,e){return{id:"customId_".concat(e)}};t.extend(t.fn.bootstrapTable.defaults,{reorderableRows:!1,onDragStyle:null,onDropStyle:null,onDragClass:"reorder_rows_onDragClass",dragHandle:">tbody>tr>td",useRowAttrFunc:!1,onReorderRowsDrag:function(t){return!1},onReorderRowsDrop:function(t){return!1},onReorderRow:function(t){return!1}}),t.extend(t.fn.bootstrapTable.Constructor.EVENTS,{"reorder-row.bs.table":"onReorderRow"}),t.BootstrapTable=function(e){function n(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),fe(this,ae(n).apply(this,arguments))}var r,o,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&ce(t,e)}(n,e),r=n,(o=[{key:"init",value:function(){for(var t,e=this,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];if(this.options.reorderableRows){this.options.useRowAttrFunc&&(this.options.rowAttributes=le);var u=this.options.onPostBody;this.options.onPostBody=function(){setTimeout((function(){e.makeRowsReorderable(),u.apply()}),1)},(t=se(ae(n.prototype),"init",this)).call.apply(t,[this].concat(o))}else{var a;(a=se(ae(n.prototype),"init",this)).call.apply(a,[this].concat(o))}}},{key:"makeRowsReorderable",value:function(){var t=this;this.$el.tableDnD({onDragStyle:this.options.onDragStyle,onDropStyle:this.options.onDropStyle,onDragClass:this.options.onDragClass,onDragStart:function(e,n){return t.onDropStart(e,n)},onDrop:function(e,n){return t.onDrop(e,n)},dragHandle:this.options.dragHandle})}},{key:"onDropStart",value:function(e,n){this.$draggingTd=t(n).css("cursor","move"),this.draggingIndex=t(this.$draggingTd.parent()).data("index"),this.options.onReorderRowsDrag(this.data[this.draggingIndex])}},{key:"onDrop",value:function(e){this.$draggingTd.css("cursor","");for(var n=[],r=0;r<e.tBodies[0].rows.length;r++){var o=t(e.tBodies[0].rows[r]);n.push(this.data[o.data("index")]),o.data("index",r)}var i=this.data[this.draggingIndex],u=n.indexOf(this.data[this.draggingIndex]),a=this.data[u],c=this.options.data.indexOf(this.data[u]);this.options.data.splice(this.options.data.indexOf(i),1),this.options.data.splice(c,0,i),this.options.onReorderRowsDrop(a),this.trigger("reorder-row",n)}}])&&ue(r.prototype,o),i&&ue(r,i),n}(t.BootstrapTable)}));
