package com.hvisions.framework.dto.file;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: FileTypeQueryDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/12</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "文件类型查询对象")
@Data
public class FileTypeQueryDTO extends PageInfo {

    public FileTypeQueryDTO() {
        this.fileTypeName = "";
        this.fileTypeCode = "";
    }

    /**
     * 文件类型名称
     */
    @ApiModelProperty(value = "文件类型名称")
    public String fileTypeName;

    /**
     * 文件类型编码
     */
    @ApiModelProperty(value = "文件类型编码")
    public String fileTypeCode;
}









