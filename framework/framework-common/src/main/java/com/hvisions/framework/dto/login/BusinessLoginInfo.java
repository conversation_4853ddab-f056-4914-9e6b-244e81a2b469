package com.hvisions.framework.dto.login;

/**
 * <p>Title: BusinessLoginInfo</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/2/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BusinessLoginInfo {
    /**
    *   用户id
    */
    @ApiModelProperty(value = "用户id")
    private Integer userId;
    /**
    *   标识符
    */
    @ApiModelProperty(value = "标识符")
    private String identityId;
}









