package com.hvisions.framework.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: Title: 用户组实体</p>
 * <p>create date: 2021/11/12</p>
 *
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Table(uniqueConstraints = @UniqueConstraint(columnNames = "groupName", name = "用户组名称不能重复"))
public class SysGroup extends SysBase {

    /**
     * 用户组名称
     */
    @NotBlank(message = "用户组名称不能为空")
    @Length(max = 255, message = "用户组名称不能超过255个字符")
    private String groupName;

    /**
     * 用户组描述
     */
    @Length(max = 255, message = "用户组描述不能超过255个字符")
    private String description;

    /**
     * 首页模块
     */
    private Integer appModuleId;

    /**
     * 用户组角色列表
     */
    @ManyToMany(cascade = CascadeType.REFRESH, fetch = FetchType.LAZY)
    @JoinTable(name = "sys_group_role",
        joinColumns = @JoinColumn(name = "group_id", referencedColumnName = "id"),
        inverseJoinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id"))
    @JsonIgnore
    private List<SysRole> roles;

    /**
     * 用户组角色列表
     */
    @OneToMany(cascade = {CascadeType.MERGE, CascadeType.REMOVE, CascadeType.PERSIST, CascadeType.REFRESH}, mappedBy = "group", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<SysUser> users = new ArrayList<>();

}
