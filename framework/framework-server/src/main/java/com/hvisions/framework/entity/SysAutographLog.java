package com.hvisions.framework.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import java.util.Date;

/**
 * <p>Title: SysAutographLog</p >
 * <p>Description: 电子签名操作日志</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/2/17</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Entity
@Getter
@Setter
public class SysAutographLog extends SysBase {

    /**
     * 当前平台登录的id
     */
    private Integer platFromLoginId;


    /**
     * 电子签名验证id
     */
    private Integer autographId;


    /**
     * 操作结果
     */
    private String operationResult;


    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * 操作时间
     */
    private Date operationTime;


}