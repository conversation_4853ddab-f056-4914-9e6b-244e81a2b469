package com.hvisions.framework.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.framework.dto.file.FileInfoDTO;
import com.hvisions.framework.dto.file.FileInfoQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:FileInfoRepository</p>
 * <p>Description:文件分页查询</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/25</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
@Mapper
public interface FileInfoMapper extends BaseMapper<FileInfoDTO> {
    /**
     * 分页查询文件 mysql
     *
     * @param fileInfoQueryDTO 文件查询
     * @return 分页
     */
    List<FileInfoDTO> getFile(FileInfoQueryDTO fileInfoQueryDTO);

}
