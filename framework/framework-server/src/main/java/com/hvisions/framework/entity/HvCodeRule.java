package com.hvisions.framework.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * <p>Title: HvCodeRuleSetting</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2023-02-01</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@Table(uniqueConstraints = @UniqueConstraint(columnNames = {"ruleCode"}, name = "规则编码不能重复"))
public class HvCodeRule extends SysBase {

    /**
     * 规则分类id
     */
    private Integer storageId;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 是否启用
     */
    private Boolean used;

}
