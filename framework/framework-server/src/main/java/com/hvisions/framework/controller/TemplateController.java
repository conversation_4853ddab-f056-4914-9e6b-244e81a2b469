package com.hvisions.framework.controller;

import com.hvisions.framework.dto.board.TemplateDTO;
import com.hvisions.framework.dto.board.TemplateQueryDTO;
import com.hvisions.framework.service.TemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>Title: TemplateController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@Api(description = "报表模版控制器")
@RequestMapping(value = "/template")
public class TemplateController {


    private final TemplateService templateService;

    @Autowired
    public TemplateController(TemplateService templateService) {
        this.templateService = templateService;
    }


    /**
     * 创建模版
     *
     * @param templateDTO 模版对象
     */
    @ApiOperation(value = "创建模版")
    @PostMapping(value = "/createTemplate")
    public void createTemplate(@Valid @RequestBody TemplateDTO templateDTO) {
        templateService.createTemplate(templateDTO);
    }

    /**
     * 更新模版
     *
     * @param templateDTO 模版对象
     */
    @ApiOperation(value = "更新模版")
    @PutMapping(value = "updateTemplate")
    public void updateTemplate(@Valid @RequestBody TemplateDTO templateDTO) {
        templateService.createTemplate(templateDTO);
    }


    /**
     * 删除
     *
     * @param id 模版id
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/deleteById/{id}")
    public void deleteById(@PathVariable int id) {
        templateService.deleteById(id);
    }


    /**
     * 分页查询模版信息
     *
     * @param templateQueryDTO 查询对象
     * @return 模版信息分页列表
     */
    @ApiOperation(value = "分页查询模版信息")
    @PostMapping(value = "/getTemplateByQuery")
    public Page<TemplateDTO> getTemplateByQuery(@RequestBody TemplateQueryDTO templateQueryDTO) {
        return templateService.getTemplateByQuery(templateQueryDTO);
    }
}