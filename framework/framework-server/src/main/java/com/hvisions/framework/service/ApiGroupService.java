package com.hvisions.framework.service;

import com.hvisions.framework.dto.api.ApiGroupDTO;

import java.util.List;

/**
 * <p>Title: ApiGroupService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/6/15</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface ApiGroupService {
    /**
     * 创建APi分组
     *
     * @param groupDTO 分组信息
     * @return 分组id
     */
    Integer create(ApiGroupDTO groupDTO);

    /**
     * 更新Api分组信息
     *
     * @param group 分组信息
     * @return 分组id
     */
    Integer update(ApiGroupDTO group);

    /**
     * 删除Api分组信息
     *
     * @param groupId 分组id
     */
    void delete(Integer groupId);

    /**
     * 根据父级id查询分组列表
     *
     * @param parentId 父级id
     * @return 分组信息列表
     */
    List<ApiGroupDTO> findByParentId(Integer parentId);

    /**
     * 获取所有分组信息
     * @return 获取所有分组信息
     */
    List<ApiGroupDTO> findAll();
}

    
    
    
    