package com.hvisions.framework.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.common.config.dynamic.endpint.ServiceInfoEndpoint;
import com.hvisions.common.config.dynamic.service.DynamicConfigService;
import com.hvisions.common.dto.ChangeProperty;
import com.hvisions.common.dto.ConfigInfo;
import com.hvisions.common.dto.ConfigProperty;
import com.hvisions.common.dto.PublishConfigDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.framework.service.ConfigControlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.time.Duration;
import java.time.temporal.TemporalUnit;
import java.util.*;

/**
 * <p>Title: ConfigControlServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2023-02-21</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
@Slf4j
public class ConfigControlServiceImpl implements ConfigControlService {

    private final DynamicConfigService dynamicConfigService;

    private final NamingService namingService;

    private final ObjectMapper objectMapper;

    @Autowired
    public ConfigControlServiceImpl(DynamicConfigService dynamicConfigService,
                                    NamingService namingService,
                                    ObjectMapper objectMapper) {
        this.dynamicConfigService = dynamicConfigService;
        this.namingService = namingService;
        this.objectMapper = objectMapper;
    }


    /**
     * 获取所有服务的基础信息
     *
     * @return 服务信息
     * @throws NacosException ex
     */
    @Override
    @Cacheable(value = "configInfo", key = "#root.methodName")
    public List<ServiceInfoEndpoint.ServiceInfo> pullAllServiceInfo() throws NacosException {
        ListView<String> services = namingService.getServicesOfServer(1, 100);
        if (services == null || services.getData() == null) {
            return Collections.emptyList();
        }
        List<ServiceInfoEndpoint.ServiceInfo> infos = new ArrayList<>(services.getCount());
        services.getData().parallelStream().forEach(service -> {
            try {
                //发起http请求
                ServiceInfoEndpoint.ServiceInfo serviceInfo = pullInfoFromServices(service);
                if (serviceInfo == null || serviceInfo.getDataId() == null) {
                    return;
                }
                //获取配置信息,如果没有配置信息,则不添加到服务列表中
                ConfigInfo configInfo = getConfigByDataId(serviceInfo.getDataId());
                boolean configInfoIsNull = Objects.isNull(configInfo) ||
                        CollectionUtils.isEmpty(configInfo.getConfigProperties());
                if (configInfoIsNull) {
                    return;
                }
                infos.add(serviceInfo);
            } catch (Exception e) {
                log.error("获取服务{}的信息失败", service, e);
            }
        });
        return infos;
    }


    /**
     * 从服务对应的EndPoint获取服务的配置信息,如果有多个实例,只取获取到的第一个
     *
     * @param dataId 服务名称
     * @return 配置信息
     */
    @Override
    @Cacheable(value = "configInfo", key = "#dataId")
    public ConfigInfo getConfigByDataId(String dataId) {
        ConfigInfo configInfo = null;
        try {
            List<Instance> allInstances = namingService.selectInstances(dataId, true);
            if (CollectionUtils.isEmpty(allInstances)) {
                return configInfo;
            }
            for (Instance instance : allInstances) {
                String ip = instance.getIp();
                Integer port = instance.getPort();
                String url = String.format("http://%s:%s/actuator/pullConfig", ip, port);
                configInfo = pullConfig(url);
                //找到任意一个服务实例的配
                if (ObjectUtil.isNotEmpty(configInfo)) {
                    break;
                }
            }
        } catch (Exception e) {
            throw new BaseKnownException("获取" + dataId + "实例的配置失败", e.getMessage());
        }
        if (!Objects.isNull(configInfo) && CollectionUtils.isNotEmpty(configInfo.getConfigProperties())) {
            //替换点为斜杠,要不前端无法获取key
            configInfo.getConfigProperties().forEach(c -> c.setDataKey(c.getDataKey().replace(".", "/")));
        }
        return configInfo;
    }

    /**
     * 发布配置到配置中心
     *
     * @param publishConfigDTO 配置数据
     * @return 是否成功
     */
    @Override
    @CacheEvict(value = "configInfo", key = "#publishConfigDTO.dataId")
    public Boolean publishConfig(PublishConfigDTO publishConfigDTO) {
        //获取原始配置
        ConfigInfo configInfo = getConfigByDataId(publishConfigDTO.getDataId());
        if (Objects.isNull(configInfo)) {
            log.error("获取" + publishConfigDTO.getDataId() + "实例的配置失败");
            return false;
        }
        //转换/为.,用于后端业务判断
        if (CollectionUtils.isNotEmpty(configInfo.getConfigProperties())) {
            configInfo.getConfigProperties().forEach(p -> p.setDataKey(p.getDataKey().replace("/", ".")));
        }
        //转换配置key,将/转换为.
        publishConfigDTO.getChangeProperties().forEach(p -> p.setDataKey(p.getDataKey().replace("/", ".")));
        //检查配置
        checkConfig(publishConfigDTO.getChangeProperties(), configInfo.getConfigProperties());
        //相同配置检查,如果所有的都相同则不发布,如果检查完存在需要重启的配置,再重启服务
        serverProtect(publishConfigDTO, configInfo.getConfigProperties());
        //如果为空,则证明没有修改过的配置,不需要发布
        if (CollectionUtils.isEmpty(publishConfigDTO.getChangeProperties())) {
            log.debug("没有需要修改的配置");
            return false;
        }
        dynamicConfigService.publishConfig(publishConfigDTO);
        return publishConfigDTO.getRestart();
    }

    /**
     * 检查配置,只有配置值确实修改了才会发布
     * 且配置值改为之后,需要重启服务的配置,需要重启服务,防止传入restart频繁重启服务
     *
     * @param publishConfigDTO 需要修改的配置
     * @param configProperties 原始配置
     */
    private void serverProtect(PublishConfigDTO publishConfigDTO, List<ConfigProperty> configProperties) {
        List<ChangeProperty> changeProperties = publishConfigDTO.getChangeProperties();
        boolean restart = false;
        Iterator<ChangeProperty> iterator = changeProperties.iterator();
        //遍历修改的配置,如果原始配置和修改的配置相同,则不需要再次发布,不能使用for循环,有坑
        while (iterator.hasNext()) {
            ChangeProperty changeProperty = iterator.next();
            //获取原始配置的值
            Optional<ConfigProperty> property = configProperties.stream()
                    .filter(c -> c.getDataKey().equals(changeProperty.getDataKey()))
                    .findFirst();
            if (property.isPresent()) {
                //如果原始配置和修改的配置相同或者值是空的,则不需要再次发布
                boolean notPublish = property.get().getDataValue().equals(changeProperty.getDataValue());
                if (notPublish) {
                    log.debug("配置{}没有修改,不需要发布", changeProperty.getDataKey());
                    iterator.remove();
                } else if (property.get().getNeedRestart()) {
                    //如果有需要重启的配置,则需要重启服务
                    restart = true;
                }
            }
        }
        publishConfigDTO.setRestart(restart);
    }

    /**
     * 读取服务的配置
     *
     * @param url url
     */
    private ConfigInfo pullConfig(String url) throws JsonProcessingException {
        ConfigInfo configInfo = new ConfigInfo();
        //发送重启请求
        ResponseEntity<Object> responseEntity;
        try {
            RestTemplateBuilder builder = new RestTemplateBuilder();
            responseEntity = builder.build().getForEntity(url, Object.class);
        } catch (RestClientException e) {
            log.error("请求{}失败", url);
            return null;
        }
        boolean requestOk = responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.hasBody();
        if (requestOk) {
            String body = objectMapper.writeValueAsString(responseEntity.getBody());
            JavaType javaType = objectMapper.getTypeFactory().constructType(ConfigInfo.class);
            configInfo = objectMapper.readValue(body, javaType);
        }
        return configInfo;
    }


    /**
     * 根据服务名称获取服务的基础信息
     *
     * @param dataId 服务名称
     * @return 服务信息
     */
    private ServiceInfoEndpoint.ServiceInfo pullInfoFromServices(String dataId) {
        ServiceInfoEndpoint.ServiceInfo serviceInfo = null;
        try {
            List<Instance> allInstances = namingService.selectInstances(dataId, true);
            if (CollectionUtils.isEmpty(allInstances)) {
                return null;
            }
            for (Instance instance : allInstances) {
                //找到任意一个服务实例，就直接返回
                if (serviceInfo != null) {
                    break;
                }
                String ip = instance.getIp();
                Integer port = instance.getPort();
                String url = String.format("http://%s:%s/actuator/serviceInfo", ip, port);
                serviceInfo = pullServiceInfo(url);
            }
        } catch (Exception e) {
            throw new BaseKnownException("获取" + dataId + "实例的配置失败", e.getMessage());
        }
        return serviceInfo;
    }


    /**
     * 读取服务的信息,包括服务的中英文名称
     *
     * @param url url
     * @return 服务信息
     * @throws JsonProcessingException json转换异常
     */
    private ServiceInfoEndpoint.ServiceInfo pullServiceInfo(String url) throws JsonProcessingException {
        ServiceInfoEndpoint.ServiceInfo serviceInfo = null;
        //发送重启请求
        ResponseEntity<Object> responseEntity;
        try {
            RestTemplateBuilder builder = new RestTemplateBuilder();
            builder.setConnectTimeout(Duration.ofSeconds(5L));
            builder.setReadTimeout(Duration.ofSeconds(5L));
            responseEntity = builder.build().getForEntity(url, Object.class);
        } catch (RestClientException e) {
            log.info("请求{}失败", url);
            return null;
        }
        boolean requestOk = responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.hasBody();
        if (requestOk) {
            String body = objectMapper.writeValueAsString(responseEntity.getBody());
            JavaType javaType = objectMapper.getTypeFactory().constructType(ServiceInfoEndpoint.ServiceInfo.class);
            serviceInfo = objectMapper.readValue(body, javaType);
        }
        return serviceInfo;
    }

    /**
     * 校验配置
     * 1.配置是否存在
     * 2.配置值是否为空
     * 3.配置类型是否为空
     * 4.配置值是否符合类型
     *
     * @param properties       配置项
     * @param originProperties 原始配置
     */
    private void checkConfig(List<ChangeProperty> properties, List<ConfigProperty> originProperties) {
        if (CollectionUtils.isEmpty(properties)) {
            throw new BaseKnownException("需要发布的配置项不能为空");
        }
        for (ChangeProperty prop : properties) {
            String dataKey = prop.getDataKey();
            String dataValue = prop.getDataValue();
            if (StringUtils.isBlank(dataKey)) {
                throw new BaseKnownException("配置:" + dataKey + "的值不能为空");
            }
            if (CollectionUtils.isNotEmpty(originProperties)) {
                ConfigProperty configProperty = originProperties.stream()
                        .filter(c -> c.getDataKey().equals(dataKey))
                        .findFirst()
                        .orElseThrow(() -> new BaseKnownException("配置:" + dataKey + "不存在"));
                //1. 检查是否是可选值中的一个
                List<String> options = configProperty.getOptions();
                if (CollectionUtils.isNotEmpty(options) && !options.contains(dataValue)) {
                    throw new BaseKnownException("配置:" + dataKey + "的值不在可选值中");
                }
                //2. 检查配置值是否符合类型
                String dataTypeClass = configProperty.getDataTypeClass();
                if (StringUtils.isBlank(dataTypeClass)) {
                    throw new BaseKnownException("配置:" + dataKey + "的类型不能为空");
                }
                //3. 检查配置值是否符合类型
                checkValMatchKey(dataKey, dataValue, dataTypeClass);
            }
        }
    }

    /**
     * 检查值是否和类型匹配
     *
     * @param dataKey       配置key
     * @param dataValue     配置的值
     * @param dataTypeClass 配置class类型
     */
    private static void checkValMatchKey(String dataKey, String dataValue, String dataTypeClass) {
        try {
            //获取class
            Class<?> clazz = Class.forName(dataTypeClass);
            SpelExpressionParser parser = new SpelExpressionParser();
            Object value;
            //List 和 Map 格式
            if (clazz.isAssignableFrom(Collection.class)) {
                value = parser.parseExpression("{" + dataValue + "}").getValue(clazz);
            } else if (clazz.isAssignableFrom(Date.class)) {
                //如果是日期格式,调用hutool的parse测试
                StandardEvaluationContext context = new StandardEvaluationContext();
                context.registerFunction("parseDate", DateUtil.class.getDeclaredMethod("parse",
                        CharSequence.class));
                value = parser.parseExpression("#parseDate('" + dataValue + "')")
                        .getValue(context, Date.class);
            } else {
                //spel表达式解析String 类型
                value = parser.parseExpression("'" + dataValue + "'").getValue(clazz);
            }
            if (value == null) {
                throw new BaseKnownException("配置:" + dataKey + "的值不符合类型");
            }
        } catch (ClassNotFoundException e) {
            throw new BaseKnownException("配置:" + dataKey + "的类型不存在");
        } catch (Exception e) {
            throw new BaseKnownException("配置:" + dataKey + "的值不符合类型;");
        }
    }
}
