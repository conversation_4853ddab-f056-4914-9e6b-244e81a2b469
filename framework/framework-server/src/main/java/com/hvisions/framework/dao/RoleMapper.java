package com.hvisions.framework.dao;

import com.hvisions.framework.dto.role.AuthRoleDTO;
import com.hvisions.framework.dto.role.AuthRoleQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>Title: RoleMaper</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/8/16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Mapper
public interface RoleMapper {

    /**
     * 查询用户
     *
     * @param authRoleQuery 用户条件
     * @return 用户信息
     */
    List<AuthRoleDTO> getUserByRoleIdAndQuery(@Param("dto") AuthRoleQuery authRoleQuery);


}