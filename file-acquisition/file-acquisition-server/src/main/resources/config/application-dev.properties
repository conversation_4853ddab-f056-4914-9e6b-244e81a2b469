server.port=10001
#环境切换 dev/prod
#spring.profiles.active=dev

spring.application.name=file-acquisition
spring.main.allow-bean-definition-overriding=true
spring.jmx.enabled=false
server.servlet.encoding.force=true
spring.http.charset=UTF-8
spring.http.enabled=true
spring.tomcat.uri-encoding=UTF-8
spring.cache.type=redis
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

################################################################################
spring.jpa.show-sql=false
spring.jpa.open-in-view=true
# 第一次使用的配置：自动创建数据库表，如果是运行SQL脚本则无需使用此配置         #
# spring.jpa.hibernate.ddl-auto = create
# 第二次开始使用的配置：据库表会根据Entity的变动而更新         #
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database-platform= com.hvisions.fileacquisition.Dialect.SQLiteDialect

# Sqlite数据源        #
spring.datasource.driver-class-name=org.sqlite.JDBC
spring.datasource.url=**************************************************************************
spring.datasource.platform=sqlite


#异步线程池
#异步线程池组件开关，默认false
spring.async-thread-pool.enable=true
#核心线程数,默认：Java虚拟机可用线程数
spring.async-thread-pool.core-pool-size=8
#线程池最大线程数,默认：40000
spring.async-thread-pool.max-pool-size=40000
#线程队列最大线程数,默认：80000
spring.async-thread-pool.queue-capacity=80000
#自定义线程名前缀，默认：Async-ThreadPool-
spring.async-thread-pool.thread-name-prefix=Async-ThreadPool-
#线程池中线程最大空闲时间，默认：60，单位：秒
spring.async-thread-pool.keep-alive-seconds=60
#核心线程是否允许超时，默认false
spring.async-thread-pool.allow-core-thread-time-out=false
#IOC容器关闭时是否阻塞等待剩余的任务执行完成，默认:false（必须设置setAwaitTerminationSeconds）
spring.async-thread-pool.wait-for-tasks-to-complete-on-shutdown=false
#阻塞IOC容器关闭的时间，默认：10秒（必须设置setWaitForTasksToCompleteOnShutdown）
spring.async-thread-pool.await-termination-seconds=10

#文件上传频率 每5分钟
scheduled.task.cron=0 0/2 * * * ?

file.prohibit=xls,xlsx,txt,csv
#特殊文件名要求
file.file-names=aura-epatemp
#忽略的文件名称
file.file-ignore-names=~$
#读取文件父路径
#读取文件父路径
file.base-url=G:\\共享文件夹
#读取文件子路径多个逗号隔开 对应的mes文件夹在前 中转无服务器路径在后 文件夹不允许带空格和特殊符号
file.server-paths=gasphase|气相色谱（石子铺）,gasphasezjg|气相色谱（赵家沟）,aura|气质数据-气质数据处理相关文件,chromatograph|三重四级杆串联液相色谱仪(石子铺),\
  chromatographzj|三重四级杆串联液相色谱仪（赵家沟）,stableisotope|稳定同位素质谱联用仪,spectrophotometer|紫外分光光度计,icpmsmetal|ICP-MS金属检测,\
  stablemassqualitative|三重四级杆串联气相色谱仪\\气质定性,\
  liquidchromatograph|液相色谱仪1260,generaldata|通用数据,\
  stablemassration|三重四级杆串联气相色谱仪\\气质定量;三重四级杆串联气相色谱仪\\气质定量(塑化剂);三重四级杆串联气相色谱仪\\气质定量(农残);三重四级杆串联气相色谱仪\\气质定量风味(定量);三重四级杆串联气相色谱仪\\气质定量风味(半定量)

#存储类型
ufop.storage-type=0
#文件存储路径
ufop.local-storage-path= /app/HiperMaticDeploy/file/collect

#文件存上传服务器URL
ufop.upload-server-url=https://quality.zjld.com/quality-api/zhenjiucustom/equipmentImMaster/uploadFile