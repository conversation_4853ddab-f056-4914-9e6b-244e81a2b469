package com.hvisions.custom.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hvisions.custom.dto.EquipmentImMasterDTO;
import com.hvisions.custom.dto.EquipmentImMasterVO;
import com.hvisions.custom.dto.EquipmentImTypeDTO;
import com.hvisions.custom.dto.EquipmentImTypeVO;
import com.hvisions.custom.entity.EquipmentImMaster;
import com.hvisions.custom.entity.FileBean;
import com.hvisions.custom.util.ConvertData;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 设备导入主表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2024-03-15
 */
public interface EquipmentImMasterService {

    IPage<EquipmentImMasterVO> findPage(EquipmentImMasterDTO equipmentImMasterDTO);

    /**
     * 保存
     *
     * @param file
     * @param equipmentImMaster
     * @param userId
     */
    void save(MultipartFile file,EquipmentImMaster equipmentImMaster, Integer userId);

    IPage<Object> findByTypeId(EquipmentImTypeDTO equipmentImTypeDTO);

    List<Map<String,String>> findFileType();

    void delete(Integer id, Integer userId);

    void update(EquipmentImMaster equipmentImMaster, Integer userId);

    IPage<FileBean> findByType(EquipmentImTypeDTO equipmentImTypeDTO);

    String addByType(EquipmentImMaster equipmentImMaster, java.lang.Integer userId);

    String uploadFile(MultipartFile file, FileBean uploadFile);

    Long saveDataQms(String imDataType,Integer masterId, Integer userId, String userName);

    ConvertData findByTypeIdDetail(EquipmentImTypeVO equipmentImTypeVO);

    EquipmentImMaster findById(Integer id);

    void process(Integer fileId);
}