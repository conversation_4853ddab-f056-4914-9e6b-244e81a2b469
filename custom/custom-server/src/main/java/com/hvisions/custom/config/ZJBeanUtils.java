package com.hvisions.custom.config;


import com.hvisions.custom.service.jpush.LocalSpringBeanManager;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
* @Description: TODO(单例类获取   工具类)
* <AUTHOR>
* @date 2018年7月21日 
*/

@Component
public class ZJBeanUtils implements ApplicationContextAware {
	
    private static ApplicationContext ctx;
    
    private static LocalSpringBeanManager localSpringBeanManager;

  	public static LocalSpringBeanManager getLocalSpringBeanManager() {
  		return localSpringBeanManager;
  	}
    @Override
    public void setApplicationContext(ApplicationContext arg0)throws BeansException {
        ctx = arg0;
        localSpringBeanManager=ctx.getBean(LocalSpringBeanManager.class);
        
        System.out.println("BeanUtils ===> init end  ===> localSpringBeanManager > "+localSpringBeanManager.getClass().getSimpleName());
    }

    public static Object getBean(String beanName) {
        if(ctx == null){
            throw new NullPointerException();
        }
        return ctx.getBean(beanName);
    }

}

