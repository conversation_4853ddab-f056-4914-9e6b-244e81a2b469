package com.hvisions.custom.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Size;

/**
 * @Description: TODO
 * @Date: 2024/4/12 09:58
 * @Author: zhangq
 * @Version: 1.0
 */
@Table(name = "general_data")
@Entity
@org.hibernate.annotations.Table(appliesTo = "general_data",comment = "1通用数据")
@TableName("general_data")
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description="通用数据")
public class GeneralDataDO extends SysBase{

    /**
     * 主表ID
     */
    @ApiModelProperty(value="主表ID")
    private Integer imMatserId;

    @ApiModelProperty(value = "采集时间")
    private String collectTime;

    /**
     * 检验项目
     */
    @ApiModelProperty(value="样品编码/检验项目")
    private String sampleCode;

    @Column(columnDefinition = "varchar(5000) COMMENT '采集值'")
    private String compound;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 状态 0未处理 1已处理 2处理失败
     */
    @Column(columnDefinition="int DEFAULT '0' comment '状态 0未处理 1已处理 9处理失败'")
    private Integer processStatus = 0;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    @Column(columnDefinition ="text DEFAULT NULL COMMENT '错误信息'")
    private String errorMsg;

    /**
     * 检验人
     */
    @ApiModelProperty(value = "检验人")
    @Size(max = 32, message = "检验人最大长度要小于 32")
    private String checker;

    @Transient
    @TableField(exist = false)
    @ApiModelProperty(value = "样品名称")
    private String compoundName;
    /**
     * 采集值
     */
    @Transient
    @TableField(exist = false)
    @ApiModelProperty(value = "采集值")
    private String compoundAmount;
}