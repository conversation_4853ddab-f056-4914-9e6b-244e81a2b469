package com.hvisions.custom.service.imp;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.custom.dao.RegisterResultExcelMapper;
import com.hvisions.custom.dao.ReportMapper;
import com.hvisions.custom.dto.*;
import com.hvisions.custom.entity.RegisterResultExcel;
import com.hvisions.custom.service.ReportService;
import com.hvisions.custom.util.FileNameUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhoucong
 * @DateTime: 2024/4/19 22:33
 * @Description:
 */
@Service
@Slf4j
@EnableAsync
public class ReportServiceImpl implements ReportService {

    @Resource
    ReportMapper reportMapper;

    @Resource
    private RegisterResultExcelMapper registerResultExcelMapper;

    /**
     * 分页查询检验结果明细报表
     *
     * @param queryDTO
     * @return
     */
    @Override
    public IPage<InspectionDetailReportDTO> getInspectionDetailReportListByPage(InspectionDetailReportQueryDTO queryDTO) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(queryDTO.getInspectionNum())) {
            wrapper.like("i.inspection_num", queryDTO.getInspectionNum());
        }
        if (StringUtils.isNotBlank(queryDTO.getInspectionStatus())) {
            wrapper.eq("i.inspection_status", queryDTO.getInspectionStatus());
        }
        if (StringUtils.isNotBlank(queryDTO.getSceneName())) {
            wrapper.like("i.scene_name", queryDTO.getSceneName());
        }
        if (StringUtils.isNotBlank(queryDTO.getGroupName())) {
            wrapper.like("g.group_name", queryDTO.getGroupName());
        }
        if (StringUtils.isNotBlank(queryDTO.getInspectionStartTimeStart()) && StringUtils.isNotBlank(queryDTO.getInspectionStartTimeEnd())) {
            wrapper.between("ir.register_date", queryDTO.getInspectionStartTimeStart(), queryDTO.getInspectionStartTimeEnd());
        }
        if (StringUtils.isNotBlank(queryDTO.getInspectionEndTimeStart()) && StringUtils.isNotBlank(queryDTO.getInspectionEndTimeEnd())) {
            wrapper.between("i.inspection_end_time", queryDTO.getInspectionEndTimeStart(), queryDTO.getInspectionEndTimeEnd());
        }
        if (StringUtils.isNotBlank(queryDTO.getMaterialCode())) {
            wrapper.like("hbm.material_code", queryDTO.getMaterialCode());
        }
        //
        IPage<InspectionDetailReportDTO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        page = reportMapper.getInspectionDetailReportListByPage(page, wrapper);
        if (page != null && !CollectionUtils.isEmpty(page.getRecords())) {
            //获取子检验单结果数据
            wrapper = new QueryWrapper<>();
            List<Integer> InspectionIdList = page.getRecords().stream().map(s -> s.getId()).collect(Collectors.toList());
            wrapper.in("a.inspection_id", InspectionIdList);
            List<InspectionItemResultDTO> inspectionItemResultList = reportMapper.getInspectionItemResultListNew(wrapper);

            //关联单据
            QueryWrapper<T> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("i.id", InspectionIdList);
            if (StringUtils.isNotBlank(queryDTO.getFactory())) {
                queryWrapper.like("rrd.attr_value", queryDTO.getFactory());
            }
            List<InsRegRelDetailDTO> insRegRelDetailDTOS = reportMapper.getInspectionRegRelDetailListNew(queryWrapper);

            if ((inspectionItemResultList != null && !inspectionItemResultList.isEmpty()) || !CollectionUtils.isEmpty(insRegRelDetailDTOS)) {
                page.getRecords().forEach(item -> {
                    if (inspectionItemResultList != null && !inspectionItemResultList.isEmpty()) {
                        //先处理 子检验单结果
                        //按检验单获取相关数据
                        List<InspectionItemResultDTO> findCheckTypeList = inspectionItemResultList.stream()
                                .filter(s -> item.getId().equals(s.getInspectionId()))
                                .collect(Collectors.toList());

                        if (!findCheckTypeList.isEmpty()) {
                            List<InspectionDetailResultDTO> inspectionResultList = new ArrayList<>();
                            //提取检验类型
                            List<String> checkTypeList = findCheckTypeList.stream()
                                    .map(s -> s.getCheckTypeName())
                                    .distinct()
                                    .collect(Collectors.toList());

                            //遍历处理每个检验类型
                            for (String checkType : checkTypeList) {
                                //分组获取每个检验类型的检验项结果
                                List<InspectionItemResultDTO> findItemResultList = findCheckTypeList.stream()
                                        .filter(s -> checkType.equals(s.getCheckTypeName()))
                                        .collect(Collectors.toList());

                                InspectionDetailResultDTO inspectionResult = new InspectionDetailResultDTO();
                                inspectionResult.setCheckTypeName(checkType);
                                inspectionResult.setInspectionResult(findItemResultList.get(0).getInspectionResult());//数据里面，相同的检验类型结果是一样的

                                List<InspectionDetailResultItemDTO> itemList = new ArrayList<>();
                                //处理当前检验类型下的检验项数据
                                //只处理当前检验类型实际存在的检验项，不填充不存在的项
                                for (InspectionItemResultDTO itemResultDTO : findItemResultList) {
                                    InspectionDetailResultItemDTO itemResult = new InspectionDetailResultItemDTO();
                                    itemResult.setInspectionId(itemResultDTO.getInspectionId());
                                    itemResult.setCheckItemName(itemResultDTO.getCheckItemName());
                                    itemResult.setCheckResult(itemResultDTO.getCheckResult());
                                    itemResult.setInspectionResult(itemResultDTO.getInspectionResult());
                                    itemResult.setViewOrder(itemResultDTO.getViewOrder());
                                    itemResult.setIsPrint(itemResultDTO.getIsPrint());
                                    itemList.add(itemResult);
                                }

                                // 按viewOrder排序，确保显示顺序正确
                                itemList.sort((a, b) -> {
                                    if (a.getViewOrder() == null && b.getViewOrder() == null) return 0;
                                    if (a.getViewOrder() == null) return 1;
                                    if (b.getViewOrder() == null) return -1;
                                    return a.getViewOrder().compareTo(b.getViewOrder());
                                });

                                inspectionResult.setItemList(itemList);
                                inspectionResultList.add(inspectionResult);
                            }
                            item.setInspectionResultList(inspectionResultList);
                        }
                    }


                    //关联单据
                    if(!CollectionUtils.isEmpty(insRegRelDetailDTOS)){
                        List<InsRegRelDetailDTO> collect = insRegRelDetailDTOS.stream().filter(item2 -> item2.getId().equals(item.getId())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(collect)) {
                            item.setInsRegRelDetailDTOList(collect);
                        }
                    }
                });

            }
        }
        return page;
    }

    @Override
    public List<InspectionDetailReportDTO> getDataComparisonContrast(ContrastReportQueryDTO queryDTO) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        String[] split = queryDTO.getInspectionNum().split(",");
        List<String> inspectionNumList = Arrays.asList(split);

        if (StringUtils.isNotBlank(queryDTO.getInspectionNum())) {
            wrapper.in("i.inspection_num", inspectionNumList);
        }

        if (StringUtils.isNotBlank(queryDTO.getSceneName())) {
            wrapper.like("i.scene_name", queryDTO.getSceneName());
        }

        if (StringUtils.isNotBlank(queryDTO.getInspectionStatus())) {
            wrapper.eq("i.inspection_status", queryDTO.getInspectionStatus());
        }

        if (StringUtils.isNotBlank(queryDTO.getGroupName())) {
            wrapper.like("g.group_name", queryDTO.getGroupName());
        }
        if (StringUtils.isNotBlank(queryDTO.getInspectionStartTimeStart()) && StringUtils.isNotBlank(queryDTO.getInspectionStartTimeEnd())) {
            wrapper.between("i.inspection_start_time", queryDTO.getInspectionStartTimeStart(), queryDTO.getInspectionStartTimeEnd());
        }
        if (StringUtils.isNotBlank(queryDTO.getInspectionEndTimeStart()) && StringUtils.isNotBlank(queryDTO.getInspectionEndTimeEnd())) {
            wrapper.between("i.inspection_end_time", queryDTO.getInspectionEndTimeStart(), queryDTO.getInspectionEndTimeEnd());
        }
        wrapper.orderByDesc("i.id");

        List<InspectionDetailReportDTO> reportDTOList = reportMapper.getDataComparisonContrast(wrapper);

        if (!CollectionUtils.isEmpty(reportDTOList)) {
            //检验项目
            List<InspectionDetailResultItemDTO> itemListAll = new ArrayList<>();

            //获取子检验单结果数据
            wrapper = new QueryWrapper<>();
            List<Integer> InspectionIdList = reportDTOList.stream().map(s -> s.getId()).collect(Collectors.toList());
            wrapper.in("a.inspection_id", InspectionIdList);
            List<InspectionItemResultDTO> inspectionItemResultList = reportMapper.getInspectionItemResultList(wrapper);
            if (inspectionItemResultList != null && !inspectionItemResultList.isEmpty()) {
                reportDTOList.forEach(item -> {
                    //获取全部检验项，用于动态列填充
                    List<String> checkItemNameList = inspectionItemResultList.stream().map(s -> s.getCheckItemName()).distinct().collect(Collectors.toList());
                    //先处理 子检验单结果
                    //按检验单获取相关数据
                    List<InspectionItemResultDTO> findCheckTypeList = inspectionItemResultList.stream().filter(s -> item.getId().equals(s.getInspectionId())).collect(Collectors.toList());
                    if (!findCheckTypeList.isEmpty()) {
                        List<InspectionDetailResultDTO> inspectionResultList = new ArrayList<>();
                        //提取检验类型
                        List<String> checkTypeList = findCheckTypeList.stream().map(s -> s.getCheckTypeName()).distinct().collect(Collectors.toList());
                        //遍历处理
                        for (String checkType : checkTypeList) {
                            //分组获取每个检验类型的检验项结果
                            List<InspectionItemResultDTO> findItemResultList = findCheckTypeList.stream().filter(s -> checkType.equals(s.getCheckTypeName())).collect(Collectors.toList());
                            InspectionDetailResultDTO inspectionResult = new InspectionDetailResultDTO();
                            inspectionResult.setCheckTypeName(checkType);
                            inspectionResult.setInspectionResult(findItemResultList.get(0).getInspectionResult());//数据里面，相同的检验类型结果是一样的
                            List<InspectionDetailResultItemDTO> itemList = new ArrayList<>();
                            //再处理 子检验单检验项数据
                            //按全部检验项填充，不存在的用--
                            for (String checkItemName : checkItemNameList) {
                                InspectionDetailResultItemDTO itemResult = new InspectionDetailResultItemDTO();
                                List<InspectionItemResultDTO> existsItemResultList = findItemResultList.stream().filter(s -> checkItemName.equals(s.getCheckItemName())).collect(Collectors.toList());
                                if (existsItemResultList.isEmpty()) {
                                    itemResult.setCheckItemName(checkItemName);
                                    itemResult.setCheckResult("--");
                                } else {
                                    itemResult.setCheckItemName(existsItemResultList.get(0).getCheckItemName());
                                    itemResult.setCheckResult(existsItemResultList.get(0).getCheckResult());
                                }
                                itemList.add(itemResult);
                            }

                            itemListAll.addAll(itemList);
                            inspectionResult.setItemList(itemList);
                            inspectionResultList.add(inspectionResult);
                        }
                        item.setInspectionResultList(inspectionResultList);
                    }
                });
            }


            if(reportDTOList.size()>1 && itemListAll.size()>0){
                List<InspectionDetailResultDTO> inspectionResultList = reportDTOList.get(0).getInspectionResultList();
                if(!CollectionUtils.isEmpty(inspectionResultList)){
                    InspectionDetailResultDTO inspectionDetailResultDTO = inspectionResultList.get(0);
                    List<InspectionDetailResultItemDTO> itemList = inspectionDetailResultDTO.getItemList();
                    if(!CollectionUtils.isEmpty(itemList)){
                        itemListAll.forEach(v->{
                            List<InspectionDetailResultItemDTO> collect = itemList.stream().filter(item -> item.getCheckItemName().equals(v.getCheckItemName())).collect(Collectors.toList());
                            if(CollectionUtils.isEmpty(collect)){
                                InspectionDetailResultItemDTO itemDTO=new InspectionDetailResultItemDTO();
                                itemDTO.setCheckItemName(v.getCheckItemName());
                                itemDTO.setCheckResult("--");
                                itemList.add(itemDTO);
                            }
                        });
                    }
                }

            }
        }




        return reportDTOList;
    }

    @Override
    public IPage<RegisterDetailReportDTO> getRegisterDetailReportListByPage(RegisterDetailReportQueryDTO queryDTO) {
        // 创建查询条件包装器
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        // 根据查询DTO设置查询条件
        if (StringUtils.isNotBlank(queryDTO.getInspectionNum())) {
            wrapper.like("i.inspection_num", queryDTO.getInspectionNum()); // 检验单号模糊查询
        }
        if (StringUtils.isNotBlank(queryDTO.getInspectionStatus())) {
            wrapper.eq("i.inspection_status", queryDTO.getInspectionStatus()); // 检验状态精确查询
        }
        if (StringUtils.isNotBlank(queryDTO.getSceneName())) {
            wrapper.like("i.scene_name", queryDTO.getSceneName()); // 场景名称模糊查询
        }
        if (StringUtils.isNotBlank(queryDTO.getGroupName())) {
            wrapper.like("g.group_name", queryDTO.getGroupName()); // 分组名称模糊查询
        }
        if (StringUtils.isNotBlank(queryDTO.getInspectionStartTimeStart()) && StringUtils.isNotBlank(queryDTO.getInspectionStartTimeEnd())) {
            wrapper.between("ir.register_date", queryDTO.getInspectionStartTimeStart(), queryDTO.getInspectionStartTimeEnd()); // 检验开始时间范围查询
        }
        if (StringUtils.isNotBlank(queryDTO.getInspectionEndTimeStart()) && StringUtils.isNotBlank(queryDTO.getInspectionEndTimeEnd())) {
            wrapper.between("i.inspection_end_time", queryDTO.getInspectionEndTimeStart(), queryDTO.getInspectionEndTimeEnd()); // 检验结束时间范围查询
        }

        // 初始化分页对象
        IPage<RegisterDetailReportDTO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        // 查询分页数据
        page = reportMapper.getRegisterDetailReportListByPage(page, wrapper);

        // 如果查询到数据且记录不为空
        if (page != null && !CollectionUtils.isEmpty(page.getRecords())) {
            // 获取所有检验单的ID列表
            List<Integer> inspectionIdList = page.getRecords().stream().filter(dto -> dto.getInspectionId() != null).map(RegisterDetailReportDTO::getInspectionId).collect(Collectors.toList());

            // 查询子检验单结果数据
            QueryWrapper<T> itemWrapper = new QueryWrapper<>();
            itemWrapper.in("a.inspection_id", inspectionIdList);
            List<InspectionItemResultDTO> inspectionItemResultList = reportMapper.getInspectionItemResultList(itemWrapper);

            // 如果子检验单结果数据不为空
            if (inspectionItemResultList != null && !inspectionItemResultList.isEmpty()) {
                // 遍历每条主检验单记录
                page.getRecords().forEach(item -> {
                    // 获取所有检验项名称，用于动态列填充
                    List<String> checkItemNameList = inspectionItemResultList.stream().map(InspectionItemResultDTO::getCheckItemName).distinct().collect(Collectors.toList());

                    // 根据主检验单ID过滤子检验单结果
                    List<InspectionItemResultDTO> findCheckTypeList = inspectionItemResultList.stream()
                            .filter(s ->item.getInspectionId() != null && item.getInspectionId().equals(s.getInspectionId()))
                            .collect(Collectors.toList());

                    // 如果找到符合条件的子检验单结果
                    if (!findCheckTypeList.isEmpty()) {
                        List<InspectionDetailResultDTO> inspectionResultList = new ArrayList<>();

                        // 提取检验类型
                        List<String> checkTypeList = findCheckTypeList.stream().map(InspectionItemResultDTO::getCheckTypeName).distinct().collect(Collectors.toList());

                        // 遍历处理每个检验类型
                        for (String checkType : checkTypeList) {
                            // 分组获取每个检验类型的检验项结果
                            List<InspectionItemResultDTO> findItemResultList = findCheckTypeList.stream()
                                    .filter(s -> checkType.equals(s.getCheckTypeName()))
                                    .collect(Collectors.toList());

                            // 创建检验详细结果DTO对象
                            InspectionDetailResultDTO inspectionResult = new InspectionDetailResultDTO();
                            inspectionResult.setCheckTypeName(checkType);
                            inspectionResult.setInspectionResult(findItemResultList.get(0).getInspectionResult()); // 检验结果

                            // 如果检验类型包含“品评”或“感官”，则获取子检验单备注字段
                            if (checkType.contains("品评") || checkType.contains("感官")) {
                                inspectionResult.setSensory(reportMapper.getSensoryRemarkById(item.getInspectionId(),checkType));
                            }

                            // 创建检验项结果列表
                            List<InspectionDetailResultItemDTO> itemList = new ArrayList<>();

                            // 遍历所有检验项名称，按照检验项填充数据
                            for (String checkItemName : checkItemNameList) {
                                InspectionDetailResultItemDTO itemResult = new InspectionDetailResultItemDTO();
                                List<InspectionItemResultDTO> existsItemResultList = findItemResultList.stream()
                                        .filter(s -> checkItemName.equals(s.getCheckItemName()))
                                        .collect(Collectors.toList());

                                // 如果找到对应的检验项结果
                                if (existsItemResultList.isEmpty()) {
                                    itemResult.setCheckItemName(checkItemName);
                                    itemResult.setCheckResult(null); // 未找到结果时设为"--"
                                    itemResult.setInspectionResult(null); // 未找到结果时设为"--"
                                } else {
                                    itemResult.setCheckItemName(existsItemResultList.get(0).getCheckItemName());
                                    itemResult.setCheckResult(existsItemResultList.get(0).getCheckResult());
                                    itemResult.setInspectionResult(existsItemResultList.get(0).getInspectionResult());
                                }
                                itemList.add(itemResult);
                            }

                            // 设置检验项结果列表到检验详细结果DTO对象
                            inspectionResult.setItemList(itemList);
                            inspectionResultList.add(inspectionResult);
                        }

                        // 设置检验详细结果列表到主检验单记录中
                        inspectionResultList.sort((e1, e2) -> {
                            boolean e1Contains = e1.getCheckTypeName().contains("品评");
                            boolean e2Contains = e2.getCheckTypeName().contains("品评");
                            // 如果e1包含"品评"而e2不包含，则e1应该排在e2之前
                            if (e1Contains && !e2Contains) {
                                return -1;
                            }
                            // 如果e2包含"品评"而e1不包含，则e2应该排在e1之前
                            if (!e1Contains && e2Contains) {
                                return 1;
                            }
                            // 默认情况下，保持原有顺序
                            return 0;
                        });
                        item.setInspectionResultList(inspectionResultList);
                    }
                });
            }

            // 关联单据信息
            if (!CollectionUtils.isEmpty(page.getRecords())) {
                // 提取所有主检验单的ID列表
                //List<Integer> inspectionIdList2 = page.getRecords().stream().map(RegisterDetailReportDTO::getInspectionId).collect(Collectors.toList());

                // 构建查询条件
                QueryWrapper<T> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("i.id", inspectionIdList);

                // 查询关联单据信息
                List<InsRegRelDetailDTO> insRegRelDetailDTOS = reportMapper.newQueryInspectionRegRelDetailList(queryWrapper);

                // 将关联信息与主检验单关联
                Map<Integer, List<InsRegRelDetailDTO>> relDetailMap = insRegRelDetailDTOS.stream().filter(dto -> dto.getId() != null)
                        .collect(Collectors.groupingBy(InsRegRelDetailDTO::getId));

                // 遍历主检验单记录，设置关联信息
                for (RegisterDetailReportDTO item : page.getRecords()) {
                    List<InsRegRelDetailDTO> relatedDetails = relDetailMap.get(item.getInspectionId());
                    if (relatedDetails != null && !relatedDetails.isEmpty()) {
                        item.setInsRegRelDetailDTOList(relatedDetails);
                    } else {
                        item.setInsRegRelDetailDTOList(new ArrayList<>()); // 或者根据业务需求做其他处理
                    }
                }
            }
        }

        // 返回查询结果分页对象
        return page;
    }

    @Override
    @Async
    public String exportRegisterResultReportApply(InspectionDetailReportQueryDTO dto){
        //入参
        String pram = JSON.toJSONString(dto);
        LambdaQueryWrapper<RegisterResultExcel> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(RegisterResultExcel::getPram,pram);
        List<RegisterResultExcel> excelList = registerResultExcelMapper.selectList(queryWrapper1);
        if(!CollectionUtils.isEmpty(excelList)){
            return "已申请通过,可以下载";
        }

        IPage<InspectionDetailReportDTO> listByPage = getInspectionDetailReportListByPage(dto);

//
//        QueryWrapper<T> wrapper = new QueryWrapper<>();
//        if (StringUtils.isNotBlank(queryDTO.getInspectionNum())) {
//            wrapper.like("i.inspection_num", queryDTO.getInspectionNum());
//        }
//        if (StringUtils.isNotBlank(queryDTO.getInspectionStatus())) {
//            wrapper.eq("i.inspection_status", queryDTO.getInspectionStatus());
//        }
//        if (StringUtils.isNotBlank(queryDTO.getSceneName())) {
//            wrapper.like("i.scene_name", queryDTO.getSceneName());
//        }
//        if (StringUtils.isNotBlank(queryDTO.getGroupName())) {
//            wrapper.like("g.group_name", queryDTO.getGroupName());
//        }
//        if (StringUtils.isNotBlank(queryDTO.getInspectionStartTimeStart()) && StringUtils.isNotBlank(queryDTO.getInspectionStartTimeEnd())) {
//            wrapper.between("i.inspection_start_time", queryDTO.getInspectionStartTimeStart(), queryDTO.getInspectionStartTimeEnd());
//        }
//        if (StringUtils.isNotBlank(queryDTO.getInspectionEndTimeStart()) && StringUtils.isNotBlank(queryDTO.getInspectionEndTimeEnd())) {
//            wrapper.between("i.inspection_end_time", queryDTO.getInspectionEndTimeStart(), queryDTO.getInspectionEndTimeEnd());
//        }
//        //
//
//        List<RegisterDetailReportDTO> list = reportMapper.getRegisterDetailReportList(wrapper);
//        if (list != null && !CollectionUtils.isEmpty(list)) {
//            //获取子检验单结果数据
//            wrapper = new QueryWrapper<>();
//            List<Integer> InspectionIdList = list.stream().map(s -> s.getId()).collect(Collectors.toList());
//            wrapper.in("a.inspection_id", InspectionIdList);
//            List<InspectionItemResultDTO> inspectionItemResultList = reportMapper.getInspectionItemResultListNew(wrapper);
//            if (inspectionItemResultList != null && !inspectionItemResultList.isEmpty()) {
//                list.forEach(item -> {
//                    //获取全部检验项，用于动态列填充
//                    List<String> checkItemNameList = inspectionItemResultList.stream().map(s -> s.getCheckItemName()).distinct().collect(Collectors.toList());
//                    //先处理 子检验单结果
//                    //按检验单获取相关数据
//                    List<InspectionItemResultDTO> findCheckTypeList = inspectionItemResultList.stream().filter(s -> item.getId().equals(s.getInspectionId())).collect(Collectors.toList());
//                    if (!findCheckTypeList.isEmpty()) {
//                        List<InspectionDetailResultDTO> inspectionResultList = new ArrayList<>();
//                        //提取检验类型
//                        List<String> checkTypeList = findCheckTypeList.stream().map(s -> s.getCheckTypeName()).distinct().collect(Collectors.toList());
//                        //遍历处理
//                        for (String checkType : checkTypeList) {
//                            //分组获取每个检验类型的检验项结果
//                            List<InspectionItemResultDTO> findItemResultList = findCheckTypeList.stream().filter(s -> checkType.equals(s.getCheckTypeName())).collect(Collectors.toList());
//                            InspectionDetailResultDTO inspectionResult = new InspectionDetailResultDTO();
//                            inspectionResult.setCheckTypeName(checkType);
//                            inspectionResult.setInspectionResult(findItemResultList.get(0).getInspectionResult());//数据里面，相同的检验类型结果是一样的
//                            //checkTypeName包含品评，感官时，取子检验单备注字段
//                            //(一个校验单对应多个子检验单，取最新一条备注吗？？？)
//                            if(checkType.contains("品评") || checkType.contains("感官")){
//                                inspectionResult.setSensory(reportMapper.getRemarkById(item.getId()));
//
//                            }
//                            List<InspectionDetailResultItemDTO> itemList = new ArrayList<>();
//                            //再处理 子检验单检验项数据
//                            //按全部检验项填充，不存在的用--
//                            for (String checkItemName : checkItemNameList) {
//                                InspectionDetailResultItemDTO itemResult = new InspectionDetailResultItemDTO();
//                                List<InspectionItemResultDTO> existsItemResultList = findItemResultList.stream().filter(s -> StringUtils.equals(checkItemName,s.getCheckItemName())).collect(Collectors.toList());
//                                if (existsItemResultList.isEmpty()) {
//                                    itemResult.setCheckItemName(checkItemName);
//                                    itemResult.setCheckResult("--");
//                                    itemResult.setInspectionResult("--");
//                                } else {
//                                    itemResult.setCheckItemName(existsItemResultList.get(0).getCheckItemName());
//                                    itemResult.setCheckResult(existsItemResultList.get(0).getCheckResult());
//                                    itemResult.setInspectionResult(existsItemResultList.get(0).getInspectionResult());
//                                }
//                                itemList.add(itemResult);
//                            }
//                            inspectionResult.setItemList(itemList);
//                            inspectionResultList.add(inspectionResult);
//                        }
//                        item.setInspectionResultList(inspectionResultList);
//                    }
//                });
//            }
//
//            log.info("开始执行查询关联单据");
//            //关联单据
//            list.forEach(item -> {
//                QueryWrapper<T> queryWrapper = new QueryWrapper<>();
//                queryWrapper.in("i.id", item.getId());
//              /*  if (StringUtils.isNotBlank(queryDTO.getFactory())){
//                    queryWrapper.like("rrd.attr_value", queryDTO.getFactory());
//                }*/
//                List<InsRegRelDetailDTO> insRegRelDetailDTOS = reportMapper.getInspectionRegRelDetailListNew(queryWrapper);
//                if (!CollectionUtils.isEmpty(insRegRelDetailDTOS)) {
//                    item.setInsRegRelDetailDTOList(insRegRelDetailDTOS);
//                }
//            });
//        }



        log.info("开始执行插入到库");
        //出参
        String outPram = JSON.toJSONString(listByPage.getRecords());

        RegisterResultExcel excel=new RegisterResultExcel();
        excel.setPram(pram);
        excel.setOutPram(outPram);
        excel.setCreateTime(new Date());
        registerResultExcelMapper.insert(excel);
        return "申请通过,可以下载";
    }

    @Override
    public List<RegisterDetailReportDTO> exportRegisterResultReport(RegisterDetailReportQueryDTO queryDTO) throws IOException, IllegalAccessException{
        //入参
        String pram = JSON.toJSONString(queryDTO);
        LambdaQueryWrapper<RegisterResultExcel> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(RegisterResultExcel::getPram,pram);
        List<RegisterResultExcel> excelList = registerResultExcelMapper.selectList(queryWrapper1);
        if(CollectionUtils.isEmpty(excelList)){
            throw new BaseKnownException(110,"请先提交申请或正式处理中");
        }

        String outPram = excelList.get(0).getOutPram();
        return JSON.parseArray(outPram, RegisterDetailReportDTO.class);
    }

    @Override
    public ExcelExportDto exportReportTasteTaskDetail(ReportTasteTaskDetailPageDTO queryDTO, HttpServletRequest request, HttpServletResponse response) throws IOException, IllegalAccessException {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        wrapper.like("t1.inspection_scene", "制曲检验");

        if (StringUtils.isNotBlank(queryDTO.getFactory())) {
            wrapper.like("t9.attr_value", queryDTO.getFactory());
        }

        if (StringUtils.isNotBlank(queryDTO.getBarcode())){
            wrapper.eq("t8.barcode", queryDTO.getBarcode());
        }

        if (!ObjectUtils.isEmpty(queryDTO.getHostTimeStart()) && !ObjectUtils.isEmpty(queryDTO.getHostTimeEnd())) {
            wrapper.between("t1.host_time", queryDTO.getHostTimeStart(), queryDTO.getHostTimeEnd());
        }

        List<ReportTasteTaskDetailDTO> list = reportMapper.getReportTasteTaskDetailList(wrapper);

        ResponseEntity<byte[]> responseEntity = ExcelUtil.generateImportFile(list,
                "品曲详情", ReportTasteTaskDetailDTO.class);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName(FileNameUtil.createExcelFullFileName("品曲详情"));
        return excelExportDto;
        /*response.setContentType("application/vnd.ms-excel");
        String filename = URLEncoder.encode("品曲详情.xls", StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
        ServletOutputStream out = response.getOutputStream();
        try {
            byte[] in = responseEntity.getBody();
            if (in != null) {
                out.write(in);
            }
        } finally {
            out.close();
        }*/
    }

    @Override
    public IPage<ReportTasteTaskDetailDTO> getReportTasteTaskDetail(ReportTasteTaskDetailPageDTO queryDTO) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        wrapper.like("t1.inspection_scene", "制曲检验");

        if (StringUtils.isNotBlank(queryDTO.getFactory())) {
            wrapper.like("t9.attr_value", queryDTO.getFactory());
        }

        if (!ObjectUtils.isEmpty(queryDTO.getHostTimeStart()) && !ObjectUtils.isEmpty(queryDTO.getHostTimeEnd())) {
            wrapper.between("t1.host_time", queryDTO.getHostTimeStart(), queryDTO.getHostTimeEnd());
        }

        IPage<ReportTasteTaskDetailDTO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        // 查询分页数据
        page = reportMapper.getReportTasteTaskDetail(page, wrapper);

        return page;
    }

    @Override
    public IPage<LinkedHashMap<String, Object>> getReportWineSampleIndicators(WineSampleIndicatorsPageDTO queryDTO) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        wrapper.eq("B.`status`", "已接收");
        wrapper.ne("JY.inspection_status", "已废弃");
        wrapper.eq("SC.scene_type", "JY");

        if (StringUtils.isNotBlank(queryDTO.getInspectionScene())) {
            wrapper.eq("JY.scene_name", queryDTO.getInspectionScene());
        }
        if (StringUtils.isNotBlank(queryDTO.getBarcode())) {
            wrapper.like("JSD.barcode", queryDTO.getBarcode());
        }

        if (!ObjectUtils.isEmpty(queryDTO.getInspectionDateStart()) && !ObjectUtils.isEmpty(queryDTO.getInspectionDateEnd())) {
            wrapper.between("JY.inspection_end_time", queryDTO.getInspectionDateStart(), queryDTO.getInspectionDateEnd());
        }

        IPage<LinkedHashMap<String, Object>> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        // 查询分页数据
        page = reportMapper.getReportWineSampleIndicators(page, wrapper);

        return page;
    }

    @Override
    public void exportReportWineSampleIndicators(WineSampleIndicatorsPageDTO queryDTO, HttpServletRequest request, HttpServletResponse response) throws IOException, IllegalAccessException {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        wrapper.eq("B.`status`", "已接收");
        wrapper.ne("JY.inspection_status", "已废弃");
        wrapper.eq("SC.scene_type", "JY");

        if (StringUtils.isNotBlank(queryDTO.getInspectionScene())) {
            wrapper.eq("JY.scene_name", queryDTO.getInspectionScene());
        }
        if (StringUtils.isNotBlank(queryDTO.getBarcode())) {
            wrapper.like("JSD.barcode", queryDTO.getBarcode());
        }

        if (!ObjectUtils.isEmpty(queryDTO.getInspectionDateStart()) && !ObjectUtils.isEmpty(queryDTO.getInspectionDateEnd())) {
            wrapper.between("JY.inspection_end_time", queryDTO.getInspectionDateStart(), queryDTO.getInspectionDateEnd());
        }

        List<LinkedHashMap<String, Object>> list = reportMapper.getReportWineSampleIndicatorsList(wrapper);
        writeExcelData("酒样指标统计表", list, response);
        //ResponseEntity<byte[]> responseEntity = ExcelUtil.generateImportFile(list,
        //        "酒样指标统计表", Map.class);
        //ExcelExportDto excelExportDto = new ExcelExportDto();
        //excelExportDto.setBody(responseEntity.getBody());
        //excelExportDto.setFileName(FileNameUtil.createExcelFullFileName("酒样指标统计表"));
        //return excelExportDto;
        /*response.setContentType("application/vnd.ms-excel");
        String filename = URLEncoder.encode("酒样指标统计表.xls", StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
        ServletOutputStream out = response.getOutputStream();
        try {
            byte[] in = responseEntity.getBody();
            if (in != null) {
                out.write(in);
            }
        } finally {
            out.close();
        }*/

    }


    @Override
    public ExcelExportDto exportReportWineSampleIndicatorsExcel(WineSampleIndicatorsPageDTO queryDTO, HttpServletRequest request, HttpServletResponse response) throws IOException, IllegalAccessException {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        wrapper.eq("B.`status`", "已接收");
        wrapper.ne("JY.inspection_status", "已废弃");
        wrapper.eq("SC.scene_type", "JY");

        if (StringUtils.isNotBlank(queryDTO.getInspectionScene())) {
            wrapper.eq("JY.scene_name", queryDTO.getInspectionScene());
        }
        if (StringUtils.isNotBlank(queryDTO.getBarcode())) {
            wrapper.like("JSD.barcode", queryDTO.getBarcode());
        }

        if (!ObjectUtils.isEmpty(queryDTO.getInspectionDateStart()) && !ObjectUtils.isEmpty(queryDTO.getInspectionDateEnd())) {
            wrapper.between("JY.inspection_end_time", queryDTO.getInspectionDateStart(), queryDTO.getInspectionDateEnd());
        }

        List<ReportWineSampleIndicatorsExcel> list = reportMapper.getReportWineSampleIndicatorsListNew(wrapper);

        ResponseEntity<byte[]> responseEntity = ExcelUtil.generateImportFile(list,
                "酒样指标统计表", ReportWineSampleIndicatorsExcel.class);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName(FileNameUtil.createExcelFullFileName("酒样指标统计表"));
        return excelExportDto;

    }


    public void writeExcelData(String title,List<LinkedHashMap<String, Object>> list , HttpServletResponse response) throws IOException {
        //  excel 文件名定义
        String fileName = title + ".xlsx";
        // 下载接口响应内容定义
        response.setHeader("Charset", String.valueOf(StandardCharsets.UTF_8));
        response.setHeader("Content-Type", "application/octet-stream;charset=" + String.valueOf(StandardCharsets.UTF_8));
        response.setHeader("Content-Disposition", "attachment; filename="
                + URLEncoder.encode(fileName, String.valueOf(StandardCharsets.UTF_8)));

        // 正式写入excel数据
        // 第一步：先确定列名，也就是列标题，这个要和查出来的字段数量相一致,可以根据动态查询的字段动态生成
        List<List<String>> head = new ArrayList<>();
        if (list.size()>0){
             List<String> keyList = new ArrayList<>(list.get(0).keySet());
             for (String colum : keyList){
                 List<String> columList = new ArrayList(1);
                 columList.add(colum);
                 head.add(columList);
             }
        }
        // 确定数据key
        LinkedHashMap<String, Object> map = list.get(0);
        List<String> mapKey = map.keySet().stream().map(String::toString).collect(Collectors.toList());
        // 创建接收转换好数据的容器
        List<List<Object>> sheetData = new ArrayList<>();
        // 解释：map类型无法直接按照key值导入，需要转换成list类型的数据，依次导入
        for (LinkedHashMap m : list) {
            List dataList = new ArrayList(mapKey.size());
            for (String colum : mapKey) {
                // valueOf是为了防止数据类型不一致导致报错，需要特殊表格类型的自行解决
                dataList.add(String.valueOf(m.get(colum)));
            }
            sheetData.add(dataList);
        }
        // 写入数据流，设置表头，设置自适应表头宽度
        EasyExcel.write(
                 response.getOutputStream())         // 响应的数据流
                .head(head)                         // 设置列标题
                .needHead(true)                     // 表示需要生成列标题
                .sheet(title)                       // sheet页的名称，可省略
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())// 设置自适应列宽
                .doWrite(sheetData);                // 将数据写入
    }

}
