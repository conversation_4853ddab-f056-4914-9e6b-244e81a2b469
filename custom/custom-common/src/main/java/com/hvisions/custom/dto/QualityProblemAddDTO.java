package com.hvisions.custom.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class QualityProblemAddDTO implements Serializable {

    @ApiModelProperty(value = "扩展字段",hidden = true)
    private List<InspectionRegRelDetailDTOList> extensionFieldDTOList;

    @ApiModelProperty(value = "文件",hidden = true)
    private List<Attachments> attachments;

    @ApiModelProperty(value = "期望解决时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectResolveTime;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "物料CODE")
    private String materialId;

    @ApiModelProperty(value = "优先级")
    private String priority;

    @ApiModelProperty(value = "问题编码")
    private String problemCode;

    @ApiModelProperty(value = "问题发现时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date problemFindTime;

    @ApiModelProperty(value = "问题名称")
    private String problemName;

    @ApiModelProperty(value = "问题类型ID")
    private String problemTypeId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "责任部门列表")
    private List<ResponsibleDepartmentDTOList> responsibleDepartmentDTOList;

    @ApiModelProperty(value = "责任人列表")
    private List<ResponsiblePersonDTOList> responsiblePersonDTOList;

    @ApiModelProperty(value = "严重度")
    private String severity;


}
