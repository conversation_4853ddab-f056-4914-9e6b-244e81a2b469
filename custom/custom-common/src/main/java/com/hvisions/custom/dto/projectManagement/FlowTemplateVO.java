package com.hvisions.custom.dto.projectManagement;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: zhengxiong.jiang
 * @createDate: 2024/2/2
 * @version: 1.0
 */
@Data
public class FlowTemplateVO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id" )
    private Integer id;
    /**
     * 流程模板名称
     */
    @ApiModelProperty(value = "流程模板名称")
    private String templateName;
    /**
     * 状态 1新建2启动3不启用
     */
    @ApiModelProperty(value = "状态 1新建2启动3不启用" )
    private Integer state;
    /**
     * create_time
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "流程节点信息")
    private List<FlowNodeDTO> nodes;

}
