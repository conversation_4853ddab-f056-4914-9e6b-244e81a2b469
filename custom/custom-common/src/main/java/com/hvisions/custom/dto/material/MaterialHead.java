package com.hvisions.custom.dto.material;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/4/2 17:53
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class MaterialHead{

    private String materialModel;
    private String materialSpec;//物料规格
    private String companyName;//公司名称
    private String cateName;//物料分类
    private String cateCode;//物料分类编码
    private String purchaseType;//获取方式
    private String materialName;//物料名称
    private String baseUnit;//基本单位
    private String purchaseUnit;//采购单位
    private String createBy;//创建人
    private String freeze;//是否冻结
    private String materialNumber;//物料编码
    private Date createTime;//创建时间
    private String company;//公司
    private String materialDesc;//物料描述
    private String materialType;//型号
    private List<MaterialItem> itemList;

}
