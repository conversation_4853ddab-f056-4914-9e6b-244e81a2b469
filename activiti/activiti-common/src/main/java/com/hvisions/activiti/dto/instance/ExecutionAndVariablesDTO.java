package com.hvisions.activiti.dto.instance;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>Title: ExecutionAndVariablesDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ExecutionAndVariablesDTO extends ExecutionDTO implements Serializable {

    public ExecutionAndVariablesDTO() {

    }

    /**
     * 参数列表
     */
    private Map<String, Object> variables;
}
