package com.hvisions.qmspclm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 5WHY;
 */
@Getter
@Setter
@ToString
@ApiModel(description = "5WHY")
public class FiveWhyDTO extends SysBaseDTO {

    /**
     * 质量问题ID
     */
    @ApiModelProperty(value = "质量问题ID")
    @NotNull
    private Integer qualityProblemId;

    /**
     * 问题节点;D1~D8
     */
    @ApiModelProperty(value = "问题节点", notes = "D1~D8", allowableValues = "D1~D8")
    @NotBlank
    private String problemNode;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    @NotNull
    private Integer sequence;

    /**
     * 问题
     */
    @ApiModelProperty(value = "问题")
    private String why;

    /**
     * 答案
     */
    @ApiModelProperty(value = "答案")
    private String answer;

}