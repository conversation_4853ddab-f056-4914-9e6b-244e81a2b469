package com.hvisions.qmspclm.annotation;

import com.hvisions.qmspclm.enums.ProblemNodeEnum;

import javax.validation.constraints.NotNull;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 问题节点操作权限注解
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ProblemNodePermission {
    /**
     * 问题节点
     *
     * @return 问题节点
     */
    @NotNull
    ProblemNodeEnum problemNodeEnum();

    /**
     * 各模块操作
     *
     * @return 各模块操作
     */
    String operate() default "";
}