package com.hvisions.qmspclm.service;

import com.hvisions.qmspclm.dto.ProblemDescriptionDTO;

/**
 * D2问题描述
 */
public interface ProblemDescService {
    /**
     * 保存问题描述
     *
     * @param problemDescriptionDTO 问题描述
     */
    void save(ProblemDescriptionDTO problemDescriptionDTO);

    /**
     * 查询问题描述
     *
     * @param qualityProblemId 质量问题ID
     * @return 问题描述
     */
    ProblemDescriptionDTO getProblemDesc(Integer qualityProblemId);
}
