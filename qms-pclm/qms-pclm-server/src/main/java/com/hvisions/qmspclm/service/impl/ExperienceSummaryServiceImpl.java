package com.hvisions.qmspclm.service.impl;

import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.qmspclm.dto.ExperienceSummaryDTO;
import com.hvisions.qmspclm.entity.ExperienceSummary;
import com.hvisions.qmspclm.repository.ExperienceSummaryRepository;
import com.hvisions.qmspclm.service.ExperienceSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class ExperienceSummaryServiceImpl implements ExperienceSummaryService {
    @Autowired
    private ExperienceSummaryRepository experienceSummaryRepository;

    @Override
    public void save(ExperienceSummaryDTO experienceSummaryDTO, UserInfoDTO userInfo) {
        experienceSummaryDTO.setOperatorId(userInfo.getId());
        experienceSummaryDTO.setOperatorName(userInfo.getUserName());
        experienceSummaryDTO.setOperateTime(new Date());
        ExperienceSummary experienceSummary = DtoMapper.convert(experienceSummaryDTO, ExperienceSummary.class);
        experienceSummaryRepository.save(experienceSummary);
    }

    @Override
    public ExperienceSummaryDTO getByQualityProblemId(Integer qualityProblemId) {
        ExperienceSummary experienceSummary = experienceSummaryRepository.findByQualityProblemId(qualityProblemId);
        return DtoMapper.convert(experienceSummary, ExperienceSummaryDTO.class);
    }
}
