package com.hvisions.qmspclm.service;

import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.qmspclm.dto.ApprovalRecordDTO;
import com.hvisions.qmspclm.dto.QualityProblemTaskDTO;
import com.hvisions.qmspclm.dto.flowchart.Node;

import java.util.List;

/**
 * 质量问题任务
 */
public interface QualityProblemTaskService {
    /**
     * 创建问题任务
     *
     * @param problemId 问题ID
     * @param node      流程节点
     */
    void create(Integer problemId, Node node);

    /**
     * 保存问题任务
     *
     * @param qualityProblemTaskDTO 质量问题任务
     */
    void save(QualityProblemTaskDTO qualityProblemTaskDTO);

    /**
     * 查询质量问题任务
     *
     * @param taskId 任务ID
     * @return 质量问题任务
     */
    QualityProblemTaskDTO getById(Integer taskId);

    /**
     * 查询最新的质量问题任务
     *
     * @param qualityProblemId 质量问题ID
     * @return 质量问题任务
     */
    QualityProblemTaskDTO getLatestTask(Integer qualityProblemId);

    /**
     * 查询质量问题任务列表
     *
     * @param qualityProblemId 质量问题ID
     * @return 质量问题任务列表
     */
    List<QualityProblemTaskDTO> listTask(Integer qualityProblemId);

    /**
     * 任务完成
     *
     * @param taskId     任务ID
     * @param userInfo   用户信息
     * @param backupData 备份数据
     */
    void completeTask(Integer taskId, UserInfoDTO userInfo, String backupData);

    /**
     * 添加审批任务记录
     *
     * @param approvalRecordDTO 审批记录
     */
    void addApprovalRecord(ApprovalRecordDTO approvalRecordDTO);

    /**
     * 获取审批记录列表
     *
     * @param taskId 任务ID
     * @return 审批记录列表
     */
    List<ApprovalRecordDTO> listApprovalRecord(Integer taskId);

    /**
     * 根据质量问题ID，删除任务
     *
     * @param qualityProblemId 质量问题ID
     */
    void deleteByQualityProblemId(Integer qualityProblemId);

    /**
     * 审批通过
     *
     * @param taskId 任务ID
     */
    void approve(Integer taskId);

    /**
     * 驳回
     *
     * @param taskId 任务ID
     */
    void reject(Integer taskId);

    /**
     * 关闭任务
     *
     * @param taskId 任务ID
     */
    void close(Integer taskId);

}
