package com.hvisions.qmspclm.service.impl;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.qmspclm.dto.FlowchartDTO;
import com.hvisions.qmspclm.dto.flowchart.Node;
import com.hvisions.qmspclm.dto.flowchart.TerminalData;
import com.hvisions.qmspclm.entity.Flowchart;
import com.hvisions.qmspclm.enums.ApprovalResultEnum;
import com.hvisions.qmspclm.enums.NodeTypeEnum;
import com.hvisions.qmspclm.enums.ProblemNodeEnum;
import com.hvisions.qmspclm.repository.FlowchartRepository;
import com.hvisions.qmspclm.service.FlowchartService;
import com.hvisions.qmspclm.service.ProblemTypeNodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.service.spi.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FlowchartServiceImpl implements FlowchartService {
    @Autowired
    private FlowchartRepository flowchartRepository;

    @Autowired
    private ProblemTypeNodeService problemTypeNodeService;

    @Override
    public void save(FlowchartDTO flowchartDTO) {
        Flowchart flowchart = DtoMapper.convert(flowchartDTO, Flowchart.class);
        // 保存
        if (Objects.isNull(flowchart.getId())) {
            FlowchartDTO latestFlowchart = findLatestVersion(flowchart.getProblemTypeId());
            int version = latestFlowchart == null ? 1 : latestFlowchart.getVersion() + 1;
            flowchart.setVersion(version);
            flowchart.setPublish(false);
            flowchart.setUsed(false);
            flowchartRepository.save(flowchart);
            return;
        }

        // 修改
        Flowchart original = flowchartRepository.findById(flowchart.getId())
                .orElseThrow(() -> new BaseKnownException("流程图不存在"));
        if (Boolean.TRUE.equals(original.getPublish())) {
            throw new ServiceException("已发布的流程图不允许修改");
        }

        if (BooleanUtils.isTrue(original.getUsed())) {
            throw new ServiceException("已使用的流程图不允许修改");
        }

        original.setCells(flowchartDTO.getCells());
        flowchartRepository.save(original);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(Integer flowchartId) {
        Flowchart flowchart = flowchartRepository.findById(flowchartId)
                .orElseThrow(() -> new BaseKnownException("流程图不存在"));
        if (Boolean.TRUE.equals(flowchart.getPublish())) {
            throw new BaseKnownException("该流程图已发布");
        }

        // 复制一份新的流程图
        FlowchartDTO flowchartDTO = DtoMapper.convert(flowchart, FlowchartDTO.class);
        check(flowchartDTO);
        flowchartDTO.setId(null);
        save(flowchartDTO);

        // 发布该流程
        publish(flowchart);
        // 保存该问题类型对应的问题节点
        List<String> problemNodeList = flowchart.getCells()
                .stream()
                .map(Node::getType)
                .filter(ProblemNodeEnum::isProblemNode)
                .collect(Collectors.toList());
        problemTypeNodeService.save(flowchart.getProblemTypeId(), problemNodeList);
    }

    private void publish(Flowchart currentFlowchart) {
        List<Flowchart> flowchartList = flowchartRepository.findAllByProblemTypeId(currentFlowchart.getProblemTypeId());
        for (Flowchart flowchart : flowchartList) {
            // 流程版本发布的时候，将其他发布的版本改为未发布
            flowchart.setPublish(Objects.equals(flowchart.getId(), currentFlowchart.getId()));
        }
        flowchartRepository.saveAll(flowchartList);
    }

    @Override
    public void deleteByProblemTypeId(Integer problemTypeId) {
        flowchartRepository.deleteAllByProblemTypeId(problemTypeId);
    }

    @Override
    public void copy(Integer originalProblemTypeId, Integer newProblemTypeId) {
        List<Flowchart> flowchartList = flowchartRepository.findAllByProblemTypeId(originalProblemTypeId);
        if (CollectionUtils.isEmpty(flowchartList)) {
            return;
        }

        List<FlowchartDTO> flowchartDTOList = DtoMapper.convertList(flowchartList, FlowchartDTO.class);
        flowchartDTOList.forEach(flowchartDTO -> {
            flowchartDTO.setProblemTypeId(newProblemTypeId);
            flowchartDTO.setId(null);
        });
        flowchartRepository.saveAll(DtoMapper.convertList(flowchartDTOList, Flowchart.class));
        problemTypeNodeService.copy(originalProblemTypeId, newProblemTypeId);
    }

    @Override
    public FlowchartDTO findById(Integer flowchartId) {
        Flowchart flowchart = flowchartRepository.findById(flowchartId).orElse(null);
        return DtoMapper.convert(flowchart, FlowchartDTO.class);
    }

    @Override
    public FlowchartDTO findLatestVersion(Integer problemTypeId) {
        Flowchart flowchart = flowchartRepository.findFirstByProblemTypeIdOrderByVersionDesc(problemTypeId);
        return DtoMapper.convert(flowchart, FlowchartDTO.class);
    }

    @Override
    public FlowchartDTO findLatestPublish(Integer problemTypeId) {
        Flowchart flowchart = flowchartRepository.findFirstByProblemTypeIdAndPublishOrderByVersionDesc(problemTypeId, true);
        return DtoMapper.convert(flowchart, FlowchartDTO.class);
    }

    @Override
    public List<Integer> findVersions(Integer problemTypeId) {
        List<Flowchart> flowchartList = flowchartRepository.findAllByProblemTypeId(problemTypeId);
        if (CollectionUtils.isEmpty(flowchartList)) {
            return Collections.emptyList();
        }

        return flowchartList.stream().map(Flowchart::getVersion).collect(Collectors.toList());
    }

    @Override
    public FlowchartDTO findFlowchart(Integer problemTypeId, Integer version) {
        Flowchart flowchart = flowchartRepository.findByProblemTypeIdAndVersion(problemTypeId, version);
        return DtoMapper.convert(flowchart, FlowchartDTO.class);
    }

    @Override
    public void check(FlowchartDTO flowchartDTO) {
        checkRequired(flowchartDTO.getCells());
        checkUnique(flowchartDTO.getCells());
        checkEdge(flowchartDTO);
        checkNodeSequence(flowchartDTO);
    }

    @Override
    public void setUsed(Integer id) {
        flowchartRepository.findById(id)
                .ifPresent(flowchart -> {
                    flowchart.setUsed(true);
                    flowchartRepository.save(flowchart);
                    log.info("flowchart: {} set used is true.", id);
                });
    }

    private void checkRequired(List<Node> cells) {
        List<NodeTypeEnum> requiredNodeList = Arrays.stream(NodeTypeEnum.values())
                .filter(NodeTypeEnum::getRequired)
                .collect(Collectors.toList());
        for (NodeTypeEnum nodeTypeEnum : requiredNodeList) {
            long count = cells.stream()
                    .filter(node -> StringUtils.equals(nodeTypeEnum.getCode(), node.getType()))
                    .count();
            if (count < 1) {
                throw new BaseKnownException(String.format("流程图中缺少%s节点", nodeTypeEnum.getCode()));
            }
        }
    }

    private void checkUnique(List<Node> cells) {
        List<NodeTypeEnum> uniqueNodeList = Arrays.stream(NodeTypeEnum.values())
                .filter(NodeTypeEnum::getUnique)
                .collect(Collectors.toList());
        for (NodeTypeEnum nodeTypeEnum : uniqueNodeList) {
            long count = cells.stream()
                    .filter(node -> StringUtils.equals(nodeTypeEnum.getCode(), node.getType()))
                    .count();
            if (count > 1) {
                throw new BaseKnownException(String.format("流程图中%s节点只能有一个", nodeTypeEnum.getCode()));
            }
        }
    }

    private void checkEdge(FlowchartDTO flowchartDTO) {
        for (Node node : flowchartDTO.getCells()) {
            checkEdge(flowchartDTO, node);
        }
    }

    private void checkEdge(FlowchartDTO flowchartDTO, Node node) {
        if ("edge".equals(node.getShape()) || NodeTypeEnum.END.getCode().equals(node.getType())) {
            return;
        }

        List<Node> edgeNodeList = flowchartDTO.getCells()
                .stream()
                .filter(itemNode ->
                        StringUtils.equals(node.getId(), Optional.ofNullable(itemNode.getSource()).orElseGet(TerminalData::new).getCell())
                ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(edgeNodeList)) {
            throw new BaseKnownException(String.format("%s节点没有连线", node.getName()));
        }

        if (NodeTypeEnum.APPROVAL.getCode().equals(node.getType())) {
            checkApprovalEdge(node, edgeNodeList);
            return;
        }

        if (edgeNodeList.size() != 1) {
            throw new BaseKnownException(String.format("%s节点连线异常", node.getName()));
        }
    }

    private void checkApprovalEdge(Node node, List<Node> edgeNodeList) {
        if (edgeNodeList.size() != 2) {
            throw new BaseKnownException(String.format("%s节点连线数量异常", node.getName()));
        }

        for (ApprovalResultEnum approvalResultEnum : ApprovalResultEnum.values()) {
            long count = edgeNodeList.stream()
                    .filter(edgNode -> approvalResultEnum.getCode().equals(edgNode.getLabels()))
                    .count();
            if (count != 1) {
                throw new BaseKnownException(String.format("%s节点连线的标签内容异常", node.getName()));
            }
        }
    }

    private void checkNodeSequence(FlowchartDTO flowchartDTO) {
        List<String> nodeCodeList = new ArrayList<>();
        Node node = flowchartDTO.findFirstNode();
        while (!NodeTypeEnum.END.getCode().equals(node.getType())) {
            if (NodeTypeEnum.APPROVAL.getCode().equals(node.getType())) {
                node = flowchartDTO.findNextNode(node.getId(), ApprovalResultEnum.APPROVE);
            } else {
                if (nodeCodeList.contains(node.getType())) {
                    throw new BaseKnownException("流程图线路异常");
                }
                nodeCodeList.add(node.getType());
                node = flowchartDTO.findNextNode(node.getId());
            }
        }
        List<String> sortedList = nodeCodeList.stream()
                .sorted()
                .collect(Collectors.toList());
        if (!ListUtils.isEqualList(nodeCodeList, sortedList)) {
            throw new BaseKnownException("流程图中节点顺序必须是从D1到D8");
        }

        List<String> allProblemNodeList = flowchartDTO.findAllProblemNode()
                .stream()
                .sorted()
                .collect(Collectors.toList());
        if (!ListUtils.isEqualList(allProblemNodeList, sortedList)) {
            throw new BaseKnownException("流程图异常");
        }
    }

}
