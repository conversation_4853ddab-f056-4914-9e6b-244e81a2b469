package com.hvisions.qmspclm.configuration;

import com.hvisions.common.config.coderule.enums.CodeRuleSerializeEnum;
import com.hvisions.common.config.coderule.enums.ExpireUnitEnum;
import com.hvisions.common.config.coderule.serialize.DateRuleValue;
import com.hvisions.common.config.coderule.serialize.KvRuleValue;
import com.hvisions.common.config.coderule.serialize.SerialNumRuleValue;
import com.hvisions.common.dto.CodeRuleDetailDto;
import com.hvisions.common.dto.CodeRuleDto;
import com.hvisions.common.dto.FullCodeRuleDto;
import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.framework.client.CodeRuleClient;
import com.hvisions.qmspclm.constant.CodeRuleConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 设置质检编码规则
 */
@Component
@Slf4j
public class CodeRuleConfigurationRunner extends SafetyCommandLineRunner {
    @Autowired
    private CodeRuleClient codeRuleClient;

    @Override
    public void callRunner(String... args) {
        log.info("qms-plcm module code rule configuration start.");
        FullCodeRuleDto fullCodeRuleDto = new FullCodeRuleDto();
        fullCodeRuleDto.setStorageName("qms-plcm");
        // 1: 系统规则; 2: 自定义规则
        fullCodeRuleDto.setStorageType(1);
        List<CodeRuleDto> codeRuleDtoList = new ArrayList<>();
        codeRuleDtoList.add(buildCodeRule(CodeRuleConstant.QUALITY_PROBLEM_CODE, "质量问题编码", "PCLM"));
        codeRuleDtoList.add(buildProblemTypeCodeRule());
        fullCodeRuleDto.setCodeRuleDtoList(codeRuleDtoList);
        codeRuleClient.saveFullCodeRule(fullCodeRuleDto);
        log.info("qms-plcm module code rule configuration end.");
    }

    /**
     * 质检模块编码规则
     *
     * @param ruleCode 规则编码
     * @param ruleName 规则名称
     * @return 编码规则
     */
    private CodeRuleDto buildCodeRule(String ruleCode, String ruleName, String codePrefix) {
        CodeRuleDto codeRuleDto = new CodeRuleDto();
        codeRuleDto.setRuleCode(ruleCode);
        codeRuleDto.setRuleName(ruleName);
        codeRuleDto.setUsed(true);
        codeRuleDto.setCodeRuleDetailDtos(buildDefaultCodeRuleDetail(codePrefix));
        return codeRuleDto;
    }

    /**
     * 设置编码规则段
     *
     * @param codePrefix 编码前缀
     * @return 编码规则段列表
     */
    private List<CodeRuleDetailDto> buildDefaultCodeRuleDetail(String codePrefix) {
        List<CodeRuleDetailDto> detailDtoList = new ArrayList<>();
        // 1.常量
        CodeRuleDetailDto constDetail = buildConstCodeRuleDetail(codePrefix);
        // 2.日期
        CodeRuleDetailDto dateDetail = buildDateCodeRuleDetail("yyyyMMdd");
        // 3.流水号
        CodeRuleDetailDto serialDetail = buildSerialCodeRuleDetail();
        detailDtoList.add(constDetail);
        detailDtoList.add(dateDetail);
        detailDtoList.add(serialDetail);
        return detailDtoList;
    }

    /**
     * 构建常量编码规则明细
     *
     * @param constChar 常量字符
     * @return 常量编码规则明细
     */
    private CodeRuleDetailDto buildConstCodeRuleDetail(String constChar) {
        CodeRuleDetailDto constDetail = new CodeRuleDetailDto();
        constDetail.setRuleType(CodeRuleSerializeEnum.CONSTANT.getType());
        KvRuleValue kvRuleValue = new KvRuleValue();
        kvRuleValue.setValue(constChar);
        constDetail.setRuleValue(kvRuleValue);
        return constDetail;
    }

    /**
     * 构建日期编码规则明细
     *
     * @param dateFormat 日期格式
     * @return 日期编码规则明细
     */
    private CodeRuleDetailDto buildDateCodeRuleDetail(String dateFormat) {
        CodeRuleDetailDto dateDetail = new CodeRuleDetailDto();
        dateDetail.setRuleType(CodeRuleSerializeEnum.DATE.getType());
        DateRuleValue dateRuleValue = new DateRuleValue();
        dateRuleValue.setFormat(dateFormat);
        dateDetail.setRuleValue(dateRuleValue);
        return dateDetail;
    }

    /**
     * 构建默认流水号编码规则明细
     * 步长: 1,重置方式: 日,初始值: 1,是否补齐: 是,补齐字符:0,长度: 4
     *
     * @return 流水号编码规则明细
     */
    private CodeRuleDetailDto buildSerialCodeRuleDetail() {
        CodeRuleDetailDto serialDetail = new CodeRuleDetailDto();
        serialDetail.setRuleType(CodeRuleSerializeEnum.SERIAL_NUMBER.getType());
        SerialNumRuleValue serialRuleValue = new SerialNumRuleValue();
        serialRuleValue.setFirstValue(1);
        serialRuleValue.setStep(1);
        serialRuleValue.setResetType(ExpireUnitEnum.DAY.getCode());
        serialRuleValue.setFill(1);
        serialRuleValue.setFillChar("0");
        serialRuleValue.setLength(4);
        serialDetail.setRuleValue(serialRuleValue);
        return serialDetail;
    }

    /**
     * 构建问题类型编码规则
     *
     * @return 问题类型编码规则
     */
    private CodeRuleDto buildProblemTypeCodeRule() {
        CodeRuleDto codeRuleDto = new CodeRuleDto();
        codeRuleDto.setRuleCode(CodeRuleConstant.PROBLEM_TYPE_CODE);
        codeRuleDto.setRuleName("问题类型编码");
        codeRuleDto.setUsed(true);
        List<CodeRuleDetailDto> detailDtoList = new ArrayList<>();
        // 常量
        CodeRuleDetailDto constDetail = buildConstCodeRuleDetail("QT");
        // 流水号
        CodeRuleDetailDto serialDetail = new CodeRuleDetailDto();
        serialDetail.setRuleType(CodeRuleSerializeEnum.SERIAL_NUMBER.getType());
        SerialNumRuleValue serialRuleValue = new SerialNumRuleValue();
        serialRuleValue.setFirstValue(1);
        serialRuleValue.setStep(1);
        serialRuleValue.setResetType(ExpireUnitEnum.NEVER.getCode());
        serialRuleValue.setFill(1);
        serialRuleValue.setFillChar("0");
        serialRuleValue.setLength(5);
        serialDetail.setRuleValue(serialRuleValue);
        detailDtoList.add(constDetail);
        detailDtoList.add(serialDetail);
        codeRuleDto.setCodeRuleDetailDtos(detailDtoList);
        return codeRuleDto;
    }
}
