package com.hvisions.qmspclm.repository;

import com.hvisions.qmspclm.entity.SurveyTask;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * 调查任务Repository
 */
public interface SurveyTaskRepository extends JpaRepository<SurveyTask, Integer> {
    /**
     * 查询调查任务列表
     *
     * @param relationId 关联ID
     * @param toolType   根因分析工具
     * @return 调查任务列表
     */
    List<SurveyTask> findAllByRelationIdAndToolType(Integer relationId, String toolType);
}
