package com.hvisions.qmspclm.service;

import com.hvisions.qmspclm.dto.cartogram.DateQuery;
import com.hvisions.qmspclm.dto.cartogram.DepartmentRatioDTO;
import com.hvisions.qmspclm.dto.cartogram.LimitQuery;
import com.hvisions.qmspclm.dto.cartogram.ProblemDTO;
import com.hvisions.qmspclm.dto.cartogram.ProblemDurationDTO;
import com.hvisions.qmspclm.dto.cartogram.ProblemGrowthTrendDTO;
import com.hvisions.qmspclm.dto.cartogram.ProblemTypeRatioDTO;
import com.hvisions.qmspclm.dto.cartogram.UserTaskDTO;

import java.util.List;

/**
 * 统计图服务
 *
 * <AUTHOR>
 */
public interface CartogramService {
    /**
     * 问题类型分布
     *
     * @param query 查询条件
     * @return 问题类型分布列表
     */
    List<ProblemTypeRatioDTO> listProblemTypeRatio(DateQuery query);

    /**
     * 责任部门占比
     *
     * @param query 查询条件
     * @return 责任部门占比
     */
    List<DepartmentRatioDTO> listDepartmentRatio(DateQuery query);

    /**
     * 问题处理时长
     *
     * @param query 查询条件
     * @return 问题处理时长
     */
    List<ProblemDurationDTO> listProblemDuration(LimitQuery query);

    /**
     * 日问题增长趋势
     *
     * @param query 查询条件
     * @return 日问题增长趋势
     */
    List<ProblemGrowthTrendDTO> listProblemGrowthTrend(DateQuery query);

    /**
     * 人员任务统计
     *
     * @param query 查询条件
     * @return 人员任务统计
     */
    List<UserTaskDTO> listUserTask(DateQuery query);

    /**
     * 逾期还未处理问题清单
     *
     * @param userId 用户ID
     * @return 逾期还未处理问题清单
     */
    List<ProblemDTO> listOverdueProblem(Integer userId);

    /**
     * 待处理问题清单
     *
     * @param userId 用户ID
     * @return 待处理问题清单
     */
    List<ProblemDTO> listPendingProblem(Integer userId);

}
