package com.hvisions.cpg.file.info.controller;


import com.hvisions.cpg.file.common.fileinfo.FileInfoDTO;
import com.hvisions.cpg.file.common.fileinfo.FileInfoResultDTO;
import com.hvisions.cpg.file.dto.FileQueryDTO;
import com.hvisions.cpg.file.info.service.FileInfoService;
import com.hvisions.cpg.file.info.service.FileStateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 文件基本信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Api(tags = "文件基本信息")
@RestController
@RequestMapping("/fileInfo")
public class FileInfoController {

    @Autowired
    private FileInfoService fileInfoService;
    @Autowired
    private FileStateService fileStateService;


    @PostMapping("/addFileInfo")
    @ApiOperation(value = "添加文件基本信息")
    public FileInfoDTO addFileInfo(@RequestBody FileInfoDTO fileInfo) {
        return fileInfoService.addFileInfo(fileInfo);
    }

    /**
     * 修改草稿后重新发起审批
     *
     * @param fileId fileId
     */
    @PostMapping("/requestAgain/{fileId}")
    @ApiOperation(value = "修改草稿后重新发起审批")
    public void requestAgain(@PathVariable Integer fileId) {
        fileInfoService.requestAgain(fileId);
    }

    @PutMapping("/updateFileInfo")
    @ApiOperation(value = "修改文件基本信息")
    public FileInfoDTO updateFileMessage(@RequestBody FileInfoDTO fileInfo) {
        return fileInfoService.updateFileMessage(fileInfo);
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "获取文件详情")
    public FileInfoDTO getDetail(@RequestParam Integer fileInfoId) {
        return fileInfoService.getDetail(fileInfoId);
    }


    @PostMapping("/listFile")
    @ApiOperation(value = "查询文件")
    public Page<FileInfoResultDTO> listFile(@RequestBody FileQueryDTO queryDTO) {
        return fileInfoService.listFile(queryDTO);
    }


    @PostMapping("/changeToUnUse")
    @ApiOperation(value = "受控文件废止")
    public void changeToUnUse(@RequestParam Integer fileId) {
        fileStateService.changeToUnUse(fileId);
    }

    @PostMapping("/deleteFile")
    @ApiOperation(value = "草稿或者废止文件删除")
    public void deleteFile(@RequestParam Integer fileId) {
        fileStateService.changeToDelete(fileId);
    }

    /**
     * 批量删除文件
     * @param fileIds
     */
    @PostMapping("/deleteFileList")
    @ApiOperation(value = "批量草稿或者废止文件删除")
    public void deleteFileList(@RequestBody List<Integer> fileIds) {
        // 判断fileId是否为空
        if (fileIds == null || fileIds.isEmpty()) {
            return;
        }
        fileIds.forEach(fileId -> {
            fileStateService.changeToDelete(fileId);
        });
    }

    @PostMapping("/changeToActive")
    @ApiOperation(value = "废止文件重新发布")
    public void changeToActive(@RequestParam Integer fileId) {
        fileStateService.changeToActive(fileId);
    }

    @PostMapping("/changeToOrigin")
    @ApiOperation(value = "删除状态恢复为未删除状态")
    public void changeToOrigin(@RequestParam Integer fileId) {
        fileStateService.changeToOrigin(fileId);
    }

    /**
     * 检查是否需要下载审批
     *
     * @param fileInfoId 文件id
     * @return 是否需要审批
     */
    @PostMapping("/checkFileDownloadPermission")
    @ApiOperation(value = "检查是否需要下载审批")
    public boolean checkFileDownloadPermission(@RequestParam Integer fileInfoId) {
        return fileInfoService.checkFileDownloadPermission(fileInfoId);
    }

    @PostMapping("/raiseDownloadValidation")
    @ApiOperation(value = "提出下载申请")
    public void raiseDownloadValidation(@RequestParam Integer fileInfoId, @RequestParam String comment) {
        fileInfoService.raiseDownloadValidation(fileInfoId, comment);
    }

    // 下载文件添加水印
    @ApiOperation(value = "下载文件添加水印")
    @GetMapping("/downloadAddWatermark/{fileInfoId}")
    public void downloadAddWatermark(HttpServletRequest request, HttpServletResponse response, @PathVariable Integer fileInfoId) {
        fileInfoService.downloadAddWatermark(request, response, fileInfoId);
    }

    @ApiOperation(value = "批量下载文件添加水印并打包成ZIP")
    @PostMapping("/downloadAddWatermarks")
    public void downloadAddWatermarks(HttpServletRequest request, HttpServletResponse response, @RequestBody List<Integer> fileIds) {
        fileInfoService.downloadAddWatermarksAsZip(request, response, fileIds);
    }


}

