package com.hvisions.cpg.file.info.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.context.UserContext;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.cpg.file.common.CommonConstants;
import com.hvisions.cpg.file.common.FileStatesConstants;
import com.hvisions.cpg.file.common.fileinfo.FileInfoDTO;
import com.hvisions.cpg.file.common.fileinfo.FileInfoResultDTO;
import com.hvisions.cpg.file.common.history.FileHistoryDTO;
import com.hvisions.cpg.file.common.tag.FileTagDTO;
import com.hvisions.cpg.file.common.type.FileTypeQueryDTO;
import com.hvisions.cpg.file.common.type.FileTypeTreeQueryDTO;
import com.hvisions.cpg.file.dto.FileQueryDTO;
import com.hvisions.cpg.file.history.service.FileHistoryService;
import com.hvisions.cpg.file.info.entity.FileInfo;
import com.hvisions.cpg.file.info.mapper.FileInfoMapper;
import com.hvisions.cpg.file.info.service.FileInfoService;
import com.hvisions.cpg.file.tag.entity.FileRelevancyTag;
import com.hvisions.cpg.file.tag.entity.FileTag;
import com.hvisions.cpg.file.tag.mapper.FileRelevancyTagMapper;
import com.hvisions.cpg.file.tag.mapper.FileTagMapper;
import com.hvisions.cpg.file.tag.service.FileRelevancyTagService;
import com.hvisions.cpg.file.type.dto.ValidSetting;
import com.hvisions.cpg.file.type.dto.ValidUserInfo;
import com.hvisions.cpg.file.type.entity.FileType;
import com.hvisions.cpg.file.type.service.FileTypeService;
import com.hvisions.cpg.file.utils.WatermarkUtils;
import com.hvisions.cpg.file.validate.dto.RequestDTO;
import com.hvisions.cpg.file.validate.service.ValidationService;
import com.hvisions.framework.client.DepartmentClient;
import com.hvisions.framework.client.FileClient;
import com.hvisions.framework.client.MessageClient;
import com.hvisions.framework.dto.file.FileDTO;
import com.hvisions.framework.dto.file.FileExcelExportDto;
import com.hvisions.framework.dto.message.MessageCreateDTO;
import com.hvisions.framework.dto.user.DepartmentDTO;
import com.hvisions.framework.utils.StringUtils;
import jodd.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * 文件基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Slf4j
@Service
public class FileInfoServiceImpl extends ServiceImpl<FileInfoMapper, FileInfo> implements FileInfoService {


    private final FileClient fileClient;
    private final FileTypeService fileTypeService;
    private final FileRelevancyTagService fileRelevancyTagService;
    private final FileHistoryService fileHistoryService;
    private final ValidationService validationService;
    private final UserContext userContext;
    private final DepartmentClient departmentClient;
    private final FileRelevancyTagMapper tagMapper;

    private final FileTagMapper fileTagMapper;
    private final MessageClient messageClient;

    @Autowired
    public FileInfoServiceImpl(FileClient fileClient,
                               FileTypeService fileTypeService,
                               FileRelevancyTagService fileRelevancyTagService,
                               FileHistoryService fileHistoryService,
                               ValidationService validationService,
                               UserContext userContext,
                               DepartmentClient departmentClient,
                               FileRelevancyTagMapper tagMapper,
                               FileTagMapper fileTagMapper,
                               MessageClient messageClient) {
        this.fileClient = fileClient;
        this.fileTypeService = fileTypeService;
        this.fileRelevancyTagService = fileRelevancyTagService;
        this.fileHistoryService = fileHistoryService;
        this.validationService = validationService;
        this.userContext = userContext;
        this.departmentClient = departmentClient;
        this.tagMapper = tagMapper;
        this.fileTagMapper = fileTagMapper;
        this.messageClient = messageClient;
    }

    /**
     * 添加文件基本信息
     *
     * @param fileInfoDTO 入参对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized FileInfoDTO addFileInfo(FileInfoDTO fileInfoDTO) {
        Optional.ofNullable(fileInfoDTO.getFileNo()).orElseThrow(() -> new BaseKnownException(0, "文件编号不能为空！"));
        Optional.ofNullable(fileInfoDTO.getFileTypeId()).orElseThrow(() -> new BaseKnownException(0, "请选择文件类型！"));
        Optional.ofNullable(fileInfoDTO.getFileVersion()).orElseThrow(() -> new BaseKnownException(0, "文件版本号不能为空！"));

        FileType fileType = fileTypeService.getById(fileInfoDTO.getFileTypeId());
        Optional.ofNullable(fileType).orElseThrow(() -> new BaseKnownException(0, "文件类型不存在！"));
        //设置作者id信息
        if (fileInfoDTO.getFileAuthorIds() == null) {
            fileInfoDTO.setFileAuthorIds(Collections.emptyList());
        } else {
            fileInfoDTO.setFileAuthors(fileInfoDTO.getFileAuthorIds().stream()
                    .map(Object::toString).collect(Collectors.joining(",")));
        }
        FileInfo entity = DtoMapper.convert(fileInfoDTO, FileInfo.class);
        entity.setWarnMessageSend(0);
        entity.setOverTimeMessageSend(0);
        entity.setFileAuthors(fileInfoDTO.getFileAuthorIds().stream()
                .map(Object::toString).collect(Collectors.joining(",")));
        // 文件版本号唯一性校验
        List<FileInfo> fileInfoByVisions = this.lambdaQuery()
                .eq(FileInfo::getDelFlag, CommonConstants.UNDELETED_FLAG)
                .eq(FileInfo::getFileNo, fileInfoDTO.getFileNo())
                .eq(FileInfo::getFileName, fileInfoDTO.getFileName())
                .eq(FileInfo::getFileVersion, fileInfoDTO.getFileVersion()).list();
        if (CollUtil.isNotEmpty(fileInfoByVisions)) {
            throw new BaseKnownException(0, "该文件版本号重复！");
        }
        //看看需要审批不
        List<ValidSetting> setting = fileTypeService.getRootSetting(entity.getFileTypeId(), CommonConstants.VALID_TYPE);

        // 不审批直接生效
        if (setting != null && setting.size() > 0) {
            // 待审批
            entity.setFileStates(FileStatesConstants.PENDING_APPROVAL);
            //保存数据
            this.saveOrUpdate(entity);
            startValidate(entity, setting, "新增审批");
        } else {
            // 生效
            entity.setFileStates(FileStatesConstants.COMMENCEMENT);
            //保存数据
            this.saveOrUpdate(entity);
        }

        //关联标签
        if (CollectionUtil.isNotEmpty(fileInfoDTO.getFileTagId())) {
            associatedTags(entity, fileInfoDTO.getFileTagId());
        }
        FileHistoryDTO fileHistory = new FileHistoryDTO();
        fileHistory.setFileId(entity.getId());
        fileHistory.setOperationType("文件新增");
        fileHistoryService.addFileHistory(fileHistory);
        return fileInfoDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void requestAgain(Integer fileId) {
        FileInfo entity = baseMapper.selectById(fileId);
        if (!entity.getFileStates().equals(FileStatesConstants.DRAFT)) {
            throw new BaseKnownException("只能允许草稿文件重新提交");
        }
        //看看需要审批不
        List<ValidSetting> setting = fileTypeService.getRootSetting(entity.getFileTypeId(), CommonConstants.VALID_TYPE);
        if (setting != null) {
            entity.setFileStates(FileStatesConstants.PENDING_APPROVAL);
            this.saveOrUpdate(entity);
            startValidate(entity, setting, "新增审批");
        }
    }

    /**
     * 修改文件基本信息
     *
     * @param fileInfoUpdateDTO 修改文件参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized FileInfoDTO updateFileMessage(FileInfoDTO fileInfoUpdateDTO) {
        Optional.ofNullable(fileInfoUpdateDTO.getId()).orElseThrow(() -> new BaseKnownException(0, "文件ID不能为空！"));
        FileInfo entity = this.getById(fileInfoUpdateDTO.getId());
        if (entity == null) {
            throw new BaseKnownException("文件id错误");
        }
        // 修改标签
        if (fileInfoUpdateDTO.getFileTagId() == null) {
            fileInfoUpdateDTO.setFileTagId(Collections.emptyList());
        }
        associatedTags(entity, fileInfoUpdateDTO.getFileTagId());
        if (fileInfoUpdateDTO.getFileAuthorIds() == null) {
            fileInfoUpdateDTO.setFileAuthorIds(Collections.emptyList());
        }
        entity.setFileVersion(fileInfoUpdateDTO.getFileVersion());
        entity.setFileName(fileInfoUpdateDTO.getFileName());
        entity.setFileNo(fileInfoUpdateDTO.getFileNo());
        entity.setFileAuthors(fileInfoUpdateDTO.getFileAuthorIds().stream()
                .map(Object::toString).collect(Collectors.joining(",")));
        entity.setFileDescription(fileInfoUpdateDTO.getFileDescription());
        entity.setOverdueDate(fileInfoUpdateDTO.getOverdueDate());
        entity.setWarnMessageSend(0);
        entity.setWarnMessageSend(0);
        // 文件编号唯一性校验
        List<FileInfo> fileInfoByCodes = this.lambdaQuery()
                .eq(FileInfo::getDelFlag, CommonConstants.UNDELETED_FLAG)
                .eq(FileInfo::getFileName, fileInfoUpdateDTO.getFileName())
                .ne(FileInfo::getId, fileInfoUpdateDTO.getId())
                .eq(FileInfo::getFileNo, fileInfoUpdateDTO.getFileNo()).list();
        if (CollectionUtil.isNotEmpty(fileInfoByCodes)) {
            throw new BaseKnownException(0, "该文件版本号已存在！");
        }
        this.updateById(entity);
        // 添加历史记录
        FileHistoryDTO fileHistory = new FileHistoryDTO();
        fileHistory.setFileId(entity.getId());
        fileHistory.setOperationType("文件修订");
        fileHistoryService.addFileHistory(fileHistory);
        return fileInfoUpdateDTO;
    }

    @Override
    public boolean checkFileDownloadPermission(Integer fileInfoId) {
        UserInfoDTO user = userContext.getSecureUser();

        FileInfo fileInfo = baseMapper.selectById(fileInfoId);
        if (fileInfo == null) {
            throw new BaseKnownException("文件不存在");
        }
        List<ValidSetting> setting = fileTypeService.getRootSetting(fileInfo.getFileTypeId(), CommonConstants.DOWNLOAD_VALID_TYPE);
        //如果没有设置下载审批，直接返回不需要审批
        if (CollectionUtil.isEmpty(setting)) {
            return false;
        }
        //如果设置了下载审批，检查当前用户是否有审批记录如果有通过的，就返回false，如果没有返回true
        return !validationService.existsDownLoadValidation(fileInfoId, user.getId());
    }


    /**
     * 清理过期文件
     */
    @Override
    public void deleteOverTimeFile() {
        DateTime date = DateUtil.offsetDay(DateUtil.date(), -30);
        List<FileInfo> list = baseMapper.selectList(new LambdaQueryWrapper<FileInfo>()
                .le(FileInfo::getUpdateTime, date)
                .in(FileInfo::getFileStates,
                        FileStatesConstants.DELETE,
                        FileStatesConstants.DRAFT_DELETE));
        for (FileInfo fileInfo : list) {
            fileInfo.setDelFlag(CommonConstants.DELETED_FLAG);
            baseMapper.updateById(fileInfo);
        }
    }


    /**
     * 文件关联标签
     *
     * @param fileInfo 文件信息
     * @param tagIds   入参信息
     */
    private void associatedTags(FileInfo fileInfo, List<Integer> tagIds) {
        fileRelevancyTagService.remove(new LambdaQueryWrapper<FileRelevancyTag>().eq(FileRelevancyTag::getFileId, fileInfo.getId()));
        List<FileRelevancyTag> fileRelevancyTags = new ArrayList<>();
        for (Integer tagId : tagIds) {
            FileRelevancyTag fileRelevancyTag = new FileRelevancyTag();
            fileRelevancyTag.setFileId(fileInfo.getId());
            fileRelevancyTag.setTagId(tagId);
            fileRelevancyTags.add(fileRelevancyTag);
        }
        fileRelevancyTagService.saveBatch(fileRelevancyTags);
    }

    /**
     * 查询文件信息
     *
     * @param queryDTO 查询对象
     * @return 文件列表
     */
    @Override
    public Page<FileInfoResultDTO> listFile(FileQueryDTO queryDTO) {
        fixQuery(queryDTO);
        Page<FileInfoResultDTO> result = Page.empty();
        if (CollectionUtil.isEmpty(queryDTO.getFileTypeIdList())) {
            return result;
        } else {
            result = PageHelperUtil.getPage(baseMapper::getList, queryDTO);;
        }
        fixResult(result.getContent());
        //只查询文件夹直接归属的文件，需要添加文件夹信息
        if (queryDTO.getQueryDirect()) {
            List<FileTypeTreeQueryDTO> tree = fileTypeService.queryUserFileTypeTree();
            if (CollectionUtil.isEmpty(tree)) {
                return result;
            }
            List<FileTypeTreeQueryDTO> childTypes;
            if (queryDTO.getFileType() == 0) {
                childTypes = tree;
            } else {
                childTypes = fileTypeService.getByIdInTree(tree, queryDTO.getFileType()).getChildren();
            }
            log.info("查询文件列表，文件类型：" + childTypes);
            if (CollectionUtil.isNotEmpty(childTypes)) {
                List<FileInfoResultDTO> contentList = new ArrayList<>(result.getContent()); // 将结果内容转换为可修改的列表
                for (FileTypeTreeQueryDTO fileType : childTypes) {
                    FileInfoResultDTO resultDTO = new FileInfoResultDTO();
                    resultDTO.setId(fileType.getId());
                    resultDTO.setFileTypeIdList(fileType.getFileTypeIdList());
                    resultDTO.setTypeRoute(fileType.getTypeRoute());
                    resultDTO.setParentId(fileType.getParentId());
                    resultDTO.setType("文件夹");
                    resultDTO.setFileName(fileType.getName());
                    resultDTO.setFileNo(fileType.getCode());
                    // 将文件夹信息插入到结果列表的开头
                    contentList.add(0, resultDTO);
                }
                log.info("查询文件列表，文件夹信息：" + JSONObject.toJSON(contentList));
                // 将修改后的内容设置回分页结果中
                result =  new PageImpl(contentList, result.getPageable(), result.getTotalElements());
            }
        }
        log.info("查询文件列表结果：" + JSONObject.toJSON(result));
        return result;
    }

    @Override
    public void sendOverTimeMessage() {
        //查询所有过期的文件，并且没发送过消息的
        List<FileInfo> files = this.baseMapper.selectList(new LambdaQueryWrapper<FileInfo>()
                .gt(FileInfo::getOverdueDate, DateUtil.date())
                .ne(FileInfo::getOverTimeMessageSend, 1)
                .eq(FileInfo::getDelFlag, CommonConstants.UNDELETED_FLAG));
        //发送消息，并且打标记
        for (FileInfo file : files) {
            file.setOverTimeMessageSend(1);
            updateById(file);
            FileType type = fileTypeService.getById(file.getFileTypeId());
            MessageCreateDTO messageCreateDTO = new MessageCreateDTO();
            messageCreateDTO.setTitle("文件过期失效");
            messageCreateDTO.setContent(String.format("%s中，%s %s %s 已经过期失效，请及时处理",
                    type.getName(), file.getFileNo(), file.getFileName(), file.getFileVersion()));
            ArrayList<Integer> users = new ArrayList<>();
            if (Objects.nonNull(file.getCreatorId())) {
                users.add(file.getCreatorId());
            }
            if (Objects.nonNull(file.getUpdaterId())) {
                users.add(file.getUpdaterId());
            }
            if (CollectionUtil.isEmpty(users)) {
                continue;
            }
            messageCreateDTO.setUserIds(users);
            messageClient.createMessage(messageCreateDTO);
        }
    }

    @Override
    public void sendToBeOverTimeMessage() {
        //查询所有过期的文件，并且没发送过消息的
        List<FileInfo> files = this.baseMapper.selectList(new LambdaQueryWrapper<FileInfo>()
                .gt(FileInfo::getOverdueDate, DateUtil.offsetDay(DateUtil.date(), 30))
                .ne(FileInfo::getWarnMessageSend, 1)
                .eq(FileInfo::getDelFlag, CommonConstants.UNDELETED_FLAG));
        //发送消息，并且打标记
        for (FileInfo file : files) {
            file.setWarnMessageSend(1);
            updateById(file);
            FileType type = fileTypeService.getById(file.getFileTypeId());
            MessageCreateDTO messageCreateDTO = new MessageCreateDTO();
            messageCreateDTO.setTitle("文件即将过期失效");
            messageCreateDTO.setContent(String.format("%s中，%s %s %s 将于30天后失效，请及时处理",
                    type.getName(), file.getFileNo(), file.getFileName(), file.getFileVersion()));
            ArrayList<Integer> users = new ArrayList<>();
            if (Objects.nonNull(file.getCreatorId())) {
                users.add(file.getCreatorId());
            }
            if (Objects.nonNull(file.getUpdaterId())) {
                users.add(file.getUpdaterId());
            }
            if (CollectionUtil.isEmpty(users)) {
                continue;
            }
            messageCreateDTO.setUserIds(users);
            messageClient.createMessage(messageCreateDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void raiseDownloadValidation(Integer fileInfoId, String comment) {
        FileInfo fileInfo = baseMapper.selectById(fileInfoId);
        if (fileInfo == null) {
            throw new BaseKnownException("文件不存在");
        }
        //看看需要审批不
        List<ValidSetting> setting = fileTypeService.getRootSetting(fileInfo.getFileTypeId(), CommonConstants.DOWNLOAD_VALID_TYPE);
        if (CollectionUtil.isEmpty(setting)) {
            throw new BaseKnownException("找不到下载设置，文件下载不需要申请，请检查业务配置");
        }
        startValidate(fileInfo, setting, "下载审批");
    }

    @Override
    public FileInfoDTO getDetail(Integer fileInfoId) {
        FileInfo entity = getById(fileInfoId);
        if (entity == null) {
            return null;
        }
        //获取标签关联信息
        List<Integer> tagIds = tagMapper.selectList(new LambdaQueryWrapper<FileRelevancyTag>()
                        .eq(FileRelevancyTag::getFileId, entity.getId()))
                .stream()
                .map(FileRelevancyTag::getTagId)
                .collect(Collectors.toList());
        FileInfoDTO result = DtoMapper.convert(entity, FileInfoDTO.class);
        ResultVO<List<FileDTO>> query = fileClient.getFileDetailBatch(Collections.singletonList(entity.getFileIds()));
        if (query.isSuccess() && CollectionUtil.isNotEmpty(query.getData())) {
            result.setFileExtension(query.getData().get(0).getFileExtend());
            result.setOriginFileName(query.getData().get(0).getFileFullName());
        }
        if (tagIds.size() > 0) {
            List<FileTag> tags = fileTagMapper.selectBatchIds(tagIds);
            result.setTagNames(tags.stream().map(FileTag::getTagName).collect(Collectors.toList()));
        }
        result.setFileTagId(tagIds);
        //获取所有用户信息
        List<UserInfoDTO> allUser = this.getBaseMapper().getAllUserInfo();
        result.setFileAuthorNames(allUser.stream()
                .filter(t -> Arrays.stream(entity.getFileAuthors().split(","))
                        .filter(Strings::isNotBlank)
                        .anyMatch(p -> (Integer.parseInt(p)) == (t.getId())))
                .map(UserInfoDTO::getUserName)
                .collect(Collectors.toList()));
        List<FileTypeTreeQueryDTO> tree = fileTypeService.queryFileTypeTree(new FileTypeQueryDTO());
        FileTypeTreeQueryDTO node = fileTypeService.getByIdInTree(tree, entity.getFileTypeId());
        result.setFileAuthorIds(Arrays.stream(entity.getFileAuthors().split(","))
                .filter(Strings::isNotBlank)
                .map(Integer::parseInt)
                .collect(Collectors.toList()));
        if (node != null) {
            result.setFileTypePath(node.getTypeRoute());
            result.setFileTypeIdList(node.getFileTypeIdList());
        }
        return result;
    }


    @Override
    public synchronized void downloadAddWatermark(HttpServletRequest request, HttpServletResponse response, Integer fileId) {
        // 获取文控系统存储的文件名
        FileInfo fileInfo = this.baseMapper.selectById(fileId);
        ResultVO<List<FileDTO>> result = fileClient.getFileDetailBatch(Collections.singletonList(fileInfo.getFileIds()));
        if (!result.isSuccess()) {
            throw new BaseKnownException("文件信息查询异常，请检查输入信息。" + result.getMessage());
        }
        FileDTO fileDTO = result.getData().stream().findFirst().orElseThrow(() -> new BaseKnownException("找不到文件，请检查文件id信息"));

        String filePath = fileDTO.getFilePath();
        String fileName = String.format("%s_%s_%s.pdf", fileInfo.getFileNo(), fileInfo.getFileName(), fileInfo.getFileVersion());
        // 获取插入内容
        List<String> list = selectApproveDetail(fileId);

        UserInfoDTO user = userContext.getSecureUser();

        // 获取水印内容
        String waterMaker = String.format("%s-%s-%s-%s", getDepartmentName(user), user.getUserAccount(), user.getUserName(),
                getCurrentDate());

        log.info("获取水印{}", waterMaker);
        File file = new File(filePath);
        if (!file.exists()) {
            ResultVO<FileExcelExportDto> fileResult = fileClient.downloadFileWithResult(fileInfo.getFileIds());
            if (!fileResult.isSuccess()) {
                throw new BaseKnownException("文件下载失败，请检查文件存储路径");
            }
            byte[] bytes = Base64.getDecoder().decode(fileResult.getData().getBody());
            filePath = CommonConstants.TEMP_FILE;
            try {
                creatFile(filePath);
                FileUtil.writeBytes(filePath, bytes);
            } catch (IOException e) {
                throw new BaseKnownException("写入文件异常：" + e.getMessage());
            }
        }
        creatFile(CommonConstants.ADD_PDF_PATH );
        creatFile(CommonConstants.WATER_PDF_PATH );
        creatFile(CommonConstants.WORD_PDF_PATH );
        String downloadPath = CommonConstants.WATER_PDF_PATH ;
        // 判断是否为pdf文件
        if ("pdf".equals(fileDTO.getFileExtend())) {
            // 添加页面
            if (CollectionUtils.isNotEmpty(list)) {
                WatermarkUtils.addPdfHeader(filePath, CommonConstants.ADD_PDF_PATH , list);
                WatermarkUtils.addWaterMark2(CommonConstants.ADD_PDF_PATH ,
                        CommonConstants.WATER_PDF_PATH , waterMaker);
            } else {
                WatermarkUtils.addWaterMark2(filePath, CommonConstants.WATER_PDF_PATH , waterMaker);
            }
        } else if ("docx".equals(fileDTO.getFileExtend()) || "doc".equals(fileDTO.getFileExtend())) {
            try {
                WatermarkUtils.doc2pdf(filePath, CommonConstants.WORD_PDF_PATH );
            } catch (Exception e) {
                throw new BaseKnownException("转换pdf异常。" + e.getMessage());
            }
            if (CollectionUtils.isNotEmpty(list)) {
                WatermarkUtils.addPdfHeader(CommonConstants.WORD_PDF_PATH , CommonConstants.ADD_PDF_PATH , list);
                WatermarkUtils.addWaterMark2(CommonConstants.ADD_PDF_PATH , CommonConstants.WATER_PDF_PATH , waterMaker);
            } else {
                WatermarkUtils.addWaterMark2(CommonConstants.WORD_PDF_PATH , CommonConstants.WATER_PDF_PATH , waterMaker);
            }
        } else {

            fileName = String.format("%s_%s_%s.%s", fileInfo.getFileNo(), fileInfo.getFileName(), fileInfo.getFileVersion(), fileDTO.getFileExtend());
            downloadPath = filePath;
        }
        response.setContentType("application/force-download;charset=UTF-8");
        final String userAgent = request.getHeader("USER-AGENT");
        try {
            if (StringUtils.contains(userAgent, "MSIE") || StringUtils.contains(userAgent, "Trident") || StringUtils.contains(userAgent, "Edge")) {// IE浏览器
                fileName = URLEncoder.encode(fileName, "UTF8");
            } else if (StringUtils.contains(userAgent, "Mozilla")) {// google,火狐浏览器
                fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            } else {
                fileName = URLEncoder.encode(fileName, "UTF8");// 其他浏览器
            }
            response.setHeader("Content-disposition", "attachment; filename=\"" + fileName + "\"");
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        InputStream in = null;
        OutputStream out = null;
        try {

            //获取要下载的文件输入流
            in = Files.newInputStream(Paths.get(downloadPath));
            int len;
            //创建数据缓冲区
            byte[] buffer = new byte[1024];
            //通过response对象获取outputStream流
            out = response.getOutputStream();
            //将FileInputStream流写入到buffer缓冲区
            while ((len = in.read(buffer)) > 0) {
                //使用OutputStream将缓冲区的数据输出到浏览器
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (out != null)
                    out.close();
                if (in != null)
                    in.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        FileHistoryDTO fileHistoryDTO = new FileHistoryDTO();
        fileHistoryDTO.setFileId(fileInfo.getId());
        fileHistoryDTO.setOperationType("下载");
        fileHistoryService.addFileHistory(fileHistoryDTO);
        log.info("------downloadAddWatermark()结束-----");
    }

    private String getCurrentDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return formatter.format(LocalDateTime.now());
    }

    private List<String> selectApproveDetail(Integer fileId) {
        return Collections.emptyList();
    }

    /**
     * 创建文件
     *
     * @param filePath 文件存储路径
     */
    private void creatFile(String filePath) {
        File file = new File(filePath);
        File fileParent = file.getParentFile();//返回的是File类型,可以调用exsit()等方法
        if (!fileParent.exists()) {
            fileParent.mkdirs();// 能创建多级目录
        }
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            log.info("文件创建成功！");
        }
    }


    private void fixResult(List<FileInfoResultDTO> result) {
        if (CollectionUtil.isEmpty(result)) {
            return;
        }
        //获取标签关联信息
        List<FileRelevancyTag> tags = tagMapper.selectList(new LambdaQueryWrapper<FileRelevancyTag>()
                .in(FileRelevancyTag::getFileId, result.stream()
                        .map(FileInfoResultDTO::getId).collect(Collectors.toList())));
        //获取所有标签信息
        List<FileTag> allTags = fileTagMapper.selectList(new LambdaQueryWrapper<>());
        //获取所有用户信息
        List<UserInfoDTO> allUser = this.getBaseMapper().getAllUserInfo();
        for (FileInfoResultDTO dto : result) {
            //设置tagid列表
            dto.setFileTagId(tags.stream()
                    .filter(t -> t.getFileId().equals(dto.getId()))
                    .map(FileRelevancyTag::getTagId)
                    .collect(Collectors.toList()));
            //根据tagid列表填充tag信息
            dto.setFileTagDTOList(allTags.stream()
                    .filter(t -> dto.getFileTagId().contains(t.getId()))
                    .map(t -> DtoMapper.convert(t, FileTagDTO.class))
                    .collect(Collectors.toList()));
            //根据作者id信息填充作者信息
            if (Strings.isNotBlank(dto.getFileAuthorIds())) {
                try {
                    dto.setFileAuthorsName(allUser.stream()
                            .filter(t -> Arrays.stream(dto.getFileAuthorIds().split(","))
                                    .anyMatch(p -> (Integer.parseInt(p)) == (t.getId())))
                            .map(UserInfoDTO::getUserName)
                            .collect(Collectors.toList()));
                } catch (NumberFormatException ex) {
                    log.warn("数据库文件作者信息有异常字符导致转换异常，{}", ex.getMessage());
                }
            }
        }
    }

    private void fixQuery(FileQueryDTO queryDTO) {
        UserInfoDTO secureUser = userContext.getSecureUser();
        queryDTO.setUserId(secureUser.getId());
        List<Integer> typeIds;
        if (secureUser.getUserAccount().equals("admin")) {
            typeIds = fileTypeService.getAllChildTypeId(queryDTO.getFileType());
        } else {
            Integer departmentId = secureUser.getDepartmentId();
            if (departmentId == null) {
                throw new BaseKnownException("找不到用户部门信息，请检查");
            }
            //找到用户可以查看的文件类型id列表
            typeIds = fileTypeService.getDepartmentTypes(departmentId);
        }

        queryDTO.setFileTypeIdList(null);
        if (queryDTO.getQueryType().equals("收藏夹")) {
            queryDTO.setQueryDirect(false);
            queryDTO.setFileType(null);
            queryDTO.setFileTypeIdList(typeIds);
        } else if (queryDTO.getQueryType().equals("回收站")) {
            queryDTO.setQueryDirect(false);
            queryDTO.setFileType(null);
            queryDTO.setFileTypeIdList(typeIds);
        } else if (queryDTO.getQueryType().equals("文件类型")) {
            if (Strings.isBlank(queryDTO.getFileName())) {
                queryDTO.setQueryDirect(true);
                if (queryDTO.getFileType() == null) {
                    throw new BaseKnownException("文件类型需要传递");
                }
                Set<Integer> types = CollectionUtil.intersectionDistinct(Collections.singletonList(queryDTO.getFileType()),
                        typeIds);
                queryDTO.setFileTypeIdList(new ArrayList<>(types));
            } else {
                queryDTO.setQueryDirect(false);
                List<Integer> allChildTypeId = fileTypeService.getAllChildTypeId(queryDTO.getFileType());
                queryDTO.setFileTypeIdList(new ArrayList<>(CollectionUtil.intersectionDistinct(allChildTypeId,
                        typeIds)));
            }
        } else if (queryDTO.getQueryType().equals("草稿箱")) {
            queryDTO.setQueryDirect(false);
            queryDTO.setFileType(null);
            queryDTO.setFileTypeIdList(typeIds);
        } else {
            throw new BaseKnownException("不存在的查询类型");
        }
    }


    /**
     * 发起审批
     *
     * @param entity      文件
     * @param setting     审批配置
     * @param requestType 审批类型
     */
    private void startValidate(FileInfo entity, List<ValidSetting> setting, String requestType) {
        // 添加审批流
        RequestDTO requestDTO = new RequestDTO();
        requestDTO.setBusinessKey("");
        requestDTO.setValidtor1(setting.get(0).getUserInfo().stream().map(ValidUserInfo::getUserId).collect(Collectors.toList()));
        requestDTO.setN1(setting.get(0).getUserPass());
        if (setting.size() > 1) {
            requestDTO.setValidtor2(setting.get(1).getUserInfo().stream().map(ValidUserInfo::getUserId).collect(Collectors.toList()));
            requestDTO.setN2(setting.get(1).getUserPass());
        }
        if (setting.size() > 2) {
            requestDTO.setValidtor3(setting.get(2).getUserInfo().stream().map(ValidUserInfo::getUserId).collect(Collectors.toList()));
            requestDTO.setN3(setting.get(2).getUserPass());
        }
        if (setting.size() > 3) {
            requestDTO.setValidtor4(setting.get(3).getUserInfo().stream().map(ValidUserInfo::getUserId).collect(Collectors.toList()));
            requestDTO.setN4(setting.get(3).getUserPass());
        }
        if (setting.size() > 4) {
            requestDTO.setValidtor5(setting.get(4).getUserInfo().stream().map(ValidUserInfo::getUserId).collect(Collectors.toList()));
            requestDTO.setN5(setting.get(4).getUserPass());
        }
        requestDTO.setLevel(setting.size());
        requestDTO.setFileInfoDTO(getDetail(entity.getId()));
        requestDTO.setRequestType(requestType);
        validationService.createRequest(requestDTO);
    }

    /**
     * 获取人员第二级别的部门名称
     *
     * @return 部门名称
     */
    private String getDepartmentName(UserInfoDTO userInfoDTO) {

        String result = userInfoDTO.getDepartmentName();
        ResultVO<List<DepartmentDTO>> departmentList = departmentClient.getDepartmentList();
        if (!departmentList.isSuccess()) {
            log.info("调用部门接口错误");
            return result;
        }
        List<DepartmentDTO> allDepartment = departmentList.getData();
        DepartmentDTO departmentDTO = allDepartment.stream()
                .filter(t -> t.getId().equals(userInfoDTO.getDepartmentId()))
                .findFirst()
                .orElse(null);
        if (departmentDTO == null) {
            log.info("部门信息错误。找不到对应的部门数据");
            return result;
        }

        if (departmentDTO.getParentId() == 0) {
            return result;
        }
        do {
            DepartmentDTO finalDepartmentDTO = departmentDTO;
            DepartmentDTO parentDepartment = allDepartment.stream()
                    .filter(t -> t.getId().equals(finalDepartmentDTO.getParentId()))
                    .findFirst()
                    .orElse(null);
            if (parentDepartment == null) {
                log.info("错误的部门设置，找不到父级部门信息，直接返回");
                break;
            } else {
                if (parentDepartment.getParentId() == 0) {
                    break;
                } else {
                    departmentDTO = parentDepartment;
                    result = parentDepartment.getDepartmentName();
                }
            }
        } while (true);

        return result;

    }

    @Override
    public void downloadAddWatermarksAsZip(HttpServletRequest request, HttpServletResponse response, List<Integer> fileIds) {
        log.info("------downloadAddWatermarksAsZip()开始-----");

        if (fileIds == null || fileIds.isEmpty()) {
            throw new BaseKnownException("文件ID列表不能为空");
        }

        // 存储下载失败的文件信息
        List<String> failedFiles = new ArrayList<>();
        List<File> successFiles = new ArrayList<>();
        List<String> successFileNames = new ArrayList<>();

        UserInfoDTO user = userContext.getSecureUser();
        String waterMaker = String.format("%s-%s-%s-%s", getDepartmentName(user), user.getUserAccount(), user.getUserName(), getCurrentDate());

        // 创建临时目录
        String tempDir = createTempDirectory();
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists()) {
            tempDirFile.mkdirs();
        }

        try {
            // 处理每个文件
            for (Integer fileId : fileIds) {
                try {
                    File processedFile = processFileWithWatermark(fileId, waterMaker, tempDir);
                    if (processedFile != null && processedFile.exists()) {
                        successFiles.add(processedFile);
                        // 获取文件信息用于命名
                        FileInfo fileInfo = this.baseMapper.selectById(fileId);
                        String fileName = String.format("%s_%s_%s.pdf", fileInfo.getFileNo(), fileInfo.getFileName(), fileInfo.getFileVersion());
                        successFileNames.add(fileName);

                        // 记录下载历史
                        FileHistoryDTO fileHistoryDTO = new FileHistoryDTO();
                        fileHistoryDTO.setFileId(fileInfo.getId());
                        fileHistoryDTO.setOperationType("批量下载");
                        fileHistoryService.addFileHistory(fileHistoryDTO);
                    } else {
                        failedFiles.add("文件ID: " + fileId + " - 处理失败：生成的文件不存在");
                    }
                } catch (Exception e) {
                    log.error("处理文件ID: {} 时发生错误: {}", fileId, e.getMessage(), e);
                    failedFiles.add("文件ID: " + fileId + " - 处理失败：" + e.getMessage());
                }
            }

            // 创建ZIP文件
            String zipFileName = "批量下载文件_" + getCurrentDate().replaceAll("[: -]", "") + ".zip";
            createZipResponse(response, successFiles, successFileNames, failedFiles, zipFileName);

        } finally {
            // 清理临时文件
            cleanupTempFiles(tempDirFile);
        }

        log.info("------downloadAddWatermarksAsZip()结束-----");
    }

    /**
     * 处理单个文件并添加水印
     */
    private File processFileWithWatermark(Integer fileId, String waterMaker, String tempDir) {
        try {
            // 获取文控系统存储的文件名
            FileInfo fileInfo = this.baseMapper.selectById(fileId);
            if (fileInfo == null) {
                throw new BaseKnownException("找不到文件信息，文件ID: " + fileId);
            }

            ResultVO<List<FileDTO>> result = fileClient.getFileDetailBatch(Collections.singletonList(fileInfo.getFileIds()));
            if (!result.isSuccess()) {
                throw new BaseKnownException("文件信息查询异常: " + result.getMessage());
            }

            FileDTO fileDTO = result.getData().stream().findFirst()
                    .orElseThrow(() -> new BaseKnownException("找不到文件详情，文件ID: " + fileId));

            String filePath = fileDTO.getFilePath();

            // 如果文件不存在，从文件服务下载
            File file = new File(filePath);
            if (!file.exists()) {
                ResultVO<FileExcelExportDto> fileResult = fileClient.downloadFileWithResult(fileInfo.getFileIds());
                if (!fileResult.isSuccess()) {
                    throw new BaseKnownException("文件下载失败: " + fileResult.getMessage());
                }
                byte[] bytes = Base64.getDecoder().decode(fileResult.getData().getBody());
                filePath = createSafeFilePath(tempDir, "original_" + fileId + "." + fileDTO.getFileExtend());
                writeFileBytes(filePath, bytes);
            }

            // 创建输出文件路径
            String outputPath = createSafeFilePath(tempDir, "watermarked_" + fileId + ".pdf");

            // 获取插入内容
            List<String> list = selectApproveDetail(fileId);

            // 根据文件类型处理
            if ("pdf".equals(fileDTO.getFileExtend())) {
                // PDF文件处理
                if (CollectionUtils.isNotEmpty(list)) {
                    String addPdfPath = createSafeFilePath(tempDir, "add_" + fileId + ".pdf");
                    WatermarkUtils.addPdfHeader(filePath, addPdfPath, list);
                    WatermarkUtils.addWaterMark2(addPdfPath, outputPath, waterMaker);
                } else {
                    WatermarkUtils.addWaterMark2(filePath, outputPath, waterMaker);
                }
            } else if ("docx".equals(fileDTO.getFileExtend()) || "doc".equals(fileDTO.getFileExtend())) {
                // Word文件处理
                String wordPdfPath = createSafeFilePath(tempDir, "word_" + fileId + ".pdf");
                WatermarkUtils.doc2pdf(filePath, wordPdfPath);
                if (CollectionUtils.isNotEmpty(list)) {
                    String addPdfPath = createSafeFilePath(tempDir, "add_" + fileId + ".pdf");
                    WatermarkUtils.addPdfHeader(wordPdfPath, addPdfPath, list);
                    WatermarkUtils.addWaterMark2(addPdfPath, outputPath, waterMaker);
                } else {
                    WatermarkUtils.addWaterMark2(wordPdfPath, outputPath, waterMaker);
                }
            } else {
                throw new BaseKnownException("不支持的文件格式: " + fileDTO.getFileExtend());
            }

            return new File(outputPath);

        } catch (Exception e) {
            log.error("处理文件ID: {} 时发生错误", fileId, e);
            throw new RuntimeException("处理文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建ZIP响应
     */
    private void createZipResponse(HttpServletResponse response, List<File> successFiles,
                                 List<String> successFileNames, List<String> failedFiles, String zipFileName) {
        try {
            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(zipFileName, StandardCharsets.UTF_8.toString()));

            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                // 添加成功的文件到ZIP
                for (int i = 0; i < successFiles.size(); i++) {
                    File file = successFiles.get(i);
                    String fileName = successFileNames.get(i);

                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zipOut.putNextEntry(zipEntry);

                    try (FileInputStream fis = new FileInputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = fis.read(buffer)) >= 0) {
                            zipOut.write(buffer, 0, length);
                        }
                    }
                    zipOut.closeEntry();
                }

                // 如果有失败的文件，创建错误信息文件
                if (!failedFiles.isEmpty()) {
                    ZipEntry errorEntry = new ZipEntry("下载失败文件列表.txt");
                    zipOut.putNextEntry(errorEntry);

                    StringBuilder errorContent = new StringBuilder();
                    errorContent.append("以下文件下载失败：\n\n");
                    for (String error : failedFiles) {
                        errorContent.append(error).append("\n");
                    }
                    errorContent.append("\n生成时间: ").append(getCurrentDate());

                    zipOut.write(errorContent.toString().getBytes(StandardCharsets.UTF_8));
                    zipOut.closeEntry();
                }

                zipOut.finish();
            }

            log.info("ZIP文件创建完成，包含 {} 个成功文件，{} 个失败文件", successFiles.size(), failedFiles.size());

        } catch (Exception e) {
            log.error("创建ZIP响应时发生错误", e);
            throw new BaseKnownException("创建ZIP文件失败: " + e.getMessage());
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(File tempDir) {
        try {
            if (tempDir.exists()) {
                deleteDirectory(tempDir);
                log.info("临时文件清理完成: {}", tempDir.getAbsolutePath());
            }
        } catch (Exception e) {
            log.warn("清理临时文件时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }

    /**
     * 创建安全的临时目录
     */
    private String createTempDirectory() {
        try {
            // 确保基础临时目录存在
            String baseTempDir = CommonConstants.BATH_FILE;
            File baseTempDirFile = new File(baseTempDir);

            // 如果基础目录是文件，删除它
            if (baseTempDirFile.exists() && baseTempDirFile.isFile()) {
                baseTempDirFile.delete();
            }

            // 创建基础目录
            if (!baseTempDirFile.exists()) {
                baseTempDirFile.mkdirs();
            }

            // 创建唯一的批量下载目录
            String uniqueDirName = "batch_download_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
            return baseTempDir + File.separator + uniqueDirName;

        } catch (Exception e) {
            log.error("创建临时目录失败", e);
            // 使用系统临时目录作为备选
            return System.getProperty("java.io.tmpdir") + File.separator + "batch_download_" + System.currentTimeMillis();
        }
    }

    /**
     * 创建安全的文件路径
     */
    private String createSafeFilePath(String directory, String fileName) {
        try {
            File dir = new File(directory);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            return directory + File.separator + fileName;
        } catch (Exception e) {
            log.error("创建文件路径失败: directory={}, fileName={}", directory, fileName, e);
            throw new BaseKnownException("创建文件路径失败: " + e.getMessage());
        }
    }

    /**
     * 安全写入文件字节
     */
    private void writeFileBytes(String filePath, byte[] bytes) {
        try {
            File file = new File(filePath);
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            FileUtil.writeBytes(filePath, bytes);
        } catch (Exception e) {
            log.error("写入文件失败: filePath={}", filePath, e);
            throw new BaseKnownException("写入文件失败: " + e.getMessage());
        }
    }

}
