package com.hvisions.cpg.file.info.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.cpg.file.common.fileinfo.FileInfoDTO;
import com.hvisions.cpg.file.common.fileinfo.FileInfoResultDTO;
import com.hvisions.cpg.file.dto.FileQueryDTO;
import com.hvisions.cpg.file.info.entity.FileInfo;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 文件基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface FileInfoService extends IService<FileInfo> {

    /**
     * 添加文件基本信息
     *
     * @param fileInfo 文件信息
     * @return 文件信息
     */
    FileInfoDTO addFileInfo(FileInfoDTO fileInfo);

    /**
     * 修改草稿后重新发起审批
     *
     * @param fileId fileId
     */
    void requestAgain(Integer fileId);

    /**
     * 修改文件基本信息
     *
     * @param fileInfo 文件信息
     * @return 文件信息
     */
    FileInfoDTO updateFileMessage(FileInfoDTO fileInfo);

    /**
     * 检查是否需要下载审批
     *
     * @param fileInfoId 文件id
     * @return 是否需要审批
     */
    boolean checkFileDownloadPermission(Integer fileInfoId);

    /**
     * 清理过期文件
     */
    void deleteOverTimeFile();

    /**
     * 查询文件信息
     *
     * @param queryDTO 查询对象
     * @return 文件列表
     */
    Page<FileInfoResultDTO> listFile(FileQueryDTO queryDTO);

    void sendOverTimeMessage();

    void sendToBeOverTimeMessage();

    void raiseDownloadValidation(Integer fileInfoId, String comment);

    FileInfoDTO getDetail(Integer fileInfoId);

    void downloadAddWatermark(HttpServletRequest request, HttpServletResponse response, Integer fileId);

    /**
     * 批量下载文件添加水印并打包成ZIP
     *
     * @param request HttpServletRequest
     * @param response HttpServletResponse
     * @param fileIds 文件ID列表
     */
    void downloadAddWatermarksAsZip(HttpServletRequest request, HttpServletResponse response, List<Integer> fileIds);
}
