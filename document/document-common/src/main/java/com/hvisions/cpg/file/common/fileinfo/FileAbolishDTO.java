package com.hvisions.cpg.file.common.fileinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Title: FileAbolish
 * Description: 文件废止申请DTO
 * Company: www.h-visions.com/
 * create date: 2022/9/13
 *
 * <AUTHOR> wb.fan
 * @version :1.0.0
 */
@Data
public class FileAbolishDTO {

    @NotEmpty(message = "文件ID不能为空")
    @ApiModelProperty(value = "文件ID")
    private Integer fileId;

    @ApiModelProperty(value = "部门审批人集合")
    private List<ApprovalDepDTO> depLeaderList;

    @ApiModelProperty(value = "审批人")
    private Integer approvalLeader;

    @NotEmpty(message = "申请人ID")
    @ApiModelProperty(value = "申请人ID")
    private Integer applicant;

    @ApiModelProperty(value = "废止理由")
    private String description;

    @ApiModelProperty(value = "废止文件")
    private String abolitionFiles;
}
