package com.hvisions.cpg.file.common.users;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
@ApiModel(description = "文控部门人员关系dto")
public class ControlDepartmentUserRelDTO{

    private Integer id;


    @ApiModelProperty(value="文控部门id")
    private Integer maintainId;

    @ApiModelProperty(value="部门名称")
    private String depName;

    @ApiModelProperty(value = "直属经理")
    private Boolean directManager;

    @ApiModelProperty(value="人员id")
    private Integer userId;

    @ApiModelProperty(value="人员名称")
    private String userName;

    @ApiModelProperty(value="人员账号")
    private String userAccount;

    @ApiModelProperty(value="人员状态")
    private Integer status;

    @ApiModelProperty(value = "文控部门经理")
    private Boolean departmentLeader;

    @ApiModelProperty(value = "文控人员")
    private Boolean controlLeader;

    @ApiModelProperty(value = "审批人")
    private Boolean approverLeader;
}
