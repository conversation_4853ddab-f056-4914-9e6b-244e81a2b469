package com.hvisions.cpg.file.common.collection;

import com.hvisions.cpg.file.common.fileinfo.ApprovalDepDTO;
import com.hvisions.cpg.file.common.tag.FileTagDTO;
import com.hvisions.cpg.file.common.type.FileTypeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * Title: FileInfoResultDTO
 * Description: 
 * Company: www.h-visions.com/
 * create date: 2022/9/14
 *
 * <AUTHOR> wb.fan
 * @version :1.0.0
 */
@Data
public class FileInfoCollectionResultDTO {
    @ApiModelProperty(value = "文件ID")
    private Integer id;

    @ApiModelProperty(value = "文件类型", hidden = true)
    private List<FileTypeDTO> fileTypeQueryList;

    @ApiModelProperty(value = "文件标签")
    private List<FileTagDTO> fileTagDTOList;

    @ApiModelProperty(value = "文件类型ID")
    private List<Integer> fileTypeId;

    @ApiModelProperty(value = "文件审批部门")
    private List<Integer> departmentId;

    @ApiModelProperty(value = "部门审批人集合")
    private List<ApprovalDepDTO> depApprovalList;

    @ApiModelProperty(value = "文件使用部门")
    private List<Integer> fileDepartmentIdList;

    @ApiModelProperty(value = "审批人")
    private Integer approvalLeader;

    @ApiModelProperty(value = "文件使用部门")
    private String fileDepartmentIds;

    @ApiModelProperty(value = "标签ID", hidden = true)
    private List<Integer> fileTagId;

    @ApiModelProperty(value = "文件编号")
    private String fileNo;

    @ApiModelProperty(value = "类别：1文件，2 文件类型")
    private String Type;

    @ApiModelProperty(value = "文件作者")
    private String fileAuthors;

    @ApiModelProperty(value = "文件作者ID")
    private List<Integer> fileAuthorIds;

    @ApiModelProperty(value = "文件作者姓名")
    private String fileAuthorsName;

    @NotBlank(message = "文件名称不能为空")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @NotBlank(message = "文件扩展名不能为空")
    @ApiModelProperty(value = "文件扩展名")
    private String fileExtension;

    @ApiModelProperty(value = "文件描述")
    private String fileDescription;

    @NotBlank(message = "文件路径不能为空")
    @ApiModelProperty(value = "文件上传ID")
    private Integer fileIds;

    @ApiModelProperty(value = "版本号", hidden = true)
    private String fileVersion;

    @ApiModelProperty(value = "文件大小", hidden = true)
    private String fileSize;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @NotBlank(message = "创建人不能为空")
    @ApiModelProperty(value = "作者")
    private String creatorId;

    @NotBlank(message = "提交状态不能为空")
    @ApiModelProperty(value = "0:保存,1:提交", hidden = true)
    private String submitState;

    @ApiModelProperty(value = "修改人")
    private String updaterId;

    @ApiModelProperty(value = "修改人")
    private String updaterName;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "文件状态")
    private String fileStates;

    @ApiModelProperty(value = "删除标识", hidden = true)
    private String delFlag;

    @ApiModelProperty(value = "到期时间")
    private Date overdueDate;

    @ApiModelProperty(value = "临期提前提醒天数")
    private Integer remindDays;

    @NotEmpty(message = "到期时间不能为空")
    @ApiModelProperty(value = "到期时间", hidden = true)
    private String overdueDateStr;

    @ApiModelProperty(value = "临期提醒日期")
    private Date remindDate;

    @ApiModelProperty(value = "收藏标识")
    private Boolean collectLogo;

    @ApiModelProperty(value = "文件类型")
    private List<Integer> fileTypeIdList;

    @ApiModelProperty(value = "父级类型Code")
    private String parTypeCode;
}
